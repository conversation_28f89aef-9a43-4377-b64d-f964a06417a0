import { Icon } from '@iconify/react/dist/iconify.js';
import React from 'react';
import { useNavigate } from 'react-router-dom';

const GoBack = ({title}) => {
    const navigate = useNavigate();

    return <h2 onClick={() => navigate(-1)} className="text-2xl text-sky-600 flex items-center my-5 gap-2 cursor-pointer"><Icon icon="tabler:arrow-left" className="" /> {title}</h2>;
};

export default GoBack;