import { Fragment, useEffect, useRef, useState } from "react";
import { Menu } from "@headlessui/react";
import { NavLink } from "react-router-dom";
import Icon from "@/components/ui/Icon";

const MenuDropdown = ({
  label = "Dropdown",
  wrapperClass = "inline-block",
  labelClass = "label-class-custom",
  children,
  classMenuItems = "mt-2 w-[220px]",
  items = [],
  classItem = "px-4 py-2",
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const ref = useRef(null);

  // useEffect(() => {
  //   const handleOutsideClick = (event) => {
  //     if (ref.current && !ref.current.contains(event.target)) {
  //       setIsOpen(false);
  //     }
  //   };
  //   document.addEventListener("click", handleOutsideClick);
  //   return () => {
  //     document.removeEventListener("click", handleOutsideClick);
  //   };
  // }, [ref]);

  return (
    <div
      className={`relative ${wrapperClass} group`}
      ref={ref}
      onMouseEnter={() => {
        console.log("mouse is here now");
        setIsOpen(true);
      }}
      onMouseLeave={() => setIsOpen(false)}
    >
      <Menu as="div" className={`block w-full ${className}`}>
        <>
          <Menu.Button className="block w-full">
            {/* <NavLink to={`/courses/${items?.id}`}> */}
              <div
                className={`${labelClass} flex items-center gap-1 group-hover:text-slate-900`}
              >
                {label}
                <Icon
                  icon="icon-park-outline:down"
                  className="text-xl transition-transform duration-200 group-hover:rotate-180"
                />
              </div>
            {/* </NavLink> */}
          </Menu.Button>

          <Menu.Items
            className={`absolute ltr:right-0 rtl:left-0 origin-top-right border border-slate-100
              rounded bg-white dark:bg-slate-800 dark:border-slate-700 shadow-dropdown z-[9999]
              ${classMenuItems} ${isOpen ? "" : "invisible opacity-0"} transition-all`}
          >
            <div>
              {children
                ? children
                : items?.sub_categories
                ? items.sub_categories.map((item, index) => (
                    <Menu.Item key={index}>
                      {({ active }) => (
                        <NavLink
                          to={`/courses/${items?.id}/${item?.id}`}
                          className={`block ${
                            active
                              ? "bg-slate-100 text-slate-900 dark:bg-slate-600 dark:text-slate-300"
                              : "text-slate-600 dark:text-slate-300"
                          } ${classItem}`}
                        >
                          {item.name}
                        </NavLink>
                      )}
                    </Menu.Item>
                  ))
                : items.map((item, index) => (
                    <Menu.Item key={index}>
                      {({ active }) => (
                        <NavLink
                          to={item.link}
                          className={`block ${
                            active
                              ? "bg-slate-100 text-slate-900 dark:bg-slate-600 dark:text-slate-300"
                              : "text-slate-600 dark:text-slate-300"
                          } ${classItem}`}
                        >
                          {item.name}
                        </NavLink>
                      )}
                    </Menu.Item>
                  ))}
            </div>
          </Menu.Items>
        </>
      </Menu>
    </div>
  );
};

export default MenuDropdown;

