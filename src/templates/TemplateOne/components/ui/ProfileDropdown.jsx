import { Menu, Transition } from "@headlessui/react";
import { Fragment } from "react";
import { NavLink } from "react-router-dom";
import Icon from "@/components/ui/Icon";
import logOutIcon from '@/assets/images/svg/logout.svg';

const ProfileDropdown = ({
  label = "Dropdown",
  wrapperClass = "inline-block",
  labelClass = "label-class-custom",
  children,
  classMenuItems = "mt-2 w-[220px]",
  items = [], // Default to an empty array
  classItem = "px-4 py-2",
  className = "",
  userImage = null,
  onLogout = () => {},
}) => {
  return (
    <div className={`relative ${wrapperClass}`}>
      <Menu as="div" className={`block w-full ${className}`}>
        {({ open }) => (
          <>
            <Menu.Button className="block w-full">
              <div className={`${labelClass} flex items-center gap-2 font-semibold text-sky-600`}>
                {userImage && (
                  <img
                    src={userImage}
                    alt="User"
                    className="w-10 h-10 rounded-full"
                  />
                )}
                {label}
                <Icon
                  icon="icon-park-outline:down"
                  className={`text-xl transition-transform duration-200 ${
                    open ? "rotate-180" : ""
                  }`}
                />
              </div>
            </Menu.Button>

            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items
                className={`absolute right-0 origin-top-left border border-slate-100 rounded bg-white dark:bg-slate-800 dark:border-slate-700 shadow-dropdown z-[9999] 
                ${classMenuItems}`}
              >
                <div>
                  {children
                    ? children
                    : Array.isArray(items) && items.length > 0 // Ensure items is an array
                    ? items.map((item, index) => (
                        <Menu.Item key={index}>
                          {({ active }) => (
                            <NavLink
                              to={item.link}
                              className={`block ${
                                active
                                  ? "bg-slate-100 text-slate-900 dark:bg-slate-600 dark:text-slate-300"
                                  : "text-slate-600 dark:text-slate-300"
                              } ${classItem}`}
                            >
                              <div className="flex items-center gap-2">
                                <img src={item.icon} alt="" />
                                <span>{item.name}</span>
                              </div>
                            </NavLink>
                          )}
                        </Menu.Item>
                      ))
                    : (
                      <p className="px-4 py-2 text-sm text-gray-500">
                        No items available
                      </p>
                    )}
                  {/* Logout Button */}
                  <Menu.Item>
                    {({ active }) => (
                      <div
                        onClick={onLogout}
                        className={`w-full flex items-center gap-2 text-left cursor-pointer ${
                          active
                            ? "bg-red-100 text-red-700 dark:bg-red-600"
                            : "text-red-500"
                        } px-3 py-2`}
                      >
                        <img src={logOutIcon} alt="" /> Logout
                      </div>
                    )}
                  </Menu.Item>
                </div>
              </Menu.Items>
            </Transition>
          </>
        )}
      </Menu>
    </div>
  );
};

export default ProfileDropdown;
