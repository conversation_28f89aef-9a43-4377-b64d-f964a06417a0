import React from "react";
import Card from "../../ui/Card";
import Dropdown from "../../ui/Dropdown";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Menu } from "@headlessui/react";
import GlobalFilter from "./GlobalFilter";
import { showEditModal } from "@/store/assignmentStore";

const BasicTablePage = ({
  tableHeaderExtra = null,
  title,
  createButton,
  createPage,
  editPage,
  actions = [],
  columns,
  changePage,
  data,
  filter,
  setFilter,
  currentPage,
  totalPages,
  submitForm,
  openCreateModal
}) => {



  return (
    <div className="shadow-lg">
      <Card noborder className=" overflow-y-auto">
        <div className="flex justify-between items-center mb-6 z-20 bg-white">
          <h4 className="card-title text-gray-700">{title}</h4>
          <div className="flex gap-2">
            {tableHeaderExtra}
            <GlobalFilter filter={filter} setFilter={setFilter} />
            {createButton && (
              <button
                className="btn btn-warning btn-sm"
                onClick={() => openCreateModal(true)}
              >
                {createButton}
              </button>
            )}
            {showEditModal && editPage}
          </div>
        </div>
        {data?.length > 0 ? (
          <table className="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
            <thead className="bg-slate-200 dark:bg-slate-700">
              <tr className="rounded bg-gray-200">
                {columns.map((column, i) => (
                  <th key={i} scope="col" className=" font-semibold py-4">
                    {column.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
              {data?.map((row, dataIndex) => (
                <tr
                  key={dataIndex}
                  className="hover:bg-gray-100"
                >
                  {columns.map(
                    (column, index) =>
                      column.field && (
                        <td key={index} className="table-td">
                          {row[column.field]}
                        </td>
                      )
                  )}
                  {actions.length > 0 && (
                    <td className="table-td ">
                      <Dropdown
                        classMenuItems="right-0 w-[140px] top-[110%] "
                        label={
                          <span className="text-xl text-center block w-full">
                            <Icon icon="heroicons-outline:dots-vertical" />
                          </span>
                        }
                      >
                        <div className="divide-y divide-slate-100 dark:divide-slate-800">
                          {actions.map((item, i) => (
                            <Menu.Item key={i}>
                              <div
                                className={`
                              w-full border-b border-b-gray-500 border-opacity-10 px-4 py-2 text-sm  last:mb-0 cursor-pointer 
                              first:rounded-t last:rounded-b flex space-x-2 items-center rtl:space-x-reverse `}
                                onClick={() => item.onClick(dataIndex)}
                              >
                                <span className="text-base">
                                  <Icon icon={item.icon} />
                                </span>
                                <span>{item.name} </span>
                              </div>
                            </Menu.Item>
                          ))}
                        </div>
                      </Dropdown>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          "No data found"
        )}
        {totalPages > 0 && (
          <Pagination
            totalPages={totalPages}
            currentPage={currentPage}
            handlePageChange={handlePageChange}
          />
        )}
      </Card>
    </div>
  );
};

export default BasicTablePage;
