import React from "react";
import useFooterType from "@/hooks/theme/useFooterType";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import googlePlay from "./googleplay.svg";
import appstore from "./appstore.svg";
import logo from "./logo.svg";
import useFetch from "@/hooks/useFetch";
import footerbg from "./footerbg.svg";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { ASSET_URL } from "@/config";
const Footer = ({ className = "custom-class", menuItems }) => {
  const { organization } = useSelector((state) => state.commonSlice);
  const { t } = useTranslation();

  // const {
  //   data: menuItems,
  //   isLoading,
  //   error,
  // } = useFetch({
  //   queryKey: "menu-list",
  //   endPoint: "menu-list",
  // });

  // const footerMenu = menuItems?.data?.filter((item) => item.is_footer == true)

  return (
    <div className="w-full">
      <div className="relative bg-gradient-to-t from-[#CAE5FE] to-[#E4F2FF]">
        <div className="container py-10">
          {/* <footer className={className + " " + footerClassName()}> */}
          <footer className="">
            <div className="px-6 py-4 relative">
              {/* <img
                src={footerbg}
                alt="Footer Background"
                className="absolute inset-0 w-[300px] h-auto object-cover opacity-50 mx-auto"
              /> */}
              <div className="relative grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 items-start gap-4 max-sm:gap-8">
                <div className="col-span-1 flex flex-col items-center md:items-start">
                  {organization?.footer_logo &&
                    <img
                      src={ASSET_URL + organization?.footer_logo}
                      alt="Logo"
                      className="w-auto h-20 mb-2"
                    />
                  }
                  { (organization?.facebook || organization?.linkedin || organization?.youtube || organization?.twitter || organization?.website) && 
                  <>
                  <h3 className="text-lg text-gray-700 mb-3">Follow Us On</h3>
                  <div className="flex space-x-4">
                    {organization?.facebook && 
                    <a
                      href={organization?.facebook}
                      target="_blank"
                      className="bg-white border border-sky-600 p-2 rounded group flex items-center justify-center"
                    >
                      <Icon
                        icon="ri:facebook-fill"
                        className="h-6 w-6 text-gray-600 group-hover:opacity-0 transition-opacity duration-300 ease-in-out"
                      />
                      <Icon
                        icon="logos:facebook"
                        className="h-6 w-6 text-gray-600 opacity-0 group-hover:opacity-100 absolute transition-opacity duration-300 ease-in-out"
                      />
                    </a>}
                    {organization?.linkedin && 
                    <a
                      href={organization?.linkedin}
                      target="_blank"
                      className="bg-white border border-sky-600 p-2 rounded group flex items-center justify-center"
                    >
                      <Icon
                        icon="fa-brands:linkedin"
                        className="h-6 w-6 text-gray-600 group-hover:opacity-0 transition-opacity duration-300 ease-in-out"
                      />
                      <Icon
                        icon="devicon:linkedin"
                        className="h-6 w-6 text-gray-600 opacity-0 group-hover:opacity-100 absolute transition-opacity duration-300 ease-in-out"
                      />
                    </a>}
                    {organization?.youtube && 
                    <a
                      href={organization?.youtube}
                      target="_blank"
                      className="bg-white border border-sky-600 p-2 rounded group flex items-center justify-center"
                    >
                      <Icon
                        icon="mingcute:youtube-fill"
                        className="h-6 w-6 text-gray-600 group-hover:opacity-0 transition-opacity duration-300 ease-in-out"
                      />
                      <Icon
                        icon="logos:youtube-icon"
                        className="h-7 w-6 text-gray-600 opacity-0 group-hover:opacity-100 absolute transition-opacity duration-300 ease-in-out"
                      />
                    </a>}
                    {organization?.twitter && 
                    <a
                      href={organization?.twitter}
                      target="_blank"
                      className="bg-white border border-sky-600 p-2 rounded group flex items-center justify-center"
                    >
                      <Icon
                        icon="arcticons:x-twitter"
                        className="h-7 w-6 text-gray-600 group-hover:opacity-0 transition-opacity duration-300 ease-in-out"
                      />
                      <Icon
                        icon="ri:x-circle-line"
                        className="h-7 w-6 text-gray-600 opacity-0 group-hover:opacity-100 absolute transition-opacity duration-300 ease-in-out"
                      />
                    </a>}
                    {organization?.website && 
                    <a
                      href={organization?.website}
                      target="_blank"
                      className="bg-white border border-sky-600 p-2 rounded group flex items-center justify-center"
                    >
                      <Icon
                        icon="bi:globe"
                        className="h-6 w-6 text-gray-600 group-hover:opacity-0 transition-opacity duration-300 ease-in-out"
                      />
                      <Icon
                        icon="logos:google"
                        className="h-7 w-6 text-gray-600 opacity-0 group-hover:opacity-100 absolute transition-opacity duration-300 ease-in-out"
                      />
                    </a>}
                  </div>
                  </>}
                </div>
                <div className="flex flex-col items-center md:items-start gap-3">
                  <h5 className="font-medium mb-2 text-xl text-gray-700">
                    Contact Us
                  </h5>
                  <div className="flex gap-2">
                    <Icon icon="bx:map" className="h-6 w-6 mt-1 text-sky-600 flex-shrink-0" />
                    <p className="items-center">
                      {/* asd fisunifun */}
                      {organization?.address ? organization?.address : "--"}
                    </p>
                  </div>
                  <p className="flex items-center">
                    <Icon
                      icon="mdi:phone"
                      className="h-6 w-6 mr-2 text-sky-600 flex-shrink-0"
                    />
                    {organization?.hotline_number}
                      {organization?.contact_no ? " | " + organization?.contact_no : ""}
                  </p>
                  <p className="flex items-center">
                    <Icon
                      icon="fluent:mail-16-regular"
                      className="h-6 w-6 mr-2 text-sky-600 flex-shrink-0"
                    />
                    {organization?.email}
                  </p>
                </div>
                <div className="col-span-1 flex flex-col items-center md:items-start gap-3">
                  <h5 className="font-medium mb-2 text-xl text-gray-700">
                    More Pages
                  </h5>
                  {menuItems?.map((item, index) => (
                    <Link to={item.slug} key={index} className="mb-1">
                      {item.name}
                    </Link>
                  ))}
                </div>
                <div className="col-span-1 flex flex-col items-center md:items-start space-y-2">
                  <h5 className="font-medium mb-2 text-xl text-gray-700">
                    Coming Soon
                  </h5>
                  <div className="flex space-x-2">
                    <Link to="#">
                      <img src={appstore} alt="App Store" className="w-32" />
                    </Link>
                    <Link to="#">
                      <img
                        src={googlePlay}
                        alt="Google Play"
                        className="w-32"
                      />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </footer>
        </div>
      </div>
      <div
        //   style={{
        //     background:
        //       "linear-gradient(90deg, #0C5483 0%, #40A5E7 52.63%, #003456 100%)",
        //   }}
        className="flex justify-center items-center bg-sky-600"
      >
        <p className="text-white py-3">
          
          &copy; {new Date().getFullYear()} All rights reserved by {organization?.name}. 
          
          {/* All Right Reserved by {organization?.name}. {new Date().getFullYear()} */}
          
          
          </p>
      </div>
    </div>
  );
};

export default Footer;
