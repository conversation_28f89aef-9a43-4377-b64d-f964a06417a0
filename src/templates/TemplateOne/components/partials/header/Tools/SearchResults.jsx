import { Icon } from "@iconify/react/dist/iconify.js";
import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import api from "@/server/api";
import Course from "../../../../pages/home/<USER>";

const SearchResults = () => {
  const { search } = useLocation();
  const params = new URLSearchParams(search);
  const [searchQuery, setSearchQuery] = useState(params.get("query") || "");
  const [searchResults, setSearchResults] = useState(null); // Set null initially
  const [loading, setLoading] = useState(false);

  const searchCourse = async (search) => {
    // if (search.length < 3) return;
    setLoading(true);
    try {
      const res = await api.get(`course-list-web?search=${search}`);
      const courseList = res?.data?.data?.data || [];
      setSearchResults(courseList);
    } catch (error) {
      console.error("Error fetching search results:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    searchCourse(searchQuery);
  }, [searchQuery]);

  return (
    <div className="min-h-screen container">
      <div className="relative flex items-center w-96 mx-auto mt-10">
        <input
          type="text"
          defaultValue={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search Course..."
          className="px-4 py-2 border border-sky-500 w-full rounded-full pr-9"
        />
        <Icon
          icon="iconoir:search"
          className="text-xl text-sky-500 absolute right-3 cursor-pointer"
        />
      </div>

      {loading && <h4 className="text-center my-20">Loading...</h4>}

      {!loading && searchResults && searchResults.length > 0 && (
        <div className="mt-5">
          <p className="text-xl text-gray-600 my-5">{searchQuery.length > 0 ? "Search Result" : "All Courses"}:</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {searchResults.map((result, index) => (
              <Course course={result} key={index} />
            ))}
          </div>
        </div>
      )}

      {!loading && searchResults && searchResults.length === 0 && (
        <h4 className="text-center my-20">No results found</h4>
      )}
    </div>
  );
};

export default SearchResults;
