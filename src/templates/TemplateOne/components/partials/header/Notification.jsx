import React, { useEffect, useState, useRef } from "react";
import Pusher from "pusher-js";
import Icon from "@/components/ui/Icon";
import { useSelector } from "react-redux";
import { useQueryClient } from "@tanstack/react-query";
import useFetch from "@/hooks/useFetch";
import api from "@/server/api";
import { formatTime, getNotificationIcon } from "@/utils/notificationUtils.jsx";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

const Notification = () => {
  const [open, setOpen] = useState(false);
  const { isAuth } = useSelector((state) => state.auth);
  const notificationRef = useRef(null);
  const pusherRef = useRef(null);
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Don't render anything if user is not authenticated
  if (!isAuth) {
    return null;
  }

  // Use Tanstack Query to fetch user notifications
  const {
    data: userResponse,
    isLoading: isLoadingUser
  } = useFetch({
    queryKey: "notifications",
    endPoint: "/notifications", // Disable pagination
    enabled: !!isAuth, // Only fetch if user is authenticated
  });

  // Use Tanstack Query to fetch organization notifications
  const {
    data: orgResponse,
    isLoading: isLoadingOrg
  } = useFetch({
    queryKey: "organization-notifications",
    endPoint: "/organization-notifications", // Disable pagination
    enabled: !!isAuth, // Only fetch if user is authenticated
  });

  // Use Tanstack Query to fetch unread notifications count
  const {
    data: unreadCountResponse
  } = useFetch({
    queryKey: "unread-notifications-count",
    endPoint: "/unread-notifications-count",
    enabled: !!isAuth, // Only fetch if user is authenticated
  });

  // Extract user notifications from response
  const userNotifications = userResponse?.data ?
    (Array.isArray(userResponse?.data) ? userResponse?.data : userResponse?.data?.data || [])
    : [];

  // Extract organization notifications from response
  const orgNotifications = orgResponse?.data ?
    (Array.isArray(orgResponse?.data) ? orgResponse?.data : orgResponse?.data?.data || [])
    : [];

  // Get unread count from API response or calculate from notifications
  const unreadCount = unreadCountResponse?.data?.total ||
    userNotifications.filter(n => !n?.read_at).length + orgNotifications.filter(n => !n?.read_at).length;

  // Determine if any data is loading
  const isLoading = isLoadingUser || isLoadingOrg;

  // Mark a notification as read and handle navigation based on notification type
  const markAsRead = async (id, notification) => {
    try {
      await api.post(`/mark-notification-read/${id}`);

      // Invalidate the queries to refetch notifications and count
      queryClient.invalidateQueries("notifications");
      queryClient.invalidateQueries("organization-notifications");
      queryClient.invalidateQueries("unread-notifications-count");

      // Handle navigation based on notification type
      if (notification?.type === 'assignment' && notification?.data?.assignment_id) {
        // Navigate to assignment details page
        navigate(`/assignment/${notification?.data?.assignment_id}`);
        setOpen(false); // Close the notification dropdown
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
      toast.error("Failed to mark notification as read");
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      await api.post("/mark-all-notifications-read");

      // Invalidate the queries to refetch notifications and count
      queryClient.invalidateQueries("notifications");
      queryClient.invalidateQueries("organization-notifications");
      queryClient.invalidateQueries("unread-notifications-count");
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      toast.error("Failed to mark all notifications as read");
    }
  };

  // Set up Pusher for real-time notifications
  useEffect(() => {
    if (!isAuth) return;

    try {
      const pusherKey = import.meta.env.VITE_PUSHER_APP_KEY;

      const pusher = new Pusher(pusherKey, {
        cluster: "ap2",
        forceTLS: true,
        enabledTransports: ['ws', 'wss']
      });
      pusherRef.current = pusher;

      const channelName = `${isAuth?.id || ''}`;
      const channel = pusher.subscribe(channelName);

      // Listen for notification events
      channel.bind("notification.sent", () => {
        // Invalidate the queries to refetch notifications and count
        queryClient.invalidateQueries("notifications");
        queryClient.invalidateQueries("organization-notifications");
        queryClient.invalidateQueries("unread-notifications-count");
      });

      channel.bind("notification", () => {
        // Invalidate the queries to refetch notifications and count
        queryClient.invalidateQueries("notifications");
        queryClient.invalidateQueries("organization-notifications");
        queryClient.invalidateQueries("unread-notifications-count");
      });

      return () => {
        channel.unbind_all();
        channel.unsubscribe();
        pusher.disconnect();
      };
    } catch (error) {
      console.error("Error setting up Pusher:", error);
      return () => { }; // Empty cleanup function
    }
  }, [isAuth, queryClient]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target)) {
        setOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // No longer automatically marking notifications as read when opening dropdown

  return (
    <div className="relative" ref={notificationRef}>
      <button
        onClick={() => setOpen(!open)}
        className="text-gray-500 relative"
        aria-label="Notifications"
      >
        {isLoading ? (
          <Icon icon="mdi:loading" className="h-6 w-6 text-gray-500 animate-spin" />
        ) : (
          <Icon icon="mdi:bell" className="h-6 w-6 text-gray-500" />
        )}

        {!isLoading && unreadCount > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {open && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-md shadow-lg overflow-hidden z-50 border border-gray-200">
          <div className="p-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <div>
              <h3 className="text-sm font-semibold text-gray-700">Notifications</h3>
            </div>
            {(userNotifications.length > 0 || orgNotifications.length > 0) && unreadCount > 0 && (
              <button
                className="text-xs text-blue-500 hover:text-blue-700"
                onClick={markAllAsRead}
              >
                Mark all as read
              </button>
            )}
          </div>

          {isLoading ? (
            <div className="p-8 text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-500 border-t-transparent"></div>
              <p className="mt-2 text-sm text-gray-600">Loading notifications...</p>
            </div>
          ) : (userNotifications.length === 0 && orgNotifications.length === 0) ? (
            <div className="p-4 text-center text-gray-500">
              <Icon icon="mi:notification" className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm">No notifications yet</p>
            </div>
          ) : (
            <div>
              {/* User Notifications Section */}
              <div className="border-b border-gray-200">
                <div className="p-2 bg-gray-50 border-b border-gray-200">
                  <h4 className="text-xs font-semibold text-gray-600">User Notifications</h4>
                </div>
                <div className="max-h-48 overflow-y-auto">
                  {userNotifications.length === 0 ? (
                    <div className="p-3 text-center text-gray-500">
                      <p className="text-xs">No user notifications</p>
                    </div>
                  ) : (
                    <ul className="divide-y divide-gray-200">
                      {userNotifications.map((notification) => (
                        <li
                          key={notification?.id}
                          className={`p-3 border-b border-gray-100 hover:bg-gray-50 ${!notification?.read_at ? 'bg-blue-50' : ''}`}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex">
                              <div className="mr-3 mt-1">
                                {getNotificationIcon(notification?.type)}
                              </div>
                              <div>
                                <h4 className="text-sm font-medium text-gray-800">{notification?.title || "Notification"}</h4>
                                <p className="text-xs text-gray-600 mt-1">{notification?.message || notification?.body}</p>
                                <div className="flex items-center mt-1 space-x-2">
                                  {notification?.link && (
                                    <a
                                      href={notification?.link}
                                      className="text-xs text-blue-500 hover:underline inline-block"
                                    >
                                      View details
                                    </a>
                                  )}
                                  {notification?.type === 'assignment' && notification?.data?.assignment_id && (
                                    <button
                                      onClick={() => {
                                        navigate(`/assignment/${notification.data.assignment_id}`);
                                        setOpen(false);
                                        if (!notification?.read_at) {
                                          markAsRead(notification?.id, notification);
                                        }
                                      }}
                                      className="text-xs text-orange-500 hover:text-orange-700 inline-flex items-center"
                                    >
                                      <Icon icon="mdi:clipboard-text" className="h-3 w-3 mr-1" />
                                      View Assignment
                                    </button>
                                  )}
                                  {!notification?.read_at && (
                                    <button
                                      onClick={() => markAsRead(notification?.id, notification)}
                                      className="text-xs text-gray-500 hover:text-blue-500 inline-flex items-center"
                                    >
                                      <Icon icon="mdi:check" className="h-3 w-3 mr-1" />
                                      Mark as read
                                    </button>
                                  )}
                                </div>
                              </div>
                            </div>
                            <span className="text-xs text-gray-400 whitespace-nowrap ml-2">
                              {formatTime(notification?.created_at)}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>

              {/* Organization Notifications Section */}
              <div>
                <div className="p-2 bg-gray-50 border-b border-gray-200">
                  <h4 className="text-xs font-semibold text-gray-600">Organization Notifications</h4>
                </div>
                <div className="max-h-48 overflow-y-auto">
                  {orgNotifications.length === 0 ? (
                    <div className="p-3 text-center text-gray-500">
                      <p className="text-xs">No organization notifications</p>
                    </div>
                  ) : (
                    <ul className="divide-y divide-gray-200">
                      {orgNotifications.map((notification) => (
                        <li
                          key={notification?.id}
                          className={`p-3 border-b border-gray-100 hover:bg-gray-50 ${!notification?.read_at ? 'bg-blue-50' : ''}`}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex">
                              <div className="mr-3 mt-1">
                                {getNotificationIcon(notification?.type)}
                              </div>
                              <div>
                                <h4 className="text-sm font-medium text-gray-800">{notification?.title || "Notification"}</h4>
                                <p className="text-xs text-gray-600 mt-1">{notification?.message || notification?.body}</p>
                                <div className="flex items-center mt-1 space-x-2">
                                  {notification?.link && (
                                    <a
                                      href={notification?.link}
                                      className="text-xs text-blue-500 hover:underline inline-block"
                                    >
                                      View details
                                    </a>
                                  )}
                                  {notification?.type === 'assignment' && notification?.data?.assignment_id && (
                                    <button
                                      onClick={() => {
                                        navigate(`/assignment/${notification.data.assignment_id}`);
                                        setOpen(false);
                                        if (!notification?.read_at) {
                                          markAsRead(notification?.id, notification);
                                        }
                                      }}
                                      className="text-xs text-orange-500 hover:text-orange-700 inline-flex items-center"
                                    >
                                      <Icon icon="mdi:clipboard-text" className="h-3 w-3 mr-1" />
                                      View Assignment
                                    </button>
                                  )}
                                  {!notification?.read_at && (
                                    <button
                                      onClick={() => markAsRead(notification?.id, notification)}
                                      className="text-xs text-gray-500 hover:text-blue-500 inline-flex items-center"
                                    >
                                      <Icon icon="mdi:check" className="h-3 w-3 mr-1" />
                                      Mark as read
                                    </button>
                                  )}
                                </div>
                              </div>
                            </div>
                            <span className="text-xs text-gray-400 whitespace-nowrap ml-2">
                              {formatTime(notification?.created_at)}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Notification;
