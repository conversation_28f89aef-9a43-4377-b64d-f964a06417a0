import React from "react";

const CustomInput = ({ label, required, type = "text", error = "", ...props }) => {
  return (
    <div>
      {/* Label styling */}
      {label && (
        <label
          htmlFor={props.id || props.name}
          className={`block text-gray-700 text-sm font-medium mb-2`}
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}

      {/* Input field with enhanced styles */}
      <input
        {...props}
        type={type}
        className={`appearance-none w-full border rounded-lg py-2 px-4 leading-tight focus:outline-none focus:ring-2 transition-all duration-300 ease-in-out shadow-sm focus:border-sky-500 ${
          error ? "border-red-500" : "border-gray-300 focus:ring-sky-300"
        }`}
      />

      {/* Error handling */}
      {error && <span className="text-red-500 text-xs">{error}</span>}
    </div>
  );
};

export default CustomInput;
