import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { useSelector } from "react-redux";
import { ASSET_URL } from "@/config";
import Card from "../../components/ui/Card";
import Button from "../../components/ui/Button";
import Textarea from "../../components/ui/Textarea";
import CustomInput from "./CustomInput";
import avatar from "@/assets/images/avatar/av-1.svg";
import "react-quill/dist/quill.snow.css";
import { motion } from "framer-motion";
import api from "@/server/api";
import { toast } from "react-toastify";
import Modal from "../../components/ui/Modal";

const DiscussionForum = ({ courseId }) => {
  const [discussions, setDiscussions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("recent");
  const [editorContent, setEditorContent] = useState("");
  const [discussionTitle, setDiscussionTitle] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportReason, setReportReason] = useState("");
  const [customReportReason, setCustomReportReason] = useState("");
  const [reportType, setReportType] = useState({ discussionId: null, commentId: null });
  const [reportSubmitting, setReportSubmitting] = useState(false);
  const { isAuth } = useSelector((state) => state.auth);

  const user = isAuth;
  // Function to fetch discussions
  const fetchDiscussions = async () => {
    if (!courseId) return;

    setLoading(true);
    try {
      const response = await api.get(`discussions?course_id=${courseId}&current_page=0`);

      // Check the response structure and handle it appropriately
      if (response.data && response.data?.data) {
        // Handle nested data structure if it exists
        if (response.data.data.data && Array.isArray(response.data.data.data)) {
          setDiscussions(response.data.data.data);
        } else if (Array.isArray(response.data.data)) {
          setDiscussions(response.data.data);
        } else {
          setDiscussions([]);
        }
      } else {
        setDiscussions([]);
      }
    } catch (error) {
      console.error("Error fetching discussions:", error);
      toast.error("Failed to load discussions. Please try again later.");
      setDiscussions([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch discussions when component mounts or courseId changes
  useEffect(() => {
    fetchDiscussions();
  }, [courseId]);

  // Format date to a readable format
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      const hours = Math.floor(diffTime / (1000 * 60 * 60));
      if (hours === 0) {
        const minutes = Math.floor(diffTime / (1000 * 60));
        return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
      }
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const handlePostSubmit = async () => {
    // Check if the button should be disabled - if so, don't proceed
    if (!editorContent.trim() || !discussionTitle.trim() || submitting) {
      return; // Don't proceed if the button should be disabled
    }

    if (!user) {
      toast.error("You must be logged in to post a discussion");
      return;
    }

    setSubmitting(true);
    try {
      const response = await api.post('discussions', {
        course_id: parseInt(courseId),
        title: discussionTitle,
        content: editorContent
      });

      if (response.data) {
        // Clear the form
        setEditorContent("");
        setDiscussionTitle("");

        // Fetch the updated discussions list from the server
        // This ensures we have the most up-to-date data with proper structure
        fetchDiscussions();
      }
    } catch (error) {
      console.error("Error posting discussion:", error);
      toast.error(error.response?.data?.message || "Failed to post discussion. Please try again.");

      // Reset form on error
      setEditorContent("");
      setDiscussionTitle("");
    } finally {
      setSubmitting(false);
    }
  };

  const handleReply = async (discussionId, replyContent) => {
    if (!replyContent.trim()) return;

    if (!user) {
      toast.error("You must be logged in to reply to a discussion");
      return;
    }

    try {
      const response = await api.post(`comments`, {
        discussion_id: discussionId,
        parent_id: null,
        content: replyContent
      });

      if (response.data && response.data.data) {
        // Fetch the updated discussions list from the server
        // This ensures we have the most up-to-date data with proper structure
        fetchDiscussions();
      }
    } catch (error) {
      console.error("Error posting comment:", error);
      toast.error(error.response?.data?.message || "Failed to post comment. Please try again.");

      // We don't need to reset the form here since the component already handles this
      // The replyContent is managed by the DiscussionPost component and is reset in handleSubmitReply
    }
  };

  const handleReport = async () => {
    if (!user) {
      toast.error("You must be logged in to report content");
      return;
    }

    // Check if a reason is provided
    if (!reportReason) {
      toast.error("Please select a reason for reporting");
      return;
    }

    // For "Other" reason, make sure custom text is entered
    if (reportReason === "Other" && !customReportReason.trim()) {
      toast.error("Please provide details for your report");
      return;
    }

    setReportSubmitting(true);
    try {
      // Prepare the reason text - if it's "Other" with custom text, use the custom text
      const finalReason = reportReason === "Other" ? customReportReason.trim() : reportReason;

      // Prepare the payload based on what's being reported
      let payload = {
        discussion_id: reportType.discussionId,
        reason: finalReason
      };

      // Only include comment_id in the payload if we're reporting a comment
      // For discussions, don't include the comment_id field at all
      if (reportType.commentId) {
        payload.comment_id = reportType.commentId;
      }
      // Don't include comment_id at all for discussion reports

      const response = await api.post('report', payload);

      if (response.data && response.data.status) {
        setShowReportModal(false);
        setReportReason("");
        setCustomReportReason("");
        setReportType({ discussionId: null, commentId: null });

        // Refresh the discussions to get the latest data from the server
        fetchDiscussions();
      }
    } catch (error) {
      console.error("Error reporting content:", error);
      toast.error(error.response?.data?.message || "Failed to report content. Please try again.");

      // Reset form on error
      setReportReason("");
      setCustomReportReason("");
      setShowReportModal(false);
    } finally {
      setReportSubmitting(false);
    }
  };

  const openReportModal = (discussionId, commentId = null) => {
    if (!user) {
      toast.error("You must be logged in to report content");
      return;
    }

    // Only check if the specific content (discussion or comment) has already been reported
    // We allow reporting comments even if the parent discussion is reported
    if (commentId === null) {
      // Check if discussion is reported
      const discussion = discussions?.find(d => d?.id === discussionId);
      if (discussion?.reported_by_me) {
        toast.info("You have already reported this discussion");
        return;
      }
    } else {
      // Check if comment is reported
      const discussion = discussions?.find(d => d?.id === discussionId);
      const comment = discussion?.comments?.find(c => c?.id === commentId);
      if (comment?.reported_by_me) {
        toast.info("You have already reported this comment");
        return;
      }
    }

    setReportType({ discussionId, commentId });
    setReportReason("");
    setCustomReportReason("");
    setShowReportModal(true);
  };

  const handleLike = async (discussionId, commentId = null) => {
    if (!user) {
      toast.error("You must be logged in to like a discussion");
      return;
    }

    try {
      // Prepare the request payload based on whether we're liking a discussion or a comment
      const payload = commentId === null
        ? { discussion_id: discussionId }
        : { comment_id: commentId };

      // Find the current item to determine if we're adding or removing a like
      let currentLikedStatus = false;
      let updatedDiscussions = [...discussions];

      if (commentId === null) {
        // Find the discussion
        const discussionIndex = updatedDiscussions.findIndex(d => d?.id === discussionId);
        if (discussionIndex !== -1) {
          currentLikedStatus = updatedDiscussions[discussionIndex]?.liked_by_me || false;

          // Update optimistically
          updatedDiscussions[discussionIndex] = {
            ...updatedDiscussions[discussionIndex],
            liked_by_me: !currentLikedStatus,
            likes_count: currentLikedStatus
              ? Math.max(0, (updatedDiscussions[discussionIndex]?.likes_count || 0) - 1)
              : (updatedDiscussions[discussionIndex]?.likes_count || 0) + 1
          };
        }
      } else {
        // Find the comment
        const discussionIndex = updatedDiscussions.findIndex(d => d?.id === discussionId);
        if (discussionIndex !== -1 && updatedDiscussions[discussionIndex]?.comments) {
          const commentIndex = updatedDiscussions[discussionIndex].comments.findIndex(c => c?.id === commentId);

          if (commentIndex !== -1) {
            const comment = updatedDiscussions[discussionIndex].comments[commentIndex];
            currentLikedStatus = comment?.liked_by_me || false;

            // Update the comment optimistically
            updatedDiscussions[discussionIndex].comments[commentIndex] = {
              ...comment,
              liked_by_me: !currentLikedStatus,
              likes_count: currentLikedStatus
                ? Math.max(0, (comment?.likes_count || 0) - 1)
                : (comment?.likes_count || 0) + 1
            };
          }
        }
      }

      // Update the UI optimistically
      setDiscussions(updatedDiscussions);

      const response = await api.post(`like`, payload);

      if (response.data && response.data.status) {
        // Refresh the discussions to get the latest data from the server
        // This ensures we have the most accurate data
        fetchDiscussions();
      }
    } catch (error) {
      console.error("Error toggling like:", error);
      toast.error("Failed to update like status. Please try again.");

      // Log the detailed error for debugging
      if (error.response) {
        console.error("Error response:", error.response.data);
      }

      // Refresh to get the correct state in case of error
      fetchDiscussions();
    }
  };

  // Filter discussions based on active tab
  const filteredDiscussions = () => {
    if (!Array.isArray(discussions)) {
      return [];
    }

    switch (activeTab) {
      case 'recent':
        return [...discussions].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      case 'popular':
        return [...discussions].sort((a, b) => (b.likes_count || 0) - (a.likes_count || 0));
      case 'unanswered':
        return discussions.filter(discussion => !discussion?.comments || discussion?.comments?.length === 0);
      default:
        return discussions;
    }
  };

  return (
    <section className="container my-14 space-y-8">
      {/* Report Modal */}
      <Modal
        activeModal={showReportModal}
        onClose={() => setShowReportModal(false)}
        title="Report Content"
        centered={true}
        footerContent={
          <div className="flex items-center justify-end space-x-3">
            <Button
              text="Cancel"
              className="border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-full px-5"
              onClick={() => setShowReportModal(false)}
            />
            <Button
              text={reportSubmitting ? "Submitting..." : "Submit Report"}
              className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-full px-5"
              onClick={handleReport}
              disabled={!reportReason.trim() || reportSubmitting}
            />
          </div>
        }
      >
        <div className="p-5">
          <p className="text-gray-600 mb-4">
            Please provide a reason for reporting this content. This will help moderators review the content appropriately.
          </p>

          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">Select a reason:</label>

            {["Inappropriate language", "Harassment or bullying", "Spam or misleading", "Off-topic content", "Other"].map((reason) => (
              <div key={reason} className="flex items-center">
                <input
                  type="radio"
                  id={reason.replace(/\s+/g, '-').toLowerCase()}
                  name="report-reason"
                  value={reason}
                  checked={reportReason === reason}
                  onChange={() => setReportReason(reason)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor={reason.replace(/\s+/g, '-').toLowerCase()} className="ml-3 block text-sm text-gray-700">
                  {reason}
                </label>
              </div>
            ))}

            {reportReason === "Other" && (
              <div className="mt-4">
                <label htmlFor="custom-reason" className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Icon icon="mdi:pencil" className="h-4 w-4 text-blue-500 mr-2" />
                  <span>Please specify your reason:</span>
                </label>
                <div className="relative">
                  <textarea
                    id="custom-reason"
                    rows="4"
                    className="block w-full rounded-md border-blue-200 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 pl-10 bg-blue-50 bg-opacity-30"
                    placeholder="Provide details about your report..."
                    value={customReportReason}
                    onChange={(e) => setCustomReportReason(e.target.value)}
                  ></textarea>
                  <div className="absolute left-3 top-3 text-blue-400">
                    <Icon icon="mdi:message-text-outline" className="h-5 w-5" />
                  </div>
                </div>
                <p className="mt-1 text-xs text-gray-500 italic">Please be specific and provide context to help moderators understand your concern.</p>
              </div>
            )}
          </div>
        </div>
      </Modal>
      {/* Header with gradient background */}
      <div className="bg-gradient-to-r from-sky-600 to-blue-700 rounded-xl p-6 shadow-lg">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center space-x-4 mb-4 md:mb-0">
            <div className="bg-white/20 p-3 rounded-full">
              <Icon icon="mdi:forum" className="h-8 w-8 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">Discussion Forum</h2>
              <p className="text-blue-100">Share ideas and get help from the community</p>
            </div>
          </div>

          <div className="flex space-x-2 bg-white/10 p-1 rounded-full">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`px-5 py-2 rounded-full text-sm font-medium transition-all ${
                activeTab === 'recent'
                  ? 'bg-white text-blue-700 shadow-md'
                  : 'text-white hover:bg-white/20'
              }`}
              onClick={() => setActiveTab('recent')}
            >
              <div className="flex items-center space-x-1">
                <Icon icon="mdi:clock-outline" className="h-4 w-4" />
                <span>Recent</span>
              </div>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`px-5 py-2 rounded-full text-sm font-medium transition-all ${
                activeTab === 'popular'
                  ? 'bg-white text-blue-700 shadow-md'
                  : 'text-white hover:bg-white/20'
              }`}
              onClick={() => setActiveTab('popular')}
            >
              <div className="flex items-center space-x-1">
                <Icon icon="mdi:fire" className="h-4 w-4" />
                <span>Popular</span>
              </div>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`px-5 py-2 rounded-full text-sm font-medium transition-all ${
                activeTab === 'unanswered'
                  ? 'bg-white text-blue-700 shadow-md'
                  : 'text-white hover:bg-white/20'
              }`}
              onClick={() => setActiveTab('unanswered')}
            >
              <div className="flex items-center space-x-1">
                <Icon icon="mdi:help-circle-outline" className="h-4 w-4" />
                <span>Unanswered</span>
              </div>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Create new post */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="border border-gray-200 shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
          <div className="p-6 space-y-4">
            {/* User info section - Avatar and name at the top */}
            <div className="flex items-center space-x-3 mb-2">
              <div className="relative">
                <img
                  src={user?.avatar ? ASSET_URL + user.avatar : avatar}
                  alt="User Avatar"
                  className="w-12 h-12 rounded-full object-cover border-2 border-sky-100"
                />
                <div className="absolute bottom-0 right-0 bg-green-500 h-3 w-3 rounded-full border-2 border-white"></div>
              </div>
              <div>
                <h6 className="font-semibold text-gray-800">{user?.name || "You"}</h6>
                <div className="flex items-center">
                  <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">{user?.role || "Student"}</span>
                  <span className="text-xs text-gray-500 ml-2">• Share your thoughts</span>
                </div>
              </div>
            </div>

            {/* Discussion Title Input - Second element */}
            <div className="mb-3">
              <CustomInput
                label="Discussion Title"
                name="discussion_title"
                placeholder="Enter a title for your discussion"
                value={discussionTitle}
                onChange={(e) => setDiscussionTitle(e.target.value)}
                className="w-full"
              />
            </div>

            {/* Content editor - Third element */}
            <div className="border rounded-lg shadow-sm overflow-hidden">
              
                <Textarea
                  type="text"
                  placeholder="Share your thoughts or questions with the class..."
                  required
                  onChange={(e) => {
                    setEditorContent(e.target.value);
                  }}
                />
              {/* <ReactQuill
                value={editorContent}
                onChange={setEditorContent}
                placeholder="Share your thoughts or questions with the class..."
                className="bg-white rounded-lg"
                modules={{
                  toolbar: [
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    ['link', 'image'],
                    ['clean']
                  ],
                }}
              /> */}
            </div>

            <div className="flex items-center justify-between pt-2">
              <div className="text-xs text-gray-600 max-w-md bg-blue-50 p-3 rounded-md border border-blue-100">
                <div className="flex items-center space-x-2 mb-1">
                  <Icon icon="mdi:shield-check" className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-700">Community Guidelines</span>
                </div>
                <p className="ml-6">Please be respectful and avoid abusive, vulgar, or offensive language. Maintain a positive learning environment for everyone.</p>
              </div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  text={
                    <div className="flex items-center space-x-2">
                      <Icon icon={submitting ? "mdi:loading" : "mdi:send"} className={`h-4 w-4 ${submitting ? 'animate-spin' : ''}`} />
                      <span>{submitting ? "Posting..." : "Post Discussion"}</span>
                    </div>
                  }
                  className="bg-gradient-to-r from-sky-600 to-blue-700 hover:from-sky-700 hover:to-blue-800 text-white px-6 py-2 rounded-full shadow-md"
                  onClick={handlePostSubmit}
                  disabled={!editorContent.trim() || !discussionTitle.trim() || submitting}
                />
              </motion.div>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-gradient-to-br from-blue-50 to-sky-50 p-4 rounded-xl border border-blue-100 shadow-sm"
        >
          <div className="flex items-center space-x-3">
            <div className="bg-blue-100 p-3 rounded-full">
              <Icon icon="mdi:message-text-outline" className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gray-800">{discussions.length}</h4>
              <p className="text-sm text-gray-600">Total Discussions</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-gradient-to-br from-green-50 to-teal-50 p-4 rounded-xl border border-green-100 shadow-sm"
        >
          <div className="flex items-center space-x-3">
            <div className="bg-green-100 p-3 rounded-full">
              <Icon icon="mdi:account-multiple-outline" className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gray-800">
                {Array.isArray(discussions) ? new Set(discussions?.map(d => d?.user?.id))?.size : 0}
              </h4>
              <p className="text-sm text-gray-600">Active Participants</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-gradient-to-br from-purple-50 to-indigo-50 p-4 rounded-xl border border-purple-100 shadow-sm"
        >
          <div className="flex items-center space-x-3">
            <div className="bg-purple-100 p-3 rounded-full">
              <Icon icon="mdi:clock-time-four-outline" className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gray-800">
                {Array.isArray(discussions) && discussions.length > 0
                  ? formatDate([...discussions].sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0].created_at)
                  : 'No activity'}
              </h4>
              <p className="text-sm text-gray-600">Last Activity</p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Discussion list */}
      {loading ? (
        <div className="flex flex-col items-center justify-center py-16">
          <div className="relative w-20 h-20">
            <div className="absolute top-0 left-0 w-full h-full border-4 border-blue-200 rounded-full animate-ping opacity-75"></div>
            <div className="relative w-full h-full border-4 border-t-blue-600 border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
          </div>
          <p className="mt-4 text-gray-600 font-medium">Loading discussions...</p>
        </div>
      ) : filteredDiscussions().length === 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center py-16 bg-gray-50 rounded-xl border border-gray-200"
        >
          <Icon icon="mdi:forum-outline" className="mx-auto h-20 w-20 text-gray-300" />
          <h3 className="mt-4 text-xl font-medium text-gray-800">No discussions yet</h3>
          <p className="mt-2 text-gray-500 max-w-md mx-auto">
            Be the first to start a discussion! Share your thoughts, questions, or insights with the class.
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="mt-6 px-6 py-2 bg-gradient-to-r from-sky-600 to-blue-700 text-white rounded-full shadow-md flex items-center space-x-2 mx-auto"
            onClick={() => document.querySelector('.ql-editor').focus()}
          >
            <Icon icon="mdi:plus-circle" className="h-5 w-5" />
            <span>Start a New Discussion</span>
          </motion.button>
        </motion.div>
      ) : (
        <div className="space-y-6">
          {filteredDiscussions().map((discussion, index) => (
            <motion.div
              key={discussion.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <DiscussionPost
                discussion={discussion}
                onLike={handleLike}
                onReply={handleReply}
                formatDate={formatDate}
                onReport={openReportModal}
              />
            </motion.div>
          ))}
        </div>
      )}
    </section>
  );
};

// Discussion Post Component
const DiscussionPost = ({ discussion, onLike, onReply, formatDate, onReport }) => {
  const [showReplies, setShowReplies] = useState(true);
  const [replyContent, setReplyContent] = useState("");
  const [isReplying, setIsReplying] = useState(false);
  const { isAuth } = useSelector((state) => state.auth);
  const user = isAuth;

  // Track liked comments locally to ensure UI updates immediately
  const [likedComments, setLikedComments] = useState({});

  // Initialize liked comments from the discussion data
  useEffect(() => {
    if (discussion?.comments && discussion.comments.length > 0) {
      const initialLikedState = {};
      discussion.comments.forEach(comment => {
        initialLikedState[comment.id] = comment.liked_by_me || false;
      });
      setLikedComments(initialLikedState);
    }
  }, [discussion?.comments]);

  const handleSubmitReply = () => {
    // Check if the button should be disabled - if so, don't proceed
    if (!replyContent.trim()) return;

    onReply(discussion.id, replyContent);
    setReplyContent("");
    setIsReplying(false);
  };

  const handleLikeClick = () => {
    // No need to update local state, as the parent component will update the discussions array
    onLike(discussion.id);
  };

  const handleCommentLikeClick = (commentId) => {
    // Update local state for immediate UI feedback
    setLikedComments(prev => ({
      ...prev,
      [commentId]: !prev[commentId]
    }));

    // Call the parent component's like handler
    onLike(discussion.id, commentId);
  };

  return (
    <Card className="border border-gray-200 shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden bg-gradient-to-br from-white to-gray-50">
      <div className="p-5 space-y-4">
        {/* Post header */}
        <div className="flex items-start space-x-3">
          <div className="relative">
            <img
              src={discussion?.user?.avatar ? ASSET_URL + discussion.user.avatar : avatar}
              alt={discussion?.user?.name || "User"}
              className="w-10 h-10 rounded-full object-cover border-2 border-sky-100"
            />
            {discussion?.user?.role === 'Mentor' && (
              <div className="absolute -bottom-1 -right-1 bg-blue-500 text-white p-1 rounded-full border-2 border-white">
                <Icon icon="mdi:check" className="h-3 w-3" />
              </div>
            )}
          </div>
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-gray-800 text-base">{discussion?.user?.name || "User"}</h3>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-0.5 text-xs rounded-full ${
                    discussion?.user?.role === 'Mentor'
                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                      : 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700'
                  }`}>
                    {discussion?.user?.role || "Student"}
                  </span>
                  <span className="text-xs text-gray-500">{formatDate(discussion?.created_at)}</span>
                </div>
              </div>

              <div className="flex space-x-2">
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  className={`${
                    discussion?.reported_by_me
                      ? 'text-red-500'
                      : 'text-gray-400 hover:text-red-500'
                  } transition-colors`}
                  onClick={() => !discussion?.reported_by_me && onReport(discussion.id, null)}
                  title={discussion?.reported_by_me ? "You've reported this discussion" : "Report this discussion"}
                  disabled={discussion?.reported_by_me}
                >
                  <Icon
                    icon={discussion?.reported_by_me ? "mdi:flag" : "mdi:flag-outline"}
                    className="h-5 w-5"
                  />
                  {discussion?.reported_by_me && (
                    <span className="sr-only">Reported</span>
                  )}
                </motion.button>
              </div>
            </div>

            {/* Discussion Title and Content */}
            <div className="mt-2 bg-gradient-to-br from-gray-50 to-white p-4 rounded-lg border border-gray-200 shadow-sm">
              {discussion?.title && (
                <div className="flex items-center mb-2 pb-2 border-b border-gray-200">
                  <Icon icon="mdi:text-box-outline" className="h-4 w-4 text-blue-600 mr-2" />
                  <h3 className="text-gray-800 font-medium text-base">
                    {discussion?.title}
                  </h3>
                </div>
              )}

              {/* Discussion Content */}
              <div className="text-gray-700 prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ __html: discussion?.content }}
              />
            </div>

            {/* Post actions */}
            <div className="mt-3 flex items-center justify-between border-t border-gray-100 pt-3">
              <div className="flex items-center space-x-6">
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  className={`flex items-center space-x-1.5 px-3 py-1.5 rounded-full ${
                    discussion?.liked_by_me
                      ? 'text-red-600 bg-red-50 border border-red-200'
                      : 'text-gray-600 hover:text-red-600 hover:bg-red-50 border border-gray-200 hover:border-red-200'
                  } transition-colors`}
                  onClick={handleLikeClick}
                >
                  <Icon
                    icon={discussion?.liked_by_me ? "mdi:heart" : "mdi:heart-outline"}
                    className="h-5 w-5"
                  />
                  <span className="font-medium">{discussion?.likes_count || 0}</span>
                </motion.button>

                <motion.button
                  whileTap={{ scale: 0.95 }}
                  className={`flex items-center space-x-1.5 px-3 py-1.5 rounded-full ${
                    isReplying
                      ? 'text-blue-600 bg-blue-50 border border-blue-200'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50 border border-gray-200 hover:border-blue-200'
                  } transition-colors`}
                  onClick={() => setIsReplying(!isReplying)}
                >
                  <Icon icon="mdi:message-reply-outline" className="h-5 w-5" />
                  <span className="font-medium">Reply</span>
                </motion.button>

                {discussion?.comments?.length > 0 && (
                  <motion.button
                    whileTap={{ scale: 0.95 }}
                    className={`flex items-center space-x-1.5 px-3 py-1.5 rounded-full ${
                      showReplies
                        ? 'text-purple-600 bg-purple-50 border border-purple-200'
                        : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50 border border-gray-200 hover:border-purple-200'
                    } transition-colors`}
                    onClick={() => setShowReplies(!showReplies)}
                  >
                    <Icon
                      icon={showReplies ? "mdi:comment-multiple" : "mdi:comment-multiple-outline"}
                      className="h-5 w-5"
                    />
                    <span className="font-medium">
                      {discussion?.comments_count || discussion?.comments?.length} {(discussion?.comments_count || discussion?.comments?.length) === 1 ? 'reply' : 'replies'}
                    </span>
                    <Icon
                      icon={showReplies ? "mdi:chevron-up" : "mdi:chevron-down"}
                      className="h-4 w-4 ml-1"
                    />
                  </motion.button>
                )}
              </div>

              {/* Share button removed */}
            </div>
          </div>
        </div>

        {/* Reply form */}
        {isReplying && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="ml-12 mt-4 space-y-4 bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 shadow-sm"
          >
            <div className="flex items-center mb-2">
              <Icon icon="mdi:reply" className="h-4 w-4 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-blue-700">Write a Reply</span>
            </div>
            <div className="flex items-center space-x-3">
              <img
                src={user?.avatar ? ASSET_URL + user.avatar : avatar}
                alt="Your Avatar"
                className="w-8 h-8 rounded-full object-cover border-2 border-sky-100"
              />
              <div>
                <h3 className="font-semibold text-gray-800 text-base">{user?.name || "You"}</h3>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-0.5 text-xs rounded-full bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700`}>
                    {user?.role || "Student"}
                  </span>
                  <span className="text-xs text-gray-500">Replying to {discussion?.user?.name || "User"}</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <Textarea
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                placeholder="Write your reply..."
                className="w-full border-blue-200 rounded-md focus:border-sky-500 focus:ring focus:ring-sky-200 focus:ring-opacity-50 pl-10"
                row={3}
              />
              <div className="absolute left-3 top-3 text-blue-400">
                <Icon icon="mdi:message-text-outline" className="h-5 w-5" />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-xs text-gray-600 max-w-xs bg-blue-50 p-2 rounded-md border border-blue-100">
                <div className="flex items-center space-x-1">
                  <Icon icon="mdi:shield-check-outline" className="h-3 w-3 text-blue-500" />
                  <p className="font-medium">Please be respectful and avoid offensive language.</p>
                </div>
              </div>

              <div className="flex space-x-3">
                <motion.div whileTap={{ scale: 0.95 }}>
                  <Button
                    text="Cancel"
                    className="border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-full px-5"
                    onClick={() => setIsReplying(false)}
                  />
                </motion.div>

                <motion.div whileTap={{ scale: 0.95 }}>
                  <Button
                    text={
                      <div className="flex items-center space-x-1">
                        <Icon icon="mdi:send" className="h-4 w-4" />
                        <span>Post Reply</span>
                      </div>
                    }
                    className="bg-gradient-to-r from-sky-600 to-blue-700 hover:from-sky-700 hover:to-blue-800 text-white rounded-full px-5"
                    onClick={handleSubmitReply}
                    disabled={!replyContent.trim()}
                  />
                </motion.div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Replies */}
        {discussion?.comments?.length > 0 && (
          <motion.div
            initial={false}
            animate={{ height: showReplies ? 'auto' : 0, opacity: showReplies ? 1 : 0 }}
            transition={{ duration: 0.3 }}
            className={`ml-12 space-y-4 mt-2 overflow-hidden ${showReplies ? '' : 'hidden'}`}
          >
            <div className="border-l-2 border-blue-200 pl-4 space-y-4">
              <div className="flex items-center mb-2">
                <Icon icon="mdi:comment-multiple-outline" className="h-4 w-4 text-blue-600 mr-2" />
                <span className="text-sm font-medium text-blue-700">Conversation</span>
              </div>
              {discussion?.comments?.map((reply, index) => (
                <motion.div
                  key={reply.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="bg-gradient-to-br from-gray-50 to-white rounded-lg p-4 hover:bg-gray-100 transition-colors border border-gray-200 shadow-sm"
                >
                  <div className="flex items-start space-x-3">
                    <div className="relative">
                      <img
                        src={reply?.user?.avatar ? ASSET_URL + reply.user.avatar : avatar}
                        alt={reply?.user?.name || "User"}
                        className="w-8 h-8 rounded-full object-cover border-2 border-sky-100"
                      />
                      {reply?.user?.role === 'Mentor' && (
                        <div className="absolute -bottom-1 -right-1 bg-blue-500 text-white p-0.5 rounded-full border-2 border-white">
                          <Icon icon="mdi:check" className="h-2 w-2" />
                        </div>
                      )}
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-gray-800 text-base">{reply?.user?.name || "User"}</h3>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-0.5 text-xs rounded-full ${
                              reply?.user?.role === 'Mentor'
                                ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                                : 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700'
                            }`}>
                              {reply?.user?.role || "Student"}
                            </span>
                            <span className="text-xs text-gray-500">{formatDate(reply?.created_at)}</span>
                          </div>
                        </div>

                        <motion.button
                          whileTap={{ scale: 0.95 }}
                          className={`${
                            reply?.reported_by_me
                              ? 'text-red-500'
                              : 'text-gray-400 hover:text-red-500'
                          } transition-colors`}
                          onClick={() => !reply?.reported_by_me && onReport(discussion.id, reply.id)}
                          title={reply?.reported_by_me ? "You've reported this comment" : "Report this comment"}
                          disabled={reply?.reported_by_me}
                        >
                          <Icon
                            icon={reply?.reported_by_me ? "mdi:flag" : "mdi:flag-outline"}
                            className="h-4 w-4"
                          />
                          {reply?.reported_by_me && (
                            <span className="sr-only">Reported</span>
                          )}
                        </motion.button>
                      </div>

                      <p className="mt-2 text-gray-700">{reply?.content}</p>

                      {/* Reply actions */}
                      <div className="mt-3 flex items-center space-x-4">
                        <motion.button
                          whileTap={{ scale: 0.95 }}
                          className={`flex items-center space-x-1 px-2 py-1 rounded-full ${
                            likedComments[reply.id] || reply?.liked_by_me
                              ? 'text-red-600 bg-red-50 border border-red-200'
                              : 'text-gray-600 hover:text-red-600 hover:bg-red-50 border border-gray-200 hover:border-red-200'
                          } transition-colors`}
                          onClick={() => handleCommentLikeClick(reply.id)}
                        >
                          <Icon
                            icon={likedComments[reply.id] || reply?.liked_by_me ? "mdi:heart" : "mdi:heart-outline"}
                            className="h-4 w-4"
                          />
                          <span className="text-sm font-medium">{reply?.likes_count || 0}</span>
                        </motion.button>

                        {/* Reply to comment button removed */}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </Card>
  );
};

export default DiscussionForum;
