import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import { Icon } from "@iconify/react";
import useFetch from "@/hooks/useFetch";
import Loading from "@/components/Loading";
import GoBack from "@/components/ui/GoBack";
import DiscussionForum from "../../components/discussion/DiscussionForum";
import { ASSET_URL } from "@/config";

const DiscussionPage = () => {
  const { courseId } = useParams();
  const [activeTab, setActiveTab] = useState("discussions");

  const {
    data: courseDetails,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `course-details-${courseId}`,
    endPoint: `course-details?id=${courseId}`,
    dependencies: [courseId],
  });

  if (isLoading) return (
    <div className="flex flex-col items-center justify-center min-h-[60vh]">
      <div className="relative w-24 h-24">
        <div className="absolute top-0 left-0 w-full h-full border-4 border-blue-200 rounded-full animate-ping opacity-75"></div>
        <div className="relative w-full h-full border-4 border-t-blue-600 border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
      </div>
      <p className="mt-6 text-gray-600 font-medium">Loading course details...</p>
    </div>
  );

  if (isError) return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
      <Icon icon="mdi:alert-circle-outline" className="text-red-500 w-20 h-20" />
      <h2 className="text-2xl font-bold text-gray-800 mt-4">Error Loading Course</h2>
      <p className="text-gray-600 mt-2 max-w-md">
        We couldn't load the course details. Please try again or contact support.
      </p>
      <button
        onClick={() => window.location.reload()}
        className="mt-6 px-6 py-2 bg-sky-600 text-white rounded-full hover:bg-sky-700 transition-colors"
      >
        Retry
      </button>
    </div>
  );

  const course = courseDetails?.data;

  return (
    <div className="container pb-16">
      <GoBack title={course?.title || "Course Discussion"} />

      {/* Hero Section with Course Info */}
      <div className="bg-gradient-to-r from-sky-600 to-blue-700 rounded-xl p-8 mb-8 shadow-lg relative overflow-hidden">
        <div className="absolute top-0 right-0 w-64 h-64 bg-white opacity-5 rounded-full -mt-20 -mr-20"></div>
        <div className="absolute bottom-0 left-0 w-40 h-40 bg-white opacity-5 rounded-full -mb-10 -ml-10"></div>

        <div className="relative z-10 flex flex-col md:flex-row items-start md:items-center gap-6">
          <img
            src={course?.thumbnail ? ASSET_URL + course?.thumbnail : "/placeholder-course.jpg"}
            alt={course?.title}
            className="w-20 h-20 object-cover rounded-lg shadow-md border-2 border-white/30"
          />

          <div className="flex-1">
            <h1 className="text-3xl font-bold text-white mb-2">
              {course?.title || "Course Discussion"}
            </h1>
            <div className="flex flex-wrap items-center gap-4 text-blue-100">
              <div className="flex items-center gap-1">
                <Icon icon="mdi:account-group" className="h-5 w-5" />
                <span>{course?.enrolled_students || 0} Students</span>
              </div>
              <div className="flex items-center gap-1">
                <Icon icon="mdi:message-text" className="h-5 w-5" />
                <span>Active Community</span>
              </div>
              <div className="flex items-center gap-1">
                <Icon icon="mdi:star" className="h-5 w-5" />
                <span>{course?.rating || "4.5"} Rating</span>
              </div>
            </div>
          </div>

          <Link
            to={`/course-details/${courseId}`}
            className="px-5 py-2 bg-white/10 hover:bg-white/20 text-white rounded-full border border-white/30 transition-colors flex items-center gap-2"
          >
            <Icon icon="mdi:book-open-variant" className="h-5 w-5" />
            <span>Course Details</span>
          </Link>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex border-b border-gray-200 mb-8">
        <button
          className={`px-6 py-3 font-medium text-sm transition-colors relative ${
            activeTab === 'discussions'
              ? 'text-sky-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('discussions')}
        >
          <div className="flex items-center gap-2">
            <Icon icon="mdi:forum" className="h-5 w-5" />
            <span>Discussions</span>
          </div>
          {activeTab === 'discussions' && (
            <motion.div
              layoutId="activeTab"
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-sky-600"
            />
          )}
        </button>

        <button
          className={`px-6 py-3 font-medium text-sm transition-colors relative ${
            activeTab === 'announcements'
              ? 'text-sky-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('announcements')}
        >
          <div className="flex items-center gap-2">
            <Icon icon="mdi:bullhorn" className="h-5 w-5" />
            <span>Announcements</span>
          </div>
          {activeTab === 'announcements' && (
            <motion.div
              layoutId="activeTab"
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-sky-600"
            />
          )}
        </button>

        <button
          className={`px-6 py-3 font-medium text-sm transition-colors relative ${
            activeTab === 'resources'
              ? 'text-sky-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('resources')}
        >
          <div className="flex items-center gap-2">
            <Icon icon="mdi:file-document-multiple" className="h-5 w-5" />
            <span>Resources</span>
          </div>
          {activeTab === 'resources' && (
            <motion.div
              layoutId="activeTab"
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-sky-600"
            />
          )}
        </button>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'discussions' && (
        <DiscussionForum courseId={courseId} />
      )}

      {activeTab === 'announcements' && (
        <div className="text-center py-16 bg-gray-50 rounded-xl border border-gray-200">
          <Icon icon="mdi:bullhorn" className="mx-auto h-16 w-16 text-gray-300" />
          <h3 className="mt-4 text-xl font-medium text-gray-800">No announcements yet</h3>
          <p className="mt-2 text-gray-500 max-w-md mx-auto">
            Announcements from your instructors will appear here.
          </p>
        </div>
      )}

      {activeTab === 'resources' && (
        <div className="text-center py-16 bg-gray-50 rounded-xl border border-gray-200">
          <Icon icon="mdi:file-document-multiple" className="mx-auto h-16 w-16 text-gray-300" />
          <h3 className="mt-4 text-xl font-medium text-gray-800">No resources yet</h3>
          <p className="mt-2 text-gray-500 max-w-md mx-auto">
            Additional resources for this course will appear here.
          </p>
        </div>
      )}
    </div>
  );
};

export default DiscussionPage;
