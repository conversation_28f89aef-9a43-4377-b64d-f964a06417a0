import React, { useState, useEffect } from "react";
import { useStripe, useElements, CardElement } from "@stripe/react-stripe-js";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { Icon } from "@iconify/react";
import api from "@/server/api";

// Card element styling
const CARD_ELEMENT_OPTIONS = {
  style: {
    base: {
      fontSize: "16px",
      color: "#424770",
      "::placeholder": {
        color: "#aab7c4",
      },
    },
    invalid: {
      color: "#ef4444",
    },
  },
  hidePostalCode: true,
};

// Validation schema for the payment form (customer info only)
const PaymentSchema = Yup.object().shape({
  customer_name: Yup.string().required("Name is required"),
  customer_email: Yup.string().email("Invalid email").required("Email is required"),
  customer_phone: Yup.string().nullable(),
});

const DirectPaymentForm = ({ packageId, amount, currency = 'aed', onSuccess, item_id, clientSecret }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [succeeded, setSucceeded] = useState(false);
  const [message, setMessage] = useState(null);

  // Currency symbol mapping
  const currencySymbols = {
    usd: '$',
    eur: '€',
    gbp: '£',
    aed: 'AED ',
  };

  const currencySymbol = currencySymbols[currency.toLowerCase()] || '';

  useEffect(() => {
    if (!stripe) {
      return;
    }

    if (!clientSecret) {
      return;
    }

    stripe.retrievePaymentIntent(clientSecret).then(({ paymentIntent }) => {
      switch (paymentIntent.status) {
        case "succeeded":
          setMessage("Payment succeeded!");
          break;
        case "processing":
          setMessage("Your payment is processing.");
          break;
        case "requires_payment_method":
          setMessage("Please provide your payment details.");
          break;
        default:
          setMessage("Something went wrong.");
          break;
      }
    });
  }, [stripe, clientSecret]);

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    if (!stripe || !elements) {
      // Stripe.js has not yet loaded.
      setError("Please wait while we initialize the payment system.");
      setSubmitting(false);
      return;
    }

    setProcessing(true);
    setError(null);
    setMessage("Processing your payment...");

    try {
      const cardElement = elements.getElement(CardElement);

      if (!cardElement) {
        throw new Error("Card element not found");
      }

      // First create a payment method using the card element
      const { error: createError, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
      });

      if (createError) {
        throw createError;
      }

      // Prepare the payment data with the payment method ID
      const paymentData = {
        amount: amount,
        currency: currency,
        package_id: packageId,
        item_id: item_id || packageId, // Use item_id if provided, otherwise use packageId
        customer_name: values.customer_name,
        customer_email: values.customer_email,
        customer_phone: values.customer_phone,
        description: `Payment for package ${packageId}`,
        payment_method_id: paymentMethod.id // Send the payment method ID instead of the card element
      };

      // Submit payment to the backend
      const response = await api.post("make-stripe-payment", paymentData);

      if (response?.data?.status === true || response?.data?.success === true) {
        setSucceeded(true);
        resetForm();

        // Show success message for a moment before redirecting
        setTimeout(() => {
          // Redirect to payments page
          window.location.href = "/my-payments";
        }, 1500);

        // Call the onSuccess callback if provided
        if (typeof onSuccess === 'function') {
          onSuccess(response.data);
        }
      } else {
        throw new Error(response?.data?.message || "Payment failed");
      }
    } catch (err) {
      console.error("Payment error:", err);
      setError(err.message || "An unexpected error occurred. Please try again.");
    } finally {
      setProcessing(false);
      setSubmitting(false);
    }
  };

  if (succeeded) {
    return (
      <div className="bg-green-50 border border-green-200 p-6 rounded-lg">
        <div className="flex items-center">
          <Icon icon="heroicons:check-circle" className="text-green-500 text-2xl mr-2" />
          <h3 className="text-lg font-medium text-green-800">Payment Successful!</h3>
        </div>
        <p className="mt-2 text-green-600">Thank you! Your payment was processed successfully.</p>
      </div>
    );
  }

  return (
    <Formik
      initialValues={{
        customer_name: '',
        customer_email: '',
        customer_phone: '',
      }}
      validationSchema={PaymentSchema}
      onSubmit={handleSubmit}
      enableReinitialize={true}
    >
      {({ isSubmitting, touched, errors, handleSubmit: formikHandleSubmit }) => (
        <Form className="space-y-4" onSubmit={(e) => {
          e.preventDefault();
          formikHandleSubmit(e);
        }}>
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-4 text-gray-800">Payment Details</h3>

            <div className="space-y-4">
              {/* Card Element */}
              <div>
                <label htmlFor="card-element" className="block text-sm font-medium text-gray-700 mb-1">
                  Card Details <span className="text-red-500">*</span>
                </label>
                <div className="p-3 border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500">
                  <CardElement options={CARD_ELEMENT_OPTIONS} />
                </div>
              </div>

              {/* Customer Information */}
              <div>
                <label htmlFor="customer_name" className="block text-sm font-medium text-gray-700 mb-1">
                  Name on Card <span className="text-red-500">*</span>
                </label>
                <Field
                  type="text"
                  id="customer_name"
                  name="customer_name"
                  placeholder="John Doe"
                  className={`w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    touched.customer_name && errors.customer_name ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                <ErrorMessage name="customer_name" component="div" className="text-red-500 text-sm mt-1" />
              </div>

              <div>
                <label htmlFor="customer_email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email <span className="text-red-500">*</span>
                </label>
                <Field
                  type="email"
                  id="customer_email"
                  name="customer_email"
                  placeholder="<EMAIL>"
                  className={`w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    touched.customer_email && errors.customer_email ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                <ErrorMessage name="customer_email" component="div" className="text-red-500 text-sm mt-1" />
              </div>

              <div>
                <label htmlFor="customer_phone" className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number (Optional)
                </label>
                <Field
                  type="text"
                  id="customer_phone"
                  name="customer_phone"
                  placeholder="+****************"
                  className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <ErrorMessage name="customer_phone" component="div" className="text-red-500 text-sm mt-1" />
              </div>
            </div>

            {/* Payment Amount */}
            <div className="mt-6 mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-gray-700">Total Amount:</span>
                <span className="text-xl font-bold text-blue-600">{currencySymbol}{amount.toFixed(2)}</span>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="text-red-500 text-sm mb-4 flex items-center">
                <Icon icon="heroicons:exclamation-circle" className="mr-2" />
                {error}
              </div>
            )}

            {/* Message */}
            {message && !error && (
              <div className="text-blue-500 text-sm mb-4 flex items-center">
                <Icon icon="heroicons:information-circle" className="mr-2" />
                {message}
              </div>
            )}

            {/* Submit Button */}
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                formikHandleSubmit();
              }}
              disabled={isSubmitting || processing || !stripe}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {processing || isSubmitting ? (
                <>
                  <Icon icon="eos-icons:loading" className="mr-2 text-xl animate-spin" />
                  Processing...
                </>
              ) : (
                `Pay ${currencySymbol}${amount.toFixed(2)}`
              )}
            </button>

            <div className="mt-4 text-xs text-gray-500 flex items-center">
              <Icon icon="heroicons:lock-closed" className="mr-1 text-blue-500" />
              <span>Payments are secure and encrypted</span>
            </div>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default DirectPaymentForm;
