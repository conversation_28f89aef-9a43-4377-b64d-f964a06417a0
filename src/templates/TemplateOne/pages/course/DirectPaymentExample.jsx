import React from "react";
import DirectPaymentForm from "./DirectPaymentForm";
import { Icon } from "@iconify/react";
import GoBack from "@/components/ui/GoBack";

const DirectPaymentExample = () => {
  // Example course data
  const course = {
    id: 110,
    title: "Advanced Web Development",
    regular_price: 999,
    sale_price: 799,
    currency: "usd",
    discount_percentage: 20,
  };

  // Handle successful payment
  const handlePaymentSuccess = (paymentData) => {
    console.log("Payment successful:", paymentData);
    // Additional success handling if needed
  };

  return (
    <div className="py-8 bg-gray-50 min-h-screen">
      <section className="space-y-6 container mx-auto px-4 max-w-6xl">
        <GoBack title={course.title} />
        
        <div className="md:flex items-start gap-6">
          {/* Left Section: Course Details */}
          <div className="flex-1 bg-white shadow-md p-6 rounded-lg">
            <h2 className="text-xl font-bold text-gray-800 mb-6">Course Details</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Course:</span>
                <span className="font-medium">{course.title}</span>
              </div>
              <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                <span className="text-gray-600">Regular Price:</span>
                <span className="text-gray-800 line-through">${course.regular_price}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Discount:</span>
                <span className="text-red-500 font-medium">{course.discount_percentage}%</span>
              </div>
              <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                <span className="text-gray-600">Sale Price:</span>
                <span className="text-green-600 font-bold">${course.sale_price}</span>
              </div>
            </div>
          </div>
          
          {/* Right Section: Payment Form */}
          <div className="flex-1">
            <DirectPaymentForm 
              packageId={course.id}
              amount={course.sale_price}
              currency={course.currency}
              onSuccess={handlePaymentSuccess}
            />
          </div>
        </div>
      </section>
    </div>
  );
};

export default DirectPaymentExample;
