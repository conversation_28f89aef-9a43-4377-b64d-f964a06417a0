import React from "react";
import { Icon } from "@iconify/react";

const CoursePaymentSuccess = () => {
  return (
    <div className="container mx-auto p-10 flex flex-col items-center justify-center">
      {/* Success Icon */}
      <Icon icon="mdi:check-circle" className="text-9xl text-green-500" />

      {/* Title */}
      <h1 className="text-4xl font-extrabold text-gray-800 ">Enrollment Successful!</h1>

      {/* Subtext */}
      <p className="text-lg mt-4 text-center text-gray-600">
        Thank you for enrolling in our course. Your payment has been successfully received.
      </p>

      {/* Additional Info */}
      <p className="text-lg mt-2 text-center text-gray-600">
        Your enrollment is under review and will be approved shortly. Once approved, you'll have access to all course materials.
      </p>

      {/* Instructions */}
      <div className="mt-8 bg-white p-6 rounded-lg shadow-md w-full max-w-lg">
        <h2 className="text-xl font-semibold text-gray-700">What's Next?</h2>
        <ul className="list-disc list-inside mt-4 text-gray-600 space-y-2">
          <li>
            Check your email for confirmation and updates about your enrollment status.
          </li>
          <li>
            Log in to your account to monitor your enrollment progress.
          </li>
          <li>
            If you have any questions, feel free to <a href="/contact" className="text-blue-500 underline">contact us</a>.
          </li>
        </ul>
      </div>

      {/* Go Back or Explore More */}
      <div className="flex space-x-4 mt-10">
        <a
          href="/dashboard"
          className="px-6 py-3 bg-sky-600 text-white rounded-full hover:bg-sky-700 transition"
        >
          Go to Dashboard
        </a>
        <a
          href="/courses"
          className="px-6 py-3 bg-gray-200 text-gray-700 rounded-full hover:bg-gray-300 transition"
        >
          Explore More Courses
        </a>
      </div>
    </div>
  );
};

export default CoursePaymentSuccess;
