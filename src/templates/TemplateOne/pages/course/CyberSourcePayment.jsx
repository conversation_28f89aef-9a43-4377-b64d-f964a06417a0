import React, { useState, useEffect } from "react";
import { Formik, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { Icon } from "@iconify/react";
import api from "@/server/api";

// Dynamic validation schema for CyberSource payment form
const createCyberSourcePaymentSchema = (useFallback) => Yup.object().shape({
  customer_name: Yup.string().required("Name is required"),
  customer_email: Yup.string().email("Invalid email").required("Email is required"),
  customer_phone: Yup.string().nullable(),
  card_exp_month: Yup.number()
    .required("Expiry month is required")
    .min(1, "Month must be between 1-12")
    .max(12, "Month must be between 1-12"),
  card_exp_year: Yup.number()
    .required("Expiry year is required")
    .min(new Date().getFullYear(), "Year cannot be in the past")
    .max(new Date().getFullYear() + 20, "Year is too far in the future"),
  // Add card fields validation only for fallback mode
  ...(useFallback && {
    card_number: Yup.string()
      .required("Card number is required")
      .matches(/^\d{13,19}$/, "Card number must be 13-19 digits"),
    card_cvc: Yup.string()
      .required("CVC is required")
      .matches(/^\d{3,4}$/, "CVC must be 3-4 digits"),
  })
});

const CyberSourcePayment = ({
  paymentId,
  clientSecret,
  credentials,
  onSuccess,
  courseId,
  currency,
  amount,
  coupon
}) => {
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [succeeded, setSucceeded] = useState(false);
  const [message, setMessage] = useState(null);
  const [microform, setMicroform] = useState(null);
  const [flexInitialized, setFlexInitialized] = useState(false);
  const [useFallback, setUseFallback] = useState(false);

  // Refs for microform fields
  const cardNumberRef = useRef(null);
  const cvvRef = useRef(null);

  // Currency symbol mapping
  const currencySymbols = {
    usd: '$',
    eur: '€',
    gbp: '£',
    aed: 'AED ',
    bdt: '৳',
  };

  const currencySymbol = currencySymbols[currency?.toLowerCase()] || '';

  // Initialize CyberSource Flex Microform
  useEffect(() => {
    if (clientSecret && !flexInitialized && window.Flex) {
      initializeFlex();
    }
  }, [clientSecret, flexInitialized]);

  const initializeFlex = async () => {
    try {
      setMessage("Initializing secure payment form...");

      // Get token from backend
      const tokenResponse = await api.get('/cybersource/token');
      const { keyId, jwt } = tokenResponse.data;

      if (!keyId || !jwt) {
        throw new Error('Failed to get CyberSource token');
      }

      // Initialize Flex
      const flex = new window.Flex({ keyId, jwt });

      // Create microform with styling
      const microformInstance = flex.microform({
        styles: {
          input: {
            fontSize: '16px',
            fontFamily: 'system-ui, -apple-system, sans-serif',
            color: '#374151',
            padding: '12px',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: '#ffffff',
          },
          ':focus': {
            borderColor: '#3b82f6',
            outline: 'none',
            boxShadow: '0 0 0 2px rgba(59, 130, 246, 0.1)',
          },
          ':invalid': {
            borderColor: '#ef4444',
          },
          '::placeholder': {
            color: '#9ca3af',
          }
        }
      });

      // Create secure fields
      const numberField = microformInstance.createField('number', {
        placeholder: 'Card Number'
      });
      const cvvField = microformInstance.createField('securityCode', {
        placeholder: 'CVV'
      });

      // Load fields into DOM
      numberField.load('#card-number-field');
      cvvField.load('#cvv-field');

      setMicroform(microformInstance);
      setFlexInitialized(true);
      setMessage("Please provide your payment details.");

      console.log("CyberSource Flex initialized successfully");
    } catch (err) {
      console.error("Flex initialization error:", err);
      setError("Failed to initialize secure payment form. Please try again.");
    }
  };

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    if (!microform) {
      setError("Payment form not initialized. Please refresh and try again.");
      setSubmitting(false);
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      // Create token using CyberSource Flex
      const tokenData = {
        cardExpirationMonth: values.card_exp_month.toString().padStart(2, '0'),
        cardExpirationYear: values.card_exp_year.toString()
      };

      console.log("Creating CyberSource token with:", tokenData);

      microform.createToken(tokenData, async (err, response) => {
        if (err) {
          console.error("Tokenization error:", err);
          setError(`Tokenization failed: ${err.message}`);
          setProcessing(false);
          setSubmitting(false);
          return;
        }

        try {
          console.log("Token created successfully:", response.token);

          // Prepare payment data for CyberSource with token
          const paymentData = {
            // Payment processing options
            transaction_id: paymentId,
            is_confirm_payment: true,

            // Course information
            item_id: courseId,
            description: `Course payment for course ID: ${courseId}`,

            // Payment information
            amount: amount,
            currency: currency,
            coupon: coupon || "",

            // Customer information
            customer_name: values.customer_name,
            customer_email: values.customer_email,
            customer_phone: values.customer_phone || "",

            // CyberSource token instead of raw card data
            token: response.token,
            card_exp_month: values.card_exp_month,
            exp_month: values.card_exp_month,
            card_exp_year: values.card_exp_year,
            exp_year: values.card_exp_year,
          };

          console.log("Submitting CyberSource payment with token:", paymentData);

          // Submit payment to the backend
          const apiResponse = await api.post("make-cybersource-payment", paymentData);

          if (apiResponse?.data?.status === true || apiResponse?.data?.success === true) {
            setSucceeded(true);
            resetForm();

            // Call the onSuccess callback if provided
            if (typeof onSuccess === 'function') {
              onSuccess(apiResponse.data);
            }
          } else {
            throw new Error(apiResponse?.data?.message || "Payment failed");
          }
        } catch (err) {
          console.error("CyberSource payment error:", err);
          setError(err.message || "An unexpected error occurred. Please try again.");
        } finally {
          setProcessing(false);
          setSubmitting(false);
        }
      });
    } catch (err) {
      console.error("Payment submission error:", err);
      setError(err.message || "An unexpected error occurred. Please try again.");
      setProcessing(false);
      setSubmitting(false);
    }
  };

  if (succeeded) {
    return (
      <div className="bg-green-50 border border-green-200 p-6 rounded-lg">
        <div className="flex items-center">
          <Icon icon="heroicons:check-circle" className="text-green-500 text-2xl mr-2" />
          <h3 className="text-lg font-medium text-green-800">Payment Successful!</h3>
        </div>
        <p className="mt-2 text-green-600">Thank you! Your payment was processed successfully.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-800">CyberSource Payment</h3>
      </div>

      <Formik
        initialValues={{
          customer_name: "",
          customer_email: "",
          customer_phone: "",
          card_exp_month: "",
          card_exp_year: "",
          ...(useFallback && {
            card_number: "",
            card_cvc: "",
          })
        }}
        validationSchema={CyberSourcePaymentSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting }) => (
          <div className="space-y-4">
            <div className="bg-white">
              <div className="space-y-4">
                {/* Secure Card Fields */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Details <span className="text-red-500">*</span>
                  </label>
                  <div className="space-y-3">
                    {/* Secure Card Number Field */}
                    <div>
                      <div
                        id="card-number-field"
                        className="w-full min-h-[48px] border border-gray-300 rounded-lg"
                        style={{ minHeight: '48px' }}
                      />
                      {!flexInitialized && (
                        <div className="text-gray-500 text-sm mt-1">Loading secure card field...</div>
                      )}
                    </div>

                    <div className="grid grid-cols-3 gap-3">
                      {/* Expiry Month */}
                      <div>
                        <Field
                          as="select"
                          name="card_exp_month"
                          className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">MM</option>
                          {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                            <option key={month} value={month}>
                              {month.toString().padStart(2, '0')}
                            </option>
                          ))}
                        </Field>
                        <ErrorMessage name="card_exp_month" component="div" className="text-red-500 text-sm mt-1" />
                      </div>

                      {/* Expiry Year */}
                      <div>
                        <Field
                          as="select"
                          name="card_exp_year"
                          className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">YYYY</option>
                          {Array.from({ length: 21 }, (_, i) => new Date().getFullYear() + i).map(year => (
                            <option key={year} value={year}>
                              {year}
                            </option>
                          ))}
                        </Field>
                        <ErrorMessage name="card_exp_year" component="div" className="text-red-500 text-sm mt-1" />
                      </div>

                      {/* Secure CVV Field */}
                      <div>
                        <div
                          id="cvv-field"
                          className="w-full min-h-[48px] border border-gray-300 rounded-lg"
                          style={{ minHeight: '48px' }}
                        />
                        {!flexInitialized && (
                          <div className="text-gray-500 text-sm mt-1">Loading CVV field...</div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Customer Information */}
                <div>
                  <label htmlFor="customer_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Name on Card <span className="text-red-500">*</span>
                  </label>
                  <Field
                    type="text"
                    name="customer_name"
                    placeholder="John Doe"
                    className={`w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      touched.customer_name && errors.customer_name ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  <ErrorMessage name="customer_name" component="div" className="text-red-500 text-sm mt-1" />
                </div>

                <div>
                  <label htmlFor="customer_email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <Field
                    type="email"
                    name="customer_email"
                    placeholder="<EMAIL>"
                    className={`w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      touched.customer_email && errors.customer_email ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  <ErrorMessage name="customer_email" component="div" className="text-red-500 text-sm mt-1" />
                </div>

                <div>
                  <label htmlFor="customer_phone" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number (Optional)
                  </label>
                  <Field
                    type="tel"
                    name="customer_phone"
                    placeholder="+****************"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <ErrorMessage name="customer_phone" component="div" className="text-red-500 text-sm mt-1" />
                </div>
              </div>

              {/* Payment Amount */}
              <div className="mt-6 mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Total Amount:</span>
                  <span className="text-xl font-bold text-blue-600">{currencySymbol}{amount}</span>
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="text-red-500 text-sm mb-4 flex items-center">
                  <Icon icon="heroicons:exclamation-circle" className="mr-2" />
                  {error}
                </div>
              )}

              {/* Message */}
              {message && !error && (
                <div className="text-blue-500 text-sm mb-4 flex items-center">
                  <Icon icon="heroicons:information-circle" className="mr-2" />
                  {message}
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={processing || isSubmitting}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {processing || isSubmitting ? (
                  <>
                    <Icon icon="eos-icons:loading" className="mr-2 text-xl animate-spin" />
                    Processing...
                  </>
                ) : (
                  `Pay ${currencySymbol}${amount}`
                )}
              </button>

              <div className="mt-4 text-xs text-gray-500 flex items-center">
                <Icon icon="heroicons:lock-closed" className="mr-1 text-blue-500" />
                <span>Payments are secure and encrypted</span>
              </div>
            </div>
          </div>
        )}
      </Formik>
    </div>
  );
};

export default CyberSourcePayment;
