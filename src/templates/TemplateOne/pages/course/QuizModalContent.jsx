import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { useNavigate, use<PERSON>ara<PERSON>, Link } from "react-router-dom";
import useFetch from "@/hooks/useFetch";
import api from "@/server/api";
import Modal from "@/components/ui/Modal";
import Loading from "@/components/Loading";
import { useSelector } from "react-redux";

const QuizModalContent = ({ data, onClose, activeModal }) => {
  const navigate = useNavigate();
  const { isAuth } = useSelector((state) => state.auth);

  const {
    data: contentDetails,
    isLoading,
  } = useFetch({
    queryKey: `content-details/${data.id}`,
    endPoint: `get-content-details?id=${data.id}`,
    dependencies: [data.id],
  });

  const content = contentDetails?.data;

  const handleStartQuiz = () => {
    // if (content.is_free) {
    //   navigate(`/quiz/${content.element_id}/${content.course_id}/${content.id}`);
    //   return;
    // } else {
      api
      .post("start-quiz", { course_id: content.course_id, chapter_quiz_id: content.element_id })
      .then((response) => {
        let result = response?.data?.data;
        console.log(result);
        if (response.data.status) {
          navigate(
            `/quiz/${result.chapter_quiz_id}/${result.course_id}/${result.id}`
          );
        }
      })
      .catch((error) => {
        console.error("Failed to start quiz:", error);
      });
    // }
    // navigate(`/quiz/${data.chapter_quiz_id}/${id}/${data.id}`);
  };
  // const { data: quizdetails, isLoading, isError } = useFetch({ queryKey: `quizStart`, endPoint: `quiz-start-details/${data.chapter_quiz_id}` });
  if (isLoading) return <Loading />;

  return (
    <div>
      <Modal onClose={onClose} activeModal={activeModal} className="max-w-3xl">
        {!isAuth ? (
          // UI for non-logged in users
          <div className="flex flex-col items-center w-full gap-6 mx-auto py-6">
            <div className="flex flex-col items-center mb-2 text-center">
              <Icon
                icon="mdi:account-lock-outline"
                className="text-yellow-500 text-7xl mb-4"
              />
              <h2 className="text-2xl font-semibold text-gray-800 mb-2">
                Sign Up to Access Course Content
              </h2>
              <p className="text-gray-600 max-w-md mb-4">
                Please sign up or log in to view and participate in this course's content, including quizzes, assignments, and learning materials.
              </p>
            </div>

            <div className="bg-blue-50 rounded-lg p-6 w-full max-w-md border border-blue-200">
              <h3 className="font-medium text-lg text-blue-700 mb-3">Benefits of Enrolling:</h3>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <Icon icon="mdi:check-circle" className="text-green-500 text-xl mt-0.5" />
                  <span>Access to all course materials and quizzes</span>
                </li>
                <li className="flex items-start gap-2">
                  <Icon icon="mdi:check-circle" className="text-green-500 text-xl mt-0.5" />
                  <span>Track your progress through the course</span>
                </li>
                <li className="flex items-start gap-2">
                  <Icon icon="mdi:check-circle" className="text-green-500 text-xl mt-0.5" />
                  <span>Earn certificates upon completion</span>
                </li>
                <li className="flex items-start gap-2">
                  <Icon icon="mdi:check-circle" className="text-green-500 text-xl mt-0.5" />
                  <span>Interact with instructors and other students</span>
                </li>
              </ul>
            </div>

            <div className="flex gap-4 mt-2">
              <Link
                to="/login"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium flex items-center"
              >
                <Icon icon="mdi:login" className="mr-2 text-xl" />
                Log In
              </Link>
              <Link
                to="/login"
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium flex items-center"
              >
                <Icon icon="mdi:account-plus" className="mr-2 text-xl" />
                Sign Up
              </Link>
            </div>
          </div>
        ) : (
          // UI for logged in users
          <div className="flex flex-col items-center w-full gap-2 mx-auto">
            <div className="flex flex-col items-center mb-4 bg-blue-50 rounded-lg shadow-lg w-full p-5">
              <Icon
                icon="fluent:clipboard-task-list-rtl-24-regular"
                className="text-sky-600 text-5xl mb-2"
              />
              <h2 className="text-2xl font-semibold text-sky-700">
                {data?.title}
              </h2>
            </div>

            <div className="rounded-lg p-4 bg-white border border-blue-200 border-dashed w-full mb-6">
              <h3 className="text-center text-lg text-sky-700 mb-4">
                Exam Summary
              </h3>

              <div className="flex max-sm:flex-wrap justify-between items-center text-gray-700 text-sm mb-2">
                <div className="flex items-start gap-2">
                  <Icon
                    icon="bi:question-circle-fill"
                    className="text-blue-600 text-2xl mb-1"
                  />
                  <span>
                    <p className="text-md">No. of Questions</p>
                    <span className="font-bold text-lg">
                      {content?.quiz?.number_of_question}
                    </span>
                  </span>
                </div>

                <div className="flex items-start gap-2">
                  <Icon
                    icon="mdi:book-edit-outline"
                    className="text-green-600 text-2xl mb-1"
                  />
                  <span>
                    <p>Positive Mark</p>
                    <span className="font-bold text-lg">
                      {content?.quiz?.positive_mark}
                    </span>
                  </span>
                </div>

                <div className="flex items-start gap-2">
                  <Icon
                    icon="mdi:star-circle"
                    className="text-yellow-600 text-2xl mb-1"
                  />
                  <span>
                    <p>Total Marks</p>
                    <span className="font-bold text-lg">
                      {content?.quiz?.total_mark}
                    </span>
                  </span>
                </div>

                <div className="flex items-start gap-2">
                  <Icon
                    icon="material-symbols:timer-outline"
                    className="text-orange-600 text-2xl mb-1"
                  />
                  <span>
                    <p>Total Time</p>
                    <span className="font-bold text-lg">
                      {content?.quiz?.duration} Minutes
                    </span>
                  </span>
                </div>
              </div>
            </div>

            <div className="w-full text-gray-800">
              <h4 className="text-sky-700 text-2xl mb-2">Instruction</h4>
              <p>{content?.quiz?.description}</p>
            </div>

            <button
              onClick={handleStartQuiz}
              className="mt-6 bg-sky-600 text-white text-lg py-2 px-6 rounded-lg flex items-center"
            >
              Start Now
              <Icon icon="lucide:arrow-right" className="ml-2 text-2xl" />
            </button>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default QuizModalContent;
