import { Icon } from "@iconify/react/dist/iconify.js";
import React, { useEffect, useRef, useState } from "react";
import { Link } from "react-router-dom";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import SwiperCore, { Autoplay, Navigation } from "swiper";
// import "./swiper.css";
import { ASSET_URL } from "@/config";
import schoolIcon from "@/assets/images/svg/school.svg";
import Course from "../home/<USER>";

SwiperCore.use([Autoplay, Navigation]);

const RelatedCoures = ({ courses = [], title = "Recommended Coures" }) => {
  const swiperRef = useRef(null);
  const [isPrevDisabled, setPrevDisabled] = useState(true);
  const [isNextDisabled, setNextDisabled] = useState(false);
  //   const [selectedCategory, setSelectedCategory] = useState(
  //     courses?.sub_categories && courses.sub_categories.length > 0
  //       ? courses.sub_categories[0].id
  //       : null
  //   );

  //   console.log(courses);

  // Update button states based on current index and total slides
  const updateButtonStates = () => {
    if (!swiperRef.current) return; // Check if swiperRef is initialized

    const swiper = swiperRef.current;
    const currentIndex = swiper.activeIndex;
    const totalSlides = swiper.slides.length;
    const slidesPerView = swiper.params.slidesPerView;

    // Disable the previous button if at the start
    setPrevDisabled(currentIndex === 0);

    // Disable the next button if we're at or beyond the last viewable slide
    setNextDisabled(currentIndex >= totalSlides - slidesPerView);
  };

  useEffect(() => {
    if (!swiperRef.current) return;

    const swiper = swiperRef.current;
    // Initial update
    updateButtonStates();

    // Add event listener
    swiper.on("slideChange", updateButtonStates);

    // Cleanup on unmount
    return () => {
      if (swiper) swiper.off("slideChange", updateButtonStates);
    };
  }, [courses?.length]);
  // console.log(courses);

  return (
    <div>
      <div className="container space-y-5 relative text-center mx-auto">
        <h2 className="text-4xl text-sky-700 max-sm:text-3xl">
          {courses?.name}
        </h2>
        {courses?.length > 0 && (
          <>
            <div className="relative space-y-5 lg:space-y-10">
              <div className="flex justify-between items-center">
                <h2 className="text-xl text-sky-700 max-sm:text-3xl text-start">
                  {title}
                </h2>
                <Link
                  className="hover:bg-sky-50 py-2 w-24 hover:w-28 justify-center rounded-lg border border-sky-600 text-sky-600 flex items-center group gap-1 hover:gap-3 transition-all duration-300"
                  to="/courses"
                >
                  See All
                  <Icon
                    icon="line-md:arrow-right"
                    className="text-lg transition-all duration-300 transform"
                  />
                </Link>
              </div>
              <Swiper
                onSwiper={(swiper) => {
                  swiperRef.current = swiper; // Set the swiperRef directly
                  updateButtonStates(); // Update buttons initially after ref is set
                }}
                slidesPerView={4}
                spaceBetween={30}
                breakpoints={{
                  320: { slidesPerView: 1 },
                  640: { slidesPerView: 2 },
                  768: { slidesPerView: 3 },
                  1024: { slidesPerView: 3 },
                }}
              >
                <div className="mx-auto">
                  {courses?.length > 0 &&
                    courses?.map((item, index) => (
                      <SwiperSlide
                        key={index}
                        className="flex flex-col items-center pb-5"
                        // onClick={() => setSelectedCategory(item?.id)}
                      >
                        <Course course={item} />
                      </SwiperSlide>
                    ))}
                </div>
              </Swiper>

              <div
                className={`container absolute top-16 ${
                  courses?.length > 3 ? "" : "xl:hidden"
                }`}
              >
                <button
                  className={`absolute xl:-left-20 md:-left-0 max-sm:-left-5 top-1/2 transform -translate-y-1/2 p-3 bg-white rounded-full shadow-lg z-10 ${
                    isPrevDisabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  onClick={() => swiperRef.current.slidePrev()}
                  disabled={isPrevDisabled}
                >
                  <Icon
                    icon="ic:round-arrow-back"
                    className="text-2xl text-sky-600"
                  />
                </button>
                <button
                  className={`absolute xl:-right-20 md:-right-2 max-sm:-right-5 top-1/2 transform -translate-y-1/2 p-3 bg-white rounded-full shadow-lg z-10 ${
                    isNextDisabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  onClick={() => swiperRef.current.slideNext()}
                  disabled={isNextDisabled}
                >
                  <Icon
                    icon="ic:round-arrow-forward"
                    className="text-2xl text-sky-600"
                  />
                </button>
              </div>
            </div>
            {/* <div className="border-b-2 border-dashed border-gray-300"></div> */}
          </>
        )}

        {/* {courses?.sub_categories[selectedCategory]?.courses?.map((course, idx) => ( */}

        {/* {filteredItem?.map((item, idx) => (
          <div key={idx}>
            <div className="flex justify-between items-center">
              <h2 className="text-4xl text-sky-700 max-sm:text-3xl text-start">
                {item?.name}
              </h2>
              <Link
                className="hover:bg-sky-50 py-2 w-24 hover:w-28 justify-center rounded-lg border border-sky-600 text-sky-600 flex items-center group gap-1 hover:gap-3 transition-all duration-300"
                to="/course-list"
              >
                See All
                <Icon
                  icon="line-md:arrow-right"
                  className="text-lg transition-all duration-300 transform"
                />
              </Link>
            </div>

            {item?.courses?.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 pt-6">
                {item?.courses?.slice(0, 3)?.map((course, idx) => (
                  <Course key={idx} course={course} />
                ))}
              </div>
            ) : (
              <p className="my-20">No Course available</p>
            )}
          </div>
        ))} */}
      </div>
      {/* ))} */}

      {/* {console.log(courses?.sub_categories[selectedCategory])} */}
    </div>
  );
};

export default RelatedCoures;
