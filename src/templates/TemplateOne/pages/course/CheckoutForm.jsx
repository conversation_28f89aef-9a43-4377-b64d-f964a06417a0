import React, { useEffect, useState } from "react";
import { useStripe, useElements, CardElement } from "@stripe/react-stripe-js";
import { Icon } from "@iconify/react";
import api from "@/server/api";

const CheckoutForm = ({ clientSecret, onSuccess }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [processing, setProcessing] = useState(false);
  const [paymentIntent, setPaymentIntent] = useState(null);
  const [error, setError] = useState(null);
  const [succeeded, setSucceeded] = useState(false);

  useEffect(() => {
    if (clientSecret && stripe) {
      stripe.retrievePaymentIntent(clientSecret).then(({ paymentIntent }) => {
        setPaymentIntent(paymentIntent);
      });
    }
  }, [clientSecret, stripe]);

  const handleSubmit = async (e) => {
    e.preventDefault(); // Prevent default form submission behavior
    
    if (!stripe || !elements) {
      setError("Stripe is not ready.");
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      const cardElement = elements.getElement(CardElement);
      if (!cardElement) throw new Error("CardElement not found.");

      const { error: stripeError, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
        },
      });

      if (stripeError) throw stripeError;

      if (paymentIntent.status === "succeeded") {
        try {
          const response = await api.post("verify-payment", {
            paymentIntentId: paymentIntent.id,
          });

          if (response?.data?.status === true) {
            setSucceeded(true);
            onSuccess(paymentIntent);
          } else {
            throw new Error("Payment verified but failed on server.");
          }
        } catch (verifyError) {
          console.error("Verification error:", verifyError);
          throw new Error("Payment verified but failed on server.");
        }
      } else {
        setError("Payment was not successful.");
      }
    } catch (err) {
      console.error("Stripe error:", err);
      setError(err.message || "An unexpected error occurred.");
    } finally {
      setProcessing(false);
    }
  };

  if (succeeded) {
    return (
      <div className="bg-green-50 border border-green-200 p-6 rounded-lg">
        <div className="flex items-center">
          <Icon icon="heroicons:check-circle" className="text-green-500 text-2xl mr-2" />
          <h3 className="text-lg font-medium text-green-800">Payment Successful!</h3>
        </div>
        <p className="mt-2 text-green-600">Thank you! Your payment was processed successfully.</p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-xl font-semibold mb-4 text-gray-800">Payment Details</h3>

        <div className="mb-6">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: "16px",
                  color: "#424770",
                  "::placeholder": {
                    color: "#aab7c4",
                  },
                },
                invalid: {
                  color: "#ef4444",
                },
              },
              hidePostalCode: true,
            }}
          />
        </div>

        {error && (
          <div className="text-red-500 text-sm mb-4 flex items-center">
            <Icon icon="heroicons:exclamation-circle" className="mr-2" />
            {error}
          </div>
        )}

        <button
          type="submit"
          disabled={!stripe || processing}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
        >
          {processing ? (
            <>
              <Icon icon="eos-icons:loading" className="mr-2 text-xl animate-spin" />
              Processing...
            </>
          ) : (
            `Pay ${paymentIntent ? `$${(paymentIntent.amount / 100).toFixed(2)}` : ""}`
          )}
        </button>

        <div className="mt-4 text-xs text-gray-500 flex items-center">
          <Icon icon="heroicons:lock-closed" className="mr-1 text-blue-500" />
          <span>Payments are secure and encrypted</span>
        </div>
      </div>
    </form>
  );
};

export default CheckoutForm;