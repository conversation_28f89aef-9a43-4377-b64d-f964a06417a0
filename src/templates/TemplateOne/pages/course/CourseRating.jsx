import React, { useState } from "react";
import { useParams } from "react-router-dom";
import api from "@/server/api";
import { toast } from "react-toastify";
import { FaStar } from "react-icons/fa";

const CourseRating = ({ ratings, course }) => {
  const { id } = useParams();
  const [rating, setRating] = useState(0);
  const [review, setReview] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await api.post(`course-rating`, {
        course_id: course.id,
        rating,
        review,
      });
      setIsSubmitting(false);
      setRating(0);
      setReview("");
    } catch (error) {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6 bg-white shadow-lg rounded-xl">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">Rate This Course</h2>

      {/* Rating Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Star Rating */}
        <div className="flex items-center space-x-2">
          <span className="text-gray-600">Your Rating:</span>
          {[1, 2, 3, 4, 5].map((num) => (
            <FaStar
              key={num}
              className={`cursor-pointer text-xl ${
                rating >= num ? "text-yellow-400" : "text-gray-300"
              }`}
              onClick={() => setRating(num)}
            />
          ))}
        </div>

        {/* Review Input */}
        <textarea
          id="review"
          value={review}
          onChange={(e) => setReview(e.target.value)}
          className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400"
          placeholder="Write your review here..."
          rows={3}
        />

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 rounded-lg transition"
        >
          {isSubmitting ? "Submitting..." : "Submit Review"}
        </button>
      </form>

      {/* Ratings List */}
      {ratings?.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-3">User Reviews</h3>
          <div className="space-y-4">
            {ratings.map((r) => (
              <div key={r.id} className="bg-gray-100 p-4 rounded-lg shadow">
                <div className="flex items-center space-x-2 mb-1">
                  {[...Array(5)].map((_, index) => (
                    <FaStar
                      key={index}
                      className={`text-lg ${
                        index < r.rating ? "text-yellow-400" : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <p className="text-gray-800">{r.review || "No review provided."}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CourseRating;
