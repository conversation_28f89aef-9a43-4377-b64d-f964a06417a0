import { Icon } from "@iconify/react/dist/iconify.js";
import React, { useRef, useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import SwiperCore, { Autoplay, Navigation } from "swiper";
import { ASSET_URL } from "@/config";
import mentorImg from "@/assets/images/all-img/mentor.png";

SwiperCore.use([Autoplay, Navigation]);

const Mentors = ({ mentors, sectionTitle }) => {
  const swiperRef = useRef(null);
  const [isPrevDisabled, setPrevDisabled] = useState(true);
  const [isNextDisabled, setNextDisabled] = useState(false);

  useEffect(() => {
    const swiper = swiperRef.current?.swiper;
    if (!swiper) return;

    const updateButtonStates = () => {
      const currentIndex = swiper.activeIndex;
      const totalSlides = swiper.slides.length;
      const slidesPerView = swiper.params.slidesPerView;

      setPrevDisabled(currentIndex === 0);
      setNextDisabled(currentIndex >= totalSlides - slidesPerView);
    };

    swiper.on("slideChange", updateButtonStates);
    updateButtonStates();

    return () => {
      swiper.off("slideChange", updateButtonStates);
    };
  }, []);

  // const mentors = mentorsInfo?.map(item => item?.mentor)
  // console.log(mentors)

  return (
    <div className="z-30 flex items-center h-full">
      <div className="container space-y-5 relative">
        <div>
          <div className="flex justify-between items-center">
            <h2 className="text-xl text-sky-700 max-sm:text-3xl text-start mb-0">
              {sectionTitle}
            </h2>
          </div>

          {!mentors || mentors.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 bg-white/80 rounded-xl shadow-md mt-8 p-8">
              <div className="bg-blue-100 p-4 rounded-full mb-4">
                <Icon icon="mdi:account-school" className="text-5xl text-sky-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">No Instructors Assigned Yet</h3>
              <p className="text-gray-600 text-center max-w-lg">
                Instructors for this course have not been assigned yet. Check back later for updates on who will be teaching this course.
              </p>
            </div>
          ) : (
            <div className="relative py-5 overflow-visible">
              <Swiper
                ref={swiperRef}
                slidesPerView={4} // Adjust this based on your layout needs
                spaceBetween={20}
                navigation={false}
                breakpoints={{
                  320: { slidesPerView: 1 }, // 1 item on mobile
                  640: { slidesPerView: 2 }, // 2 items on small screens
                  768: { slidesPerView: 3 }, // 3 items on medium screens
                  1024: { slidesPerView: 4 }, // 4 items on large screens
                }}
              >
                {mentors?.map((mentor, idx) => (
                  <SwiperSlide
                    key={idx}
                    className="flex flex-col items-center p-4 border border-sky-500 rounded-xl bg-[#082840] relative overflow-visible mt-14"
                  >
                    <img
                      src={mentor?.image ? ASSET_URL + mentor.image : mentorImg}
                      alt={mentor.name}
                      className="w-20 h-20 object-cover rounded-full absolute left-4 -top-12 mb-3 z-10"
                      style={{
                        filter: "drop-shadow(0 0 4px rgba(255, 255, 255, 0.7))",
                      }}
                    />
                    <div className="w-full p-4 bg-gray-700 rounded-lg shadow-lg mt-6">
                      <h3 className="text-xl font-semibold text-white">
                        {mentor?.designation
                          ? mentor.designation?.length > 22
                            ? `${mentor.designation.slice(0, 23)}...`
                            : mentor.designation
                          : mentor?.name}
                      </h3>
                      { mentor?.profession && <p className="text-gray-100">{mentor?.profession}</p> }
                      <p className="text-gray-100">
                        {mentor?.education ? mentor.education : mentor?.institute}
                      </p>
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>

              {/* Custom Navigation Buttons */}
              <div
                className={`container absolute top-32 ${
                  mentors?.length > 4 ? "" : "xl:hidden"
                }`}
              >
                <button
                  className={`absolute -left-20 z-10 bg-gray-600 rounded-full shadow-xl p-3 transition ${
                    isPrevDisabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  onClick={() => {
                    if (!isPrevDisabled && swiperRef.current?.swiper) {
                      swiperRef.current.swiper.slidePrev();
                    }
                  }}
                  disabled={isPrevDisabled}
                >
                  <Icon
                    icon="ic:round-arrow-back"
                    className="text-2xl text-sky-600"
                  />
                </button>
                <button
                  className={`absolute -right-20 text-sky-600 z-10 bg-gray-600 rounded-full shadow-xl p-3 transition ${
                    isNextDisabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  onClick={() => {
                    if (!isNextDisabled && swiperRef.current?.swiper) {
                      swiperRef.current.swiper.slideNext();
                    }
                  }}
                  disabled={isNextDisabled}
                >
                  <Icon icon="ic:round-arrow-forward" className="text-2xl" />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Mentors;
