import { Icon } from "@iconify/react/dist/iconify.js";
import React, { useRef, useState, useEffect } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import SwiperCore, { Autoplay, Navigation } from "swiper";
import { ASSET_URL } from "@/config";
import mentorImg from "@/assets/images/all-img/mentor.png";

SwiperCore.use([Autoplay, Navigation]);

const Mentors = ({ mentors, sectionTitle }) => {
  const swiperRef = useRef(null);
  const [isPrevDisabled, setPrevDisabled] = useState(true);
  const [isNextDisabled, setNextDisabled] = useState(false);

  useEffect(() => {
    const swiper = swiperRef.current?.swiper;
    if (!swiper) return;

    const updateButtonStates = () => {
      const currentIndex = swiper.activeIndex;
      const totalSlides = swiper.slides.length;
      const slidesPerView = swiper.params.slidesPerView;

      setPrevDisabled(currentIndex === 0);
      setNextDisabled(currentIndex >= totalSlides - slidesPerView);
    };

    swiper.on("slideChange", updateButtonStates);
    updateButtonStates();

    return () => {
      swiper.off("slideChange", updateButtonStates);
    };
  }, []);

  return (
    <section className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4 md:mb-0">
            {sectionTitle}
          </h2>
          {mentors?.length > 4 && (
            <div className="flex space-x-3">
              <button
                className={`w-9 h-9 flex items-center justify-center rounded-full border border-gray-300 bg-white shadow-sm transition-colors ${
                  isPrevDisabled ? "opacity-40 cursor-default" : "hover:bg-gray-50"
                }`}
                onClick={() => !isPrevDisabled && swiperRef.current?.swiper.slidePrev()}
                disabled={isPrevDisabled}
                aria-label="Previous mentors"
              >
                <Icon icon="ph:caret-left" className="text-lg text-gray-600" />
              </button>
              <button
                className={`w-9 h-9 flex items-center justify-center rounded-full border border-gray-300 bg-white shadow-sm transition-colors ${
                  isNextDisabled ? "opacity-40 cursor-default" : "hover:bg-gray-50"
                }`}
                onClick={() => !isNextDisabled && swiperRef.current?.swiper.slideNext()}
                disabled={isNextDisabled}
                aria-label="Next mentors"
              >
                <Icon icon="ph:caret-right" className="text-lg text-gray-600" />
              </button>
            </div>
          )}
        </div>

        {!mentors || mentors.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 px-6 bg-white rounded-lg border border-gray-200">
            <div className="bg-blue-50 p-4 rounded-full mb-4">
              <Icon icon="mdi:account-tie" className="text-3xl text-blue-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-800 mb-2">No Instructors Assigned</h3>
            <p className="text-gray-500 text-sm text-center max-w-md">
              Our instructors will be announced soon
            </p>
          </div>
        ) : (
          <div className="relative">
            <Swiper
              ref={swiperRef}
              slidesPerView={4}
              spaceBetween={24}
              navigation={false}
              autoplay={{ delay: 5000, disableOnInteraction: true }}
              breakpoints={{
                320: { slidesPerView: 1.2, spaceBetween: 16 },
                640: { slidesPerView: 2.2, spaceBetween: 20 },
                768: { slidesPerView: 3, spaceBetween: 20 },
                1024: { slidesPerView: 4, spaceBetween: 24 },
              }}
            >
              {mentors.map((mentor, idx) => (
                <SwiperSlide key={idx}>
                  <div className="h-full flex flex-col items-center bg-white rounded-lg border border-gray-200 overflow-hidden transition-all hover:shadow-md">
                    <div className="w-full pt-4 px-4 flex justify-center">
                      <div className="relative">
                        <img
                          src={mentor?.image ? ASSET_URL + mentor.image : mentorImg}
                          alt={mentor.name}
                          className="w-20 h-20 object-cover rounded-full border-2 border-white shadow-md"
                        />
                        <div className="absolute -bottom-1 -right-1 bg-blue-500 rounded-full p-1">
                          <Icon icon="mdi:check-decagram" className="text-white text-xs" />
                        </div>
                      </div>
                    </div>
                    <div className="w-full py-4 px-4 text-center">
                      <h3 className="text-base font-medium text-gray-900 mb-1">{mentor.name}</h3>
                      <p className="text-xs text-blue-600 font-medium">
                        {mentor.profession || "Course Instructor"}
                      </p>
                    </div>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        )}
      </div>
    </section>
  );
};

export default Mentors;