import React, { useState, useEffect } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import InputField from '@/components/form/InputField';
import InputSelect from '@/components/form/InputSelect';
import MultipleSelect from '@/components/form/MultipleSelect';
import DatePicker from '@/components/form/DatePicker';
import useFetch from "@/hooks/useFetch";
import api from "@/server/api";

const CreateAssignment = ({ setIsOpen }) => {
    const [loading, setLoading] = useState(false);
    // const [classList, setClassList] = useState([]);
    // const [subjectList, setSubjectList ] = useState([]);
    // const [studentList, setStudentList ] = useState([]);
    const initialValues = {
        course_id: '',
        title: '',
        description: '',
        deadline: '',
        mark: '',
        pass_mark: '',
        instructions: '',
        publish_date: '',
        status: 'Unpublished',
        is_active: '1',
        student_ids: [],

    };

    const validationSchema = Yup.object().shape({
        title: Yup.string().required('Title is required'),
        description: Yup.string().required('Description is required'),
        course_id: Yup.string().required('Course is required'),
        deadline: Yup.date().required('Deadline is required').min(new Date(), 'Deadline must be in the future'),
        publish_date: Yup.date().required('Publish Date is required').min(new Date(), 'Publish Date must be in the future'),
    });

    const handleSubmit = async (values, { setSubmitting }) => {
        // Handle form submission here
        setLoading(true);

        console.log(values);
        try {
            // let formData = new FormData();
            
            // formData.append('course_id', values.course_id);
            // formData.append('title', values.title);
            // formData.append('description', values.description);
            // formData.append('deadline', values.deadline);
            // formData.append('mark', values.mark);
            // formData.append('pass_mark', values.pass_mark);
            // formData.append('instructions', values.instructions);
            // formData.append('publish_date', values.publish_date);
            // formData.append('status', values.status);
            // formData.append('is_active', values.is_active);
            // formData.append('student_ids', values.student_ids);
            // formData.append('supporting_doc', values.supporting_doc);
            
            const response = await api.post('assignments', values);
            setIsOpen(false);
        } catch (error) {
            console.error(error);
        } finally {
            setSubmitting(false);
            setLoading(false);
        }
    };
    const [courseId, setCourseId] = useState(null);
    const { data: courseList } = useFetch({ queryKey: 'mentorCourseList', endPoint: 'mentor-course-list' });
    
    const { data: studentList, isStudentLoading, isStudentError } = useFetch({ queryKey: ['studentList', courseId], endPoint: `mentor-student-list-by-course/${courseId}` });
//   console.log(studentList);


    const studentOptions = studentList?.data?.map(student => ({
        value: student.student_id,
        label: student.student_name
    })) || [];

    const courseOptions = courseList?.data?.map(course => ({
        value: course.id.toString(),
        label: course.title
    })) || [];

    return (
        <div className="w-full px-6 bg-white rounded-lg shadow-md">
            <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
            >
                {({ setFieldValue, values }) => (
                    <Form>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="md:col-span-2">
                            <InputField
                                label="Title"
                                name="title"
                                type="text"
                                required
                            />
                            </div>
                            <DatePicker
                                label="Deadline"
                                name="deadline"
                                required
                            />
                            <DatePicker
                                label="Publish Date"
                                name="publish_date"
                                required
                            />
                            <div className="md:col-span-2">
                                <InputField
                                    label="Description"
                                    name="description"
                                    isTextArea
                                    rows={5}
                                    required
                                />
                            </div>
                            <InputSelect
                                label="Course"
                                name="course_id"
                                options={courseOptions}
                                required
                                onRoleSelect={(couseId) => {
                                    setCourseId(couseId);
                                }}
                            />
                        
                            <MultipleSelect
                                label="Students"
                                name="student_ids"
                                options={studentOptions}
                                onRoleSelect={(value) => {
                                    console.log(value);
                                }}
                            />
                            
                            <InputField
                            label="Mark"
                            name="mark"
                            type="number"
                            />

                            <InputField
                            label="Pass Mark"
                            name="pass_mark"
                            type="number"
                            />
                            
                            <div className="md:col-span-2">
                            <InputField
                                label="Instructions"
                                name="instructions"
                                type="text"
                            />
                            </div>
                        {/* <div className="mb-4">
                            <label className="block text-[#1D1D1F] text-base font-medium mb-2">Supporting Doc</label>
                            
                            <Fileinput
                            name="icon"
                            accept="application/msword, application/pdf"
                            type="file"
                            placeholder="Supporting Doc"
                            preview={false}
                            selectedFile={values.supporting_doc}
                            onChange={(e) => {
                            setFieldValue("supporting_doc", e.currentTarget.files[0]);
                                
                            }}
                            />
                        </div> */}
                        
                        </div>
                        <div className="mt-6">
                            <button
                                type="submit"
                                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                            >
                                Create Assignment
                            </button>
                        </div>
                    </Form>
                )}
            </Formik>
        </div>
    );
};

export default CreateAssignment;
