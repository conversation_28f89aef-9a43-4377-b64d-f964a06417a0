import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import useFetch from '@/hooks/useFetch';
import Loading from '@/components/Loading';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import api from '@/server/api';
import { ASSET_URL } from '@/config';

const WrittenExam = () => {
  const { quizId, resultId, courseId } = useParams();
  const [submitting, setSubmitting] = useState(false);
  const [modalImage, setModalImage] = useState(null);

  const navigate = useNavigate();
  // Fetch quiz details
  const { data: quizdetails, isLoading, isError } = useFetch({
    queryKey: `quizdetails/${quizId}`,
    endPoint: `quiz-details/${quizId}`,
    params: {
      item_id: quizId,
      item_type: 'Course',
    },
  });

  const writtenQuestion = quizdetails?.data.written_questions;

  // Open image modal
  const openModal = (imagePath) => setModalImage(imagePath);

  // Close image modal
  const closeModal = () => setModalImage(null);

  // Handle form submission
  const handleSubmit = async (values) => {
    setSubmitting(true);
    const formData = new FormData();
    formData.append('result_id', resultId);
    formData.append('quiz_id', quizId);
    formData.append('course_id', courseId);

    values.attachment_files.forEach((file) => {
      formData.append('attachment_files[]', file);
    });

    try {
      const response = await api.filepost('submit-written-answer', formData);
  
      if (response.status === 200) {
        navigate(`/exam-details/${resultId}`, { replace: true });
        // alert('Submission successful!');
        // window.location.reload();
      }
    } catch (error) {
      console.error('Submission failed:', error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="container mt-12 space-y-6">
      {isLoading ? (
        <Loading />
      ) : isError ? (
        <p className="text-red-500">Failed to load quiz details.</p>
      ) : (
        <>
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-bold text-sky-700">Quiz Details</h2>
            <p className="text-gray-700 mt-4">
              <b>Description:</b> {writtenQuestion.description}
            </p>
            {writtenQuestion.question_attachment && (
              <div className="mt-4">
                <img
                  src={ASSET_URL + writtenQuestion.question_attachment}
                  alt="Question Attachment"
                  className="h-32 w-auto rounded-lg shadow-md cursor-pointer"
                  onClick={() =>
                    openModal(ASSET_URL + writtenQuestion.question_attachment)
                  }
                />
              </div>
            )}
            <p className="text-sm text-gray-600 mt-4">
              <strong>Duration:</strong> {writtenQuestion.duration} minutes
            </p>
            <p className="text-sm text-gray-600">
              <strong>Total Marks:</strong> {writtenQuestion.marks}
            </p>
            <p className="text-sm text-gray-500">
              <strong>Number of Questions:</strong> {writtenQuestion.no_of_question}
            </p>


            <p className="text-gray-700 mt-4">
              <b>Instruction:</b> {writtenQuestion.instruction}
            </p>
          </div>

          <Formik
            initialValues={{
              attachment_files: [],
            }}
            validationSchema={Yup.object({
              answer: Yup.string(),
              attachment_files: Yup.array().min(1, 'At least one file is required.'),
            })}
            onSubmit={handleSubmit}
          >
            {({ setFieldValue, values, errors, touched }) => (
              <Form className="bg-white shadow rounded-lg p-6 space-y-6">

                {errors.answer && touched.answer && (
                  <p className="text-red-500 text-sm">{errors.answer}</p>
                )}

                {/* File Upload */}
                <div>
                  <label className="block text-gray-700 font-bold">
                    Attach Files
                  </label>
                  <input
                    type="file"
                    multiple
                    accept=".jpg,.jpeg,.png,.pdf"
                    onChange={(e) =>
                      setFieldValue('attachment_files', Array.from(e.target.files))
                    }
                    className="mt-2 block"
                  />
                  {errors.attachment_files && touched.attachment_files && (
                    <p className="text-red-500 text-sm">
                      {errors.attachment_files}
                    </p>
                  )}
                </div>

                {/* File Previews */}
                {values.attachment_files.length > 0 && (
                  <div className="flex gap-6">
                    {values.attachment_files.map((file, index) => (
                      <div
                        key={index}
                        className="relative border"
                      >
                        {file.type.startsWith('image/') ? (
                          <img
                            src={URL.createObjectURL(file)}
                            alt="Preview"
                            className="h-20 w-auto"
                          />
                        ) : (
                          <p className="text-sm">{file.name}</p>
                        )}
                        <button
                          type="button"
                          onClick={() =>
                            setFieldValue(
                              'attachment_files',
                              values.attachment_files.filter(
                                (_, i) => i !== index
                              )
                            )
                          }
                          className="absolute top-[-12px] right-[-12px] bg-red-600 text-white rounded-full p-1"
                        >
                          <Icon icon="mdi:close" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}

                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={submitting}
                    className="bg-sky-700 text-white px-4 py-2 rounded hover:bg-sky-600"
                  >
                    {submitting ? 'Submitting...' : 'Submit'}
                  </button>
                </div>
              </Form>
            )}
          </Formik>

          {/* Modal */}
          {modalImage && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50"
              onClick={closeModal}
            >
              <div className="bg-white p-4 rounded-lg relative">
                <button
                  className="absolute top-2 right-2 bg-red-600 text-white rounded-full p-2"
                  onClick={closeModal}
                >
                  <Icon icon="mdi:close" />
                </button>
                <img
                  src={modalImage}
                  alt="Modal"
                  className="max-w-full max-h-screen"
                />
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default WrittenExam;
