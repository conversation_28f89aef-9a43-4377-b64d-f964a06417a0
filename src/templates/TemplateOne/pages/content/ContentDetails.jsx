import React, { useState, useEffect, useRef } from "react";
import Loading from "@/components/Loading";
import { useParams, useNavigate } from "react-router-dom";
import useFetch from "@/hooks/useFetch";
import { Icon } from "@iconify/react";
import ReactPlayer from "react-player";
import GoBack from "@/components/ui/GoBack";
import { ASSET_URL } from "@/config";
import ModuleAccordion from "@/components/ui/ModuleAccordion";
import { useSelector } from "react-redux";
import api from "@/server/api";
import { toast } from "react-toastify";

const ContentDetails = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isPlayingVideo, setIsPlayingVideo] = useState(false);
  const [isViewingScript, setIsViewingScript] = useState(false);
  const [scriptLoading, setScriptLoading] = useState(true);
  const { id } = useParams();
  const navigate = useNavigate();
  const iframeRef = useRef(null);
  const { isAuth } = useSelector((state) => state.auth);

  const {
    data: contentDetails,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `content-details/${id}`,
    endPoint: `get-content-details?id=${id}`,
    dependencies: [id],
  });

  const content = contentDetails?.data;

  // Track video completion
  useEffect(() => {
    if (!isAuth || !content) return;

    let timer;

    if (isPlayingVideo) {
      timer = setTimeout(async () => {
        try {
          await api.get(`mark-video-completed?content_id=${id}`);
          console.log('Video marked as watched');
        } catch (error) {
          console.error('Error marking video completed:', error);
          toast.error("Failed to save video progress");
        }
      }, 5000);
    }

    return () => clearTimeout(timer);
  }, [isPlayingVideo, isAuth, id, content]);

  // Track script completion
  useEffect(() => {
    if (!isAuth || !content || content?.type !== "script") return;

    let timer;

    if (isViewingScript) {
      timer = setTimeout(async () => {
        try {
          await api.get(`mark-script-completed?content_id=${id}`);
          
        } catch (error) {
          console.error('Error marking script completed:', error);
          toast.error("Failed to save script progress");
        }
      }, 5000);
    }

    return () => clearTimeout(timer);
  }, [isViewingScript, isAuth, id, content]);

  // Handle iframe visibility
  useEffect(() => {
    if (!iframeRef.current || content?.type !== "script") return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        setIsViewingScript(entry.isIntersecting);
      },
      { threshold: 0.5 }
    );

    observer.observe(iframeRef.current);

    return () => {
      if (iframeRef.current) {
        observer.unobserve(iframeRef.current);
      }
    };
  }, [content]);

  if (isLoading) return <Loading />;
  if (isError) return <div>Error fetching data</div>;

  return (
    <div className="pb-10">
      {content && (
        <div className="container pt-1 lg:pt-5 space-y-3">
          <GoBack title="Back" />
          
          {/* Media Container */}
          <div className="h-[450px] max-sm:h-[220px] rounded-lg overflow-hidden bg-gray-100">
            {content?.type === "video" && (
              <ReactPlayer
                url={content?.video?.youtube_url || content?.video?.s3_url || content?.video?.raw_url}
                playing={isPlayingVideo}
                muted={true}
                controls={true}
                width="100%"
                height="100%"
                onPlay={() => setIsPlayingVideo(true)}
                onPause={() => setIsPlayingVideo(false)}
                onEnded={() => setIsPlayingVideo(false)}
              />
            )}
            
            {content?.type === "script" && (
              <div ref={iframeRef} className="w-full h-full">
                {scriptLoading && (
                  <div className="w-full h-full flex items-center justify-center">
                    <Loading />
                  </div>
                )}
                <iframe
                  className={`w-full h-full ${scriptLoading ? 'hidden' : 'block'}`}
                  src={content?.script?.raw_url}
                  frameBorder="0"
                  allowFullScreen
                  onLoad={() => {
                    setScriptLoading(false);
                    setIsViewingScript(true);
                  }}
                  onError={() => {
                    setScriptLoading(false);
                    toast.error("Failed to load script");
                  }}
                />
              </div>
            )}
          </div>

          {/* Content Details */}
          <div className="space-y-3 pt-5">
            <h2 className="text-xl text-sky-700 font-semibold">
              {content?.video?.title || content?.script?.title}
            </h2>
            
            {content?.video?.description && (
              <p className="text-sm text-gray-700">
                {isExpanded
                  ? content.video.description
                  : content.video.description.slice(0, 300)}
                {content.video.description.length > 300 && (
                  <button
                    className="text-sky-600 hover:text-sky-800 ml-1 font-medium"
                    onClick={() => setIsExpanded(!isExpanded)}
                  >
                    {isExpanded ? "Show less" : "...See more"}
                  </button>
                )}
              </p>
            )}

            <ModuleAccordion
              module={content?.outline}
              isOpen={true}
              selectedItem={id}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ContentDetails;