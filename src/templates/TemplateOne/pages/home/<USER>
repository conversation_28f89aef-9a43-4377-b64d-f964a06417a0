import React from "react";
import { Link } from "react-router-dom";
import { Icon } from "@iconify/react/dist/iconify.js";
import { ASSET_URL } from "@/config";

const HighlightSection = ({ promotionalItems }) => {
  return (
    <div className="my-16 max-w-5xl px-6 lg:px-0 mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {promotionalItems?.map((item, index) => (
          <div
            className="relative rounded-2xl overflow-hidden shadow-lg group h-[280px]"
            key={index}
          >
            {/* Background image */}
            <img
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
              src={ASSET_URL + item.image}
              alt={item.title}
            />

            {/* Overlay gradient */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent z-10" />

            {/* Text content */}
            <div className="absolute bottom-0 z-20 p-6 text-white space-y-2">
              <h3 className="text-2xl font-bold">{item.title}</h3>
              <p className="text-lg text-orange-400">{item.description}</p>
              <Link
                to={item.course ? `/course/${item.course.slug}` : item.link}
                className="inline-flex items-center gap-2 text-sm px-4 py-2 border border-sky-400 rounded-full bg-sky-600 hover:bg-sky-500 transition-all"
              >
                See Details
                <Icon icon="line-md:arrow-right" className="text-lg" />
              </Link>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HighlightSection;
