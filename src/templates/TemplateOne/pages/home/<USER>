// import React from "react";

// const Card = ({ course, index }) => {
//   return (
//     <div className="card border border-gray-300 rounded-lg shadow-md relative">
//       <img
//         src={course.imageUrl}
//         alt={course.title}
//         className="card-img-top rounded-t-lg"
//       />
//       {/* Flag */}
//       <div className="absolute top-2 right-2 bg-white text-blue-400 text-xs px-3 py-1 rounded">
//         {course.isFree ? "Free" : "Discount"}
//       </div>

//       <div className="card-body p-4">
//         <h5 className="card-title text-lg font-semibold">{course.title}</h5>
//         <p className="card-text text-gray-600">{course.description}</p>
//         <button className="mt-4 px-10 py-1 text-blue-400 rounded-lg hover:bg-blue-400 border border-slate-300 mx-auto block hover:text-white">
//           Enroll Now
//         </button>
//       </div>
//     </div>
//   );
// };

// export default Card;

import React from "react";
import { Icon } from "@iconify/react";

const Card = ({ course, index }) => {
  return (
    <div className="card border border-gray-300 rounded-lg shadow-md relative">
      <img
        src={course.imageUrl}
        alt={course.title}
        className="card-img-top rounded-t-lg"
      />
      {/* Flag */}
      {/* <div
        className={`absolute top-2 right-2 text-xs px-3 py-1 rounded bg-white ${
          course.isFree ? "bg-white text-blue-400" : "bg-white text-blue-400"
        }`}
      >
        {course.isFree ? (
          "Free"
        ) : (
          <div className="flex justify-center items-center ">
            <Icon icon="bx:purchase-tag" className="h-5 w-5 mr-2" />
            30 %
          </div>
        )}
      </div> */}
      <div
        className={`absolute top-2 right-2 text-xs px-3 py-1 rounded ${
          course.isFree
            ? "bg-white text-primary-500"
            : "bg-white text-primary-500"
        }`}
      >
        {course.isFree ? (
          "Free"
        ) : (
          <div className="flex justify-center items-center">
            <Icon icon="bx:purchase-tag" className="h-5 w-5 mr-2" />
            {course.discount ? `${course.discount} % OFF` : "Discount"}
          </div>
        )}
      </div>
      <div className="card-body p-4">
        <h5 className="card-title text-lg font-semibold">{course.title}</h5>
        <p className="card-text text-gray-600">{course.description}</p>
        {/* <button className="mt-4 px-10 py-1 text-blue-400 rounded-lg hover:bg-blue-400 border border-slate-300 mx-auto block hover:text-white">
          Enroll Now
        </button> */}
        <button className="mt-4 px-10 py-1 text-blue-400 rounded-lg bg-gradient-to-r hover:from-pictonBlue-500 hover:to-primary-500 border border-slate-300 mx-auto block hover:text-white">
          Enroll Now
        </button>
      </div>
    </div>
  );
};

export default Card;
