import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.css";
import SwiperCore, { Navigation, Pagination } from "swiper";
import { useEffect, useRef, useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import itIcon from "@/assets/images/svg/it.svg";
import onlineIcon from "@/assets/images/svg/online.svg";
import careerIcon from "@/assets/images/svg/career.svg";
import schoolIcon from "@/assets/images/svg/school.svg";
import collegeIcon from "@/assets/images/svg/college.svg";
import coursesIcon from "@/assets/images/svg/courses.svg";
import { ASSET_URL } from "@/config";
import { useNavigate } from "react-router-dom";

// Install Swiper modules
SwiperCore.use([Navigation, Pagination]);

const Categories = ({categories}) => {
    const swiperRef = useRef(null);
    const [isPrevDisabled, setPrevDisabled] = useState(true);
    const [isNextDisabled, setNextDisabled] = useState(false);
    const navigate = useNavigate();
    useEffect(() => {
      const swiper = swiperRef.current.swiper;
  
      // Update button states based on the current slide index
      const updateButtonStates = () => {
        const currentIndex = swiper.activeIndex;
        const totalSlides = swiper.slides.length;
        const slidesPerView = swiper.params.slidesPerView;
        
        // Disable the previous button if we're at the start
        setPrevDisabled(currentIndex === 0);
        
        // Disable the next button if we've reached or exceeded the last slide
        setNextDisabled(currentIndex >= totalSlides - slidesPerView);
      };
  
      // Add event listeners to the swiper
      swiper.on('slideChange', updateButtonStates);
  
      // Run on initial mount
      updateButtonStates();
  
      return () => {
        swiper.off('slideChange', updateButtonStates);
      };
    }, []);
  return (
    <div className="container space-y-5 relative py-12">
      <span className="bg-sky-100 text-sky-600 p-2 px-4 text-lg rounded-full">
        Categories
      </span>
      <h2 className="text-4xl pb-5 text-sky-700">
        {/* Explore Course Category */}
      </h2>
      <div className="relative">
        <Swiper
          ref={swiperRef}
          slidesPerView={8} // Show 8 items in large screens
          spaceBetween={20} // Space between slides
          navigation={false} // Disable default navigation
          breakpoints={{
            320: {
              slidesPerView: 2, // 1 item on mobile
            },
            640: {
              slidesPerView: 3, // 2 items on small screens
            },
            768: {
              slidesPerView: 3, // 3 items on medium screens
            },
            1024: {
              slidesPerView: 4, // 6 items on large screens
            },
            1200: {
              slidesPerView: 6, // 6 items on large screens
            },
          }}
        >
          {categories?.map((item) => (
            <SwiperSlide
              key={item.id}
              className="flex flex-col items-center py-5 relative z-0"
            >
              <div
                className="border rounded-lg bg-white shadow-md hover:bg-sky-50 p-3 group text-center w-full transition-color duration-300 cursor-pointer"
                onClick={() => navigate(`/courses/${item.id}`)}
              >
                <img src={item?.icon ? ASSET_URL + item.icon: itIcon} alt="" className="w-full object-contain mx-auto h-24" />
                <p className="text-[17px] mt-2 text-gray-800 group-hover:text-sky-700">{item.name}</p>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Custom Navigation Buttons */}
        <div className={`absolute top-14 max-sm:top-[70px] container ${categories?.length < 7 ? "lg:hidden" : ''} ${categories?.length < 3 ? "max-sm:hidden" : ''}`}>
          <button
            className={`absolute xl:-left-20 md:-left-0 max-sm:-left-0 border text-sky-600 z-10 bg-white rounded-full shadow-lg p-4 max-sm:p-3 transition ${
              isPrevDisabled ? "opacity-50 cursor-not-allowed" : ""
            }`}
            onClick={() => {
              if (!isPrevDisabled) {
                swiperRef.current.swiper.slidePrev();
              }
            }}
            disabled={isPrevDisabled}
          >
            <Icon icon="ep:arrow-left-bold" className="text-2xl" />
          </button>
          <button
            className={`absolute xl:-right-20 md:-right-2 max-sm:-right-0 border text-sky-600 z-10 bg-white rounded-full shadow-lg p-4 max-sm:p-3 transition ${
              isNextDisabled ? "opacity-50 cursor-not-allowed" : ""
            }`}
            onClick={() => {
              if (!isNextDisabled) {
                swiperRef.current.swiper.slideNext();
              }
            }}
            disabled={isNextDisabled}
          >
            <Icon icon="ep:arrow-right-bold" className="text-2xl" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Categories;
