import React, { useState, useRef, useEffect } from "react";
import Testimonial from "./Testimonial";
// import CallToAction from "./CallToAction";
// import Courses from "./Courses";
import HeroSection from "./HeroSection";
import Experts from "./Experts";
import Loading from "@/components/Loading";
import { useSelector } from "react-redux";
import Course from "./Course";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import SwiperCore, { Autoplay, Navigation } from "swiper";
import "./swiper.css";
import avatar from "@/assets/images/avatar/av-1.svg";
import avatar2 from "@/assets/images/avatar/av-3.svg";
import bgShape1 from "@/assets/images/svg/netVector.svg";
import bgShape2 from "@/assets/images/svg/shape2.svg";
import bgShape3 from "@/assets/images/svg/shape3.svg";
import bgShape4 from "@/assets/images/svg/shape4.svg";
import Categories from "@/pages/home/<USER>";
import Courses from "./Courses";
import schoolIcon from "@/assets/images/svg/school.svg";
import { Icon } from "@iconify/react";
import MobileApp from "./MobileApp";
import useFetch from "@/hooks/useFetch";
import catBg from "@/assets/images/all-img/cat.png";
import HighlightSection from "./HighlightSection";
import ContactUs from "../../../TemplateTwo/pages/home/<USER>";

SwiperCore.use([Autoplay, Navigation]);

const Home = () => {
  const { organization } = useSelector((state) => state.commonSlice);
  const { data: landingPageData = [], isLoading } = useFetch({
    queryKey: "homeDetails",
    endPoint: `landing-page-information?id=${organization.id}`,
  });
  const prevRef = useRef(null);
  const nextRef = useRef(null);
  // console.log(organization)

  //Client Say Data
  const { data: clientData = [], isTestimonialLoading } = useFetch({
    queryKey: "testimonials",
    endPoint: `testimonials`,
  });

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div>
      <div className="mx-auto">
        <HeroSection organization={organization} />


      </div>

      {/* categories section  */}
      <div className="relative mb-28 xl:mt-8 ">
        <img src={catBg} alt="" className="absolute z-0 hidden xl:block" />
        <div className="relative z-10 bg-[rgba(245,250,255,0.5)]">
          <Categories categories={landingPageData?.data?.categories} />
    </div>
          <div className="relative z-10">
          {/* <HighlightSection promotionalItems={landingPageData?.data?.promotional_items} /> */}

          <div className="relative">
            {landingPageData?.data &&
              landingPageData?.data?.category_with_courses?.map((data, idx) => (
                <div key={idx}>
                  {idx % 2 == 0 ? (
                    <img
                      src={bgShape1}
                      alt=""
                      className="absolute z-0 -left-20"
                    />
                  ) : (
                    <img
                      src={bgShape3}
                      alt=""
                      className="absolute z-0 lg:-right-20 right-0"
                    />
                  )}
                  <Courses
                    category={data}
                    coursesCategory={data?.sub_categories}
                    bought_items={landingPageData?.data?.bought_items}
                  />
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* client say section */}
      {clientData?.data?.length > 0 && <div className="relative z-10">
        <Testimonial clientData={clientData} />
      </div>}

      {/* Contact us section */}
      <div>
        <ContactUs organization={organization} />
      </div>

      {/* mobile app section  */}
      {/* <MobileApp organization={organization} /> */}
    </div>
  );
};

export default Home;
