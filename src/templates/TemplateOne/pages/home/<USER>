import React from "react";
import { Icon } from "@iconify/react";
import { ASSET_URL } from "@/config";
import DemoCourseImage from "@/assets/course_demo.jpg";
import { useNavigate } from "react-router-dom";
import Rating from "@/components/ui/Rating";

const Course = ({ course, index, purchased = false, boughtCourses = [] }) => {
  const navigate = useNavigate();

  const handleNavigate = () => {
    navigate(course.slug ? `/course/${course.slug}` : `/course-details/${course.id}`);
  };

  const mentorInfo = () => {
    const count = course?.mentors?.length || 0;
    if (count === 0) return null;
    const firstMentor = course.mentors[0]?.name || "";
    return count === 1
      ? firstMentor
      : `${firstMentor} and ${count - 1} other${count - 1 > 1 ? "s" : ""}`;
  };

  const renderPrice = () => {
    if (!course.show_price) return null;

    if (course.is_free) {
      return <span className="text-sky-600 font-semibold">Free</span>;
    }

    if (course.installment_type === "Monthly" && course.monthly_amount > 0) {
      return (
        <span className="text-sky-600 font-semibold">
         {course.monthly_amount?.toLocaleString()} /Month { course?.currency }
        </span>
      );
    }

    if (course.sale_price && course.regular_price) {
      return (
        <span className="flex items-center gap-2 font-semibold text-red-500">
           {course.sale_price?.toLocaleString()} { course?.currency }
           { course.regular_price > course.sale_price &&
            <span className="text-gray-500 line-through text-sm">
              {course.regular_price?.toLocaleString()} { course?.currency }
            </span>
           }
        </span>
      );
    }

    return null;
  };

  return (
    <div
      onClick={handleNavigate}
      className="cursor-pointer border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow bg-white flex flex-col justify-between group overflow-hidden relative"
    >
      <img
        src={course.thumbnail ? ASSET_URL + course.thumbnail : DemoCourseImage}
        alt={course.title}
        className="w-full h-36 object-cover rounded-t-lg"
      />
      <div className="p-4 flex flex-col gap-2">
        <div className="flex items-center justify-between">
          {course.rating > 0 &&  <Rating rating={course.rating} /> }
          {boughtCourses.includes(course.id) ? (
            <p className="text-green-500 font-medium text-xs">Enrolled</p>
          ) : (
            <p className="text-red-500 font-medium text-xs">Enroll Now</p>
          )}
        </div>
        <h2 className="text-sm text-sky-700 font-semibold truncate">
          {course.title}
        </h2>
        <div className="flex justify-between items-center text-xs text-gray-600">
          {mentorInfo() && <span>{mentorInfo()}</span>}
          {!purchased && course.show_price && (
            <div className="text-right">{renderPrice()}</div>
          )}
        </div>
      </div>
      <button
        className="w-full py-2 border-t text-sky-600 font-medium hover:bg-sky-50 transition-all flex items-center justify-center gap-2 text-sm"
      >
        See Details
        <Icon icon="line-md:arrow-right" className="text-base" />
      </button>
    </div>
  );
};

export default Course;
