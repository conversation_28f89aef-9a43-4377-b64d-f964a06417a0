import React, { useState, useEffect } from "react";
import Modal from "@/components/ui/Modal";
import { Formik, Form, Field, ErrorMessage } from "formik";
import Button from "@/components/ui/Button";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import InputSelect from "@/components/form/InputSelect";
import Select from "react-select";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import Fileinput from "@/components/ui/Fileinput";
import useFetch from "@/hooks/useFetch";
import MultiSelectComponent from "@/components/ui/MultiSelectComponent";
import api from "@/server/api";
import * as Yup from "yup";
import { useQueryClient } from "@tanstack/react-query";

const CreateAssignment = ({ showModal, setShowModal }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [courseId, setCourseId] = useState("");
  const [batchList, setBatchList] = useState([]);

  // Fetch course list
  const { data: courseList } = useFetch({
    queryKey: `mentor-course-list-for-filter`,
    endPoint: `mentor-course-list-for-filter`,
  });



  // Fetch students based on courseId
  // const { data: students, isStudentLoading } = useFetch(
  //   courseId
  //     ? {
  //         queryKey: `/mentor/student-list?course_id=${courseId}`,
  //         endPoint: `/mentor/student-list?course_id=${courseId}`,
  //       }
  //     : {
  //         queryKey: ``,
  //         endPoint: ``,
  //       }
  // );

  // useEffect(() => {
  //   if (students?.data) {
  //     setStudentList(students.data);
  //   }
  // }, [students]);


  const { data: batchs, isLoading: isBatchLoading } = useFetch(
    courseId
      ? {
          queryKey: `/mentor/batch-list?course_id=${courseId}`,
          endPoint: `/mentor/batch-list?course_id=${courseId}`,
        }
      : {
          queryKey: ``,
          endPoint: ``,
        }
  );

  useEffect(() => {
    if (batchs?.data) {
      setBatchList(batchs.data);
    }
  }, [batchs]);


  // Validation schema
  const validationSchema = Yup.object({
    title: Yup.string()
      .required("Assignment title is required")
      .max(100, "Title cannot exceed 100 characters"),
    course_id: Yup.string().required("Please select a course"),
    description: Yup.string()
      .required("Description is required")
      .max(500, "Description cannot exceed 500 characters"),
    publish_date: Yup.date().required("Publish date is required"),
    deadline: Yup.date()
      .required("Deadline is required")
      .min(Yup.ref("publish_date"), "Deadline must be after the publish date"),
    mark: Yup.number()
      .required("Mark is required")
      .min(1, "Mark must be at least 1"),
    supporting_doc: Yup.mixed().test(
      "fileType",
      "Only PDF files are allowed",
      (value) => value && value.type === "application/pdf"
    ),
    instructions: Yup.string()
      .required("Instructions are required")
      .max(300, "Instructions cannot exceed 300 characters"),
    // student: Yup.array()
    //   .min(1, "Select at least one student")
    //   .required("Students selection is required"),
    batch_id: Yup.string().required("Batch are required")
  });
  const queryClient = useQueryClient();

  const handleSubmit = async (values) => {
    try {
      setIsLoading(true);

      console.log("Submitted values:", values);

      // const batchIds = values.batch
      //   ?.map((batch) => batch.value)
      //   .join(",");

      const formData = new FormData();
      formData.append("title", values.title);
      formData.append("course_id", values.course_id);

      const publishDate = new Date(values.publish_date);
      const formattedPublishDate = publishDate.toISOString().split("T")[0];
      formData.append("publish_date", formattedPublishDate);
      const date = new Date(values.deadline);
      const formattedDate = date.toISOString().split("T")[0];
      console.log(formattedDate);
      formData.append("deadline", formattedDate);
      formData.append("mark", values.mark);
      formData.append("supporting_doc", values.supporting_doc);
      formData.append("description", values.description);
      formData.append("instructions", values.instructions);
      formData.append("status", "Ongoing");
      // formData.append("batch_ids", `[${batchIds}]`);
      formData.append("batch_id", values.batch_id);

      const response = await api.filepost("mentor/create-assignment", formData);
      queryClient.invalidateQueries("assignment-list");
      setShowModal(false);
      console.log(response);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  console.log(batchList)

  return (
    <div>
      <Modal
        activeModal={showModal}
        onClose={() => setShowModal(false)}
        title="Create Assignment"
        className="max-w-4xl"
        footer={
          <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => setShowModal(false)}
          />
        }
      >
        <Formik
          initialValues={{
            title: "",
            description: "",
            course_id: "",
            deadline: "",
            mark: "",
            supporting_doc: "",
            batch_id: ""
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, setFieldValue, errors }) => (
            <Form>{console.log(errors)}
              <div className="space-y-3">
                <div className="lg:flex justify-between gap-4 w-full">
                  <div className="w-full">
                    <InputField
                      label="Assignment Title"
                      name="title"
                      type="text"
                      placeholder="Assignment Name"
                      required
                    />
                  </div>
                  <div className="w-full">
                    <label className="block text-gray-600 text-sm font-medium mb-2">
                      Select Course
                      <span className="text-red-500">*</span>
                    </label>
                    <Select
                      placeholder="Select Course"
                      options={courseList?.data?.map((course) => ({
                        value: course.id,
                        label: course.title,
                      }))}
                      name="course_id"
                      onChange={(e) => {
                        console.log(e.value);
                        setCourseId(e.value);
                        setFieldValue("course_id", e.value);
                      }}
                    />
                    <ErrorMessage name="course_id">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}  </div>
                      )}
                    </ErrorMessage>
                  </div>


                  <div className="w-full">
                    <label className="block text-gray-600 text-sm font-medium mb-2">
                      Select Batch
                      <span className="text-red-500">*</span>
                    </label>
                    <Select
                      placeholder="Select Batch"
                      options={batchList?.map((batch) => ({
                        value: batch.id,
                        label: batch.name,
                      }))}
                      name="batch_id"
                      onChange={(e) => {
                        console.log(e.value);
                        // setCourseId(e.value);
                        setFieldValue("batch_id", e.value);
                      }}
                    />
                    <ErrorMessage name="batch_id">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                </div>

                <div className="gap-4">
                  <Textarea
                    label="Description"
                    type="text"
                    placeholder="Assignment Description"
                    required
                    onChange={(e) => {
                      setFieldValue("description", e.target.value);
                    }}
                  />
                  <ErrorMessage name="description">
                    {(msg) => (
                      <div className="text-red-500 text-sm mt-1">{msg}</div>
                    )}
                  </ErrorMessage>
                </div>
                <div className="lg:flex justify-between gap-4">
                  <div className="w-full">
                    <DatePicker
                      label="Publish Date"
                      placeholder="YYYY-MM-DD"
                      format="YYYY/MM/DD"
                      name="publish_date"
                      required
                      onChange={(e) => {
                        setFieldValue("publish_date", e);
                      }}
                    />
                    <ErrorMessage name="publish_date">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                  <div className="w-full">
                    <DatePicker
                      label="Deadline"
                      placeholder="YYYY-MM-DD"
                      format="YYYY/MM/DD"
                      name="deadline"
                      required
                      onChange={(e) => {
                        setFieldValue("deadline", e);
                      }}
                    />
                    <ErrorMessage name="deadline">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                  <div className="w-full">
                    <InputField
                      label="Total Mark"
                      name="mark"
                      type="number"
                      min="0"
                      placeholder="Enter Assignment Mark"
                      required
                    />
                  </div>
                </div>
                <div className="">
                  <Fileinput
                    name="supporting_doc"
                    accept=".pdf"
                    type="file"
                    placeholder="Select Document"
                    title="Question (Document)"
                    selectedFile={values.supporting_doc} 
                    onChange={(e) => {
                      setFieldValue("supporting_doc", e.target.files[0]);
                    }}
                  />
                  <ErrorMessage name="supporting_doc">
                    {(msg) => (
                      <div className="text-red-500 text-sm mt-1">{msg}</div>
                    )}
                  </ErrorMessage>

                  {/* <div className="mt-3">
                    <label className="block text-gray-600 text-sm font-medium mb-2">
                      Select Students
                      <span className="text-red-500"> *</span>
                    </label>
                    <MultiSelectComponent
                      name="student"
                      placeholder="Select Students"
                      options={studentList?.map((student) => ({
                        label: student.name,
                        value: student.id,
                      }))}
                      valueKey="value"
                      labelKey="label"
                      onChange={(selected) =>
                        setFieldValue("student", selected)
                      }
                    />
                    <ErrorMessage name="student">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div> */}

                  <div className="gap-4 mt-3">
                    <Textarea
                      label="Instructions"
                      type="text"
                      placeholder="Instructions"
                      required
                      onChange={(e) => {
                        setFieldValue("instructions", e.target.value);
                      }}
                    />
                  </div>
                </div>
              </div>
              <div className="flex justify-end mt-4">
                <Button
                  text="Submit"
                  type="submit"
                  btnClass="btn-primary"
                  isLoading={isLoading}
                />
              </div>
            </Form>
          )}
        </Formik>
      </Modal>
    </div>
  );
};

export default CreateAssignment;
