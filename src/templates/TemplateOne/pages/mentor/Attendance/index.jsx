import React, { useState, useMemo, useEffect } from "react";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "react-big-calendar/lib/css/react-big-calendar.css";
import CreateClass from "./CreateClass";
import { useDispatch, useSelector } from "react-redux";
import Attendance from "./Attendance";
import useFetch from "@/hooks/useFetch";
import { setShowModal } from "@/store/assignmentStore";
import api from "@/server/api";
const localizer = momentLocalizer(moment);

const AttendanceList = () => {
  const dispatch = useDispatch();
  const [isAttendancePage, setIsAttendancePage] = useState(false);
  const { showModal } = useSelector((state) => state.assignmentSlice);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [eventData, setEventData] = useState(null);
  const [contextMenu, setContextMenu] = useState(null); // Manage context menu state

  // Fetch data using RTK Query

  const { data, isLoading: isLoading, isError, error } = useFetch({
    queryKey: "live-class-list",
    endPoint: `mentor/live-class-list`,
  });

  const res = data?.data;
  // Prepare events for the calendar
  const events = useMemo(() => {
    return res?.map((schedule) => ({
      id: schedule.id,
      title: ` ${schedule.title}`,
      start: new Date(schedule.schedule_datetime), // Start time from `schedule_datetime`
      end: new Date(
        new Date(schedule.schedule_datetime).getTime() +
          schedule.duration * 60000
      ), // End time calculated using `duration`
      resource: schedule,
    }));
  }, [res]);

  // Slot selection handler (when clicking on an empty slot)
  const handleSlotSelect = (slotInfo) => {
    setSelectedDate(slotInfo.start); // Set the selected date
    dispatch(setShowModal(true)); // Open the modal
  };

  const closeContextMenu = () => {
    setContextMenu(null); // Hide the context menu
  };

  // Handle "Create Class" button click
  const handleCreateClass = () => {
    dispatch(setShowModal(true)); // Open the modal
    closeContextMenu(); // Close the context menu
  };

  // Event selection handler
  const handleEventSelect = (event) => {
    setEventData(res.find((item) => item.id === event.id));
    setIsAttendancePage(true);
  };


  return (
    <div className="max-w-7xl mx-auto">
      {isAttendancePage ? (
        <Attendance
          eventData={eventData}
          setIsAttendancePage={setIsAttendancePage}
        />
      ) : (
        <div style={{ margin: "20px" }}>
          <h2 className="text-2xl font-semibold mb-4">Class Routine</h2>
          {isLoading && <div>Loading...</div>}
          {isError && <div>Error: {error.message}</div>}
          {!isLoading && !isError && (
            <div style={{ height: "800px" }} onClick={closeContextMenu}>
              <Calendar
                localizer={localizer}
                events={events} // Events populated from API
                startAccessor="start" // Start time for events
                endAccessor="end" // End time for events
                style={{ height: 800 }}
                selectable // Enable slot selection
                onSelectSlot={handleSlotSelect} // Handler for selecting an empty slot
                onSelectEvent={handleEventSelect} // Handler for selecting an event
                views={["month", "week", "day"]} // Calendar views
                defaultView="month" // Default view when calendar loads
                defaultDate={new Date()} // Focus on today's date
                dayLayoutAlgorithm="no-overlap" // Prevent overlapping events in the day
              />
            </div>
          )}

          {/* Pass selectedDate as a prop to CreateClass */}
          {showModal && <CreateClass selectedDate={selectedDate} showModal={showModal} setShowModal={setShowModal} />}
        </div>
      )}
    </div>
  );
};

export default AttendanceList;
