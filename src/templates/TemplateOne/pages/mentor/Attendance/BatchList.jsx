import React from "react";
import useFetch from "@/hooks/useFetch";
import avatar from "@/assets/images/avatar/av-1.svg";
import { ASSET_URL } from "../@/config";

const BatchList = ({ setBatchId }) => {
  const { data, isLoading: batchLoading } = useFetch({
    queryKey: "batch-list",
    endPoint: `mentor/batch-list?pagination=false`,
  });
  const batchList = data?.data;
  console.log(batchList);
  return (
    <div className=" min-h-[50vh]">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5">
        {batchList?.map((item, idx) => (
          <div
            onClick={() => setBatchId({ value: item?.id, label: item?.name })}
            className="border rounded-lg hover:shadow-sm border-gray-300 hover:cursor-pointer"
            key={idx}
          >
            <img
              className="rounded-t-lg"
              src={item?.image ? ASSET_URL + item.image : avatar}
              alt=""
            />
            <h2 className="text-lg text-center py-2">{item?.name}</h2>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BatchList;
