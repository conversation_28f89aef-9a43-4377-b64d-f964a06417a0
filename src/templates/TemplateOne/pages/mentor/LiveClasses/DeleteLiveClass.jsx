import React from "react";
import { useDispatch } from "react-redux";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import api from "@/server/api";
import { useQueryClient } from "@tanstack/react-query";

const DeleteLiveClass = ({ showDeleteModal, setShowDeleteModal, data }) => {
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  //   const [deleteApi, { isLoading, isError, error, isSuccess }] = useDeleteApiMutation();
  const onSubmit = async () => {
    const response = await api.post("mentor/delete-live-class-schedule", {
      id: data?.id,
    });

    if (response.data?.status) {
      queryClient.invalidateQueries('live-class-list')
      dispatch(setShowDeleteModal(false));
    }
  };
  return (
    <Modal
      activeModal={showDeleteModal}
      onClose={() => dispatch(setShowDeleteModal(false))}
      title="Delete Assignment"
      className="max-w-2xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowDeleteModal(false))}
        />
      }
    >
      <h3 className="text-center">Are you sure?</h3>
      <p className="text-center text-slate-500 text-sm mt-4">
        You are going delete <b>"{data?.title}"</b> assignment. Once you have
        done this, there is no going back.{" "}
      </p>

      <div className="flex justify-end mt-5">
        <Button
          // isLoading={isLoading}
          type="button"
          className="btn text-center btn-primary mr-4"
          onClick={() => dispatch(setShowDeleteModal(false))}
        >
          Cancel
        </Button>
        <Button
          //   isLoading={isLoading}
          type="button"
          className="btn text-center btn-danger"
          onClick={onSubmit}
        >
          Delete
        </Button>
      </div>
      {/* <Button text="Delete" btnClass="btn btn-danger" /> */}
    </Modal>
  );
};

export default DeleteLiveClass;
