import React, { useEffect, useState } from "react";
import Modal from "@/components/ui/Modal";
import { useDispatch } from "react-redux";
import Button from "@/components/ui/Button";
import { ErrorMessage, Form, Formik } from "formik";
import InputField from "@/components/ui/InputField";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import Select from "react-select";
import Fileinput from "@/components/ui/Fileinput";
import useFetch from "@/hooks/useFetch";
import Textinput from "@/components/ui/Textinput";
import MultiSelectComponent from "@/components/ui/MultiSelectComponent";
import api from "@/server/api";
import * as Yup from "yup";
import DateTimePicker from "@/components/form/DateTimePicker";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useQueryClient } from "@tanstack/react-query";

const EditLiveClass = ({
  editData: data,
  showEditModal,
  setShowEditModal,
  setEditData,
}) => {
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const { data: editDataOfApi, isEditDataLoading } = useFetch(
    data?.id
      ? {
          queryKey: `mentor-live-class-details/${data.id}`,
          endPoint: `mentor/live-class-details?id=${data?.id ? data.id : null}`,
        }
      : {
          queryKey: ``,
          endPoint: ``,
        }
  );

  const editData = editDataOfApi?.data;

  const [courseId, setCourseId] = useState(
    editData?.course_id ? editData.course_id : null
  ); // Start with null
  // const [studentList, setStudentList] = useState([]);
  const [batchList, setBatchList] = useState([]);
  const [isloading, setIsLoading] = useState(false);

  // Fetch course list
  const { data: courseList } = useFetch({
    queryKey: `mentor-course-list-for-filter`,
    endPoint: `mentor-course-list-for-filter`,
  });

  // Fetch students based on courseId
  // const { data: students, isStudentLoading } = useFetch(
  //   courseId
  //     ? {
  //         queryKey: `mentor/student-list?course_id=${courseId}`,
  //         endPoint: `mentor/student-list?course_id=${courseId}`,
  //       }
  //     : {
  //         queryKey: ``,
  //         endPoint: ``,
  //       }
  // );

  // Sync editData.course_id with courseId when editData becomes available
  useEffect(() => {
    if (editData?.course_id) {
      setCourseId(editData.course_id); // Automatically set courseId
    }
  }, [editData]);

  // Update studentList when students are fetched
  // useEffect(() => {
  //   if (students?.data) {
  //     setStudentList(students.data);
  //   }
  // }, [students]);

  const { data: batches, isLoading: isBatchLoading } = useFetch(
    courseId
      ? {
          queryKey: `/mentor/batch-list?course_id=${courseId}`,
          endPoint: `/mentor/batch-list?course_id=${courseId}`,
        }
      : {
          queryKey: ``,
          endPoint: ``,
        }
  );

  useEffect(() => {
    if (batches?.data) {
      setBatchList(batches.data);
    }
  }, [batches]);

  const validationSchema = Yup.object({
    title: Yup.string()
      .required("Class title is required")
      .max(100, "Title cannot exceed 100 characters"),
    course_id: Yup.string().required("Please select a course"),
    schedule_datetime: Yup.date().required("Class date & time is required"), // No need to validate format here since we handle it in `handleSubmit`
    duration: Yup.string().required("Duration is required"),
    class_url: Yup.string().required("Class URL is required"),
    // student_ids: Yup.array()
    //   .min(1, "Select at least one student")
    //   .required("Students selection is required"),
    batch_id: Yup.string().required("Batch are required"),
  });

  const handleSubmit = async (values) => {
    try {
      setIsLoading(true);

      // Make API call
      const response = await api.post(
        "mentor/update-live-class-schedule",
        values
      );
      if (response?.data?.status) {
        queryClient.invalidateQueries("live-class-list");
        dispatch(setShowEditModal(false));
      }
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() =>
        dispatch(setShowEditModal(false), dispatch(setEditData(null)))
      }
      title="Edit Live Class"
      className="max-w-4xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() =>
            dispatch(setShowEditModal(false), dispatch(setEditData(null)))
          }
        />
      }
    >
      {!editData && (
        <Icon
          icon="eos-icons:bubble-loading"
          className="text-center text-gray-600 text-2xl mx-auto w-full"
        />
      )}
      {editData && (
        <Formik
          enableReinitialize
          initialValues={{
            ...editData,
            // student_ids: editData?.students?.map((student) => student.id) || [],
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, setFieldValue }) => (
            <Form>
              <div className="space-y-3">
                <div className="lg:flex justify-between gap-4 w-full">
                  <div className="w-full">
                    <InputField
                      label="Class Title"
                      name="title"
                      type="text"
                      placeholder="Live Class Name"
                      required
                    />
                  </div>
                  <div className="w-full">
                    <label className="block text-gray-600 text-sm font-medium mb-2">
                      Course
                      <span className="text-red-500"> *</span>
                    </label>
                    <Select
                      placeholder="Select Course"
                      options={courseList?.data?.map((course) => ({
                        value: course.id,
                        label: course.title,
                      }))}
                      name="course_id"
                      value={
                        courseList?.data
                          ?.map((course) => ({
                            value: course.id,
                            label: course.title,
                          }))
                          .find(
                            (option) => option.value === values.course_id
                          ) || null
                      }
                      onChange={(e) => {
                        console.log(e.value);
                        setCourseId(e.value);
                        setFieldValue("course_id", e.value);
                      }}
                    />

                    <ErrorMessage name="course_id">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>

                  <div className="w-full">
                    <label className="block text-gray-600 text-sm font-medium mb-2">
                      Select Batch
                      <span className="text-red-500"> *</span>
                    </label>
                    <Select
                      placeholder="Select Batch"
                      options={batchList?.map((batch) => ({
                        value: batch.id,
                        label: batch.name,
                      }))}
                      name="batch_id"
                      value={
                        batchList
                          ?.map((batch) => ({
                            value: batch.id,
                            label: batch.name,
                          }))
                          .find((option) => option.value === values.batch_id) ||
                        null
                      }
                      onChange={(e) => {
                        console.log(e.value);
                        // setCourseId(e.value);
                        setFieldValue("batch_id", e.value);
                      }}
                    />

                    <ErrorMessage name="batch_id">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                </div>
                {/* <div className="mt-3">
                  <label className="block text-gray-600 text-sm font-medium mb-2">
                    Select Students
                    <span className="text-red-500"> *</span>
                  </label>
                  <MultiSelectComponent
                    name="student_ids"
                    placeholder="Select Students"
                    options={studentList?.map((student) => ({
                      label: student.name,
                      value: student.id,
                    }))}
                    valueKey="value"
                    labelKey="label"
                  />
                </div> */}
                <div className="lg:flex justify-between gap-4">
                  <div className="w-full">
                    <InputField
                      label="Duration"
                      name="duration"
                      type="text"
                      placeholder="Class Duration"
                      required
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label="Class URL"
                      name="class_url"
                      type="text"
                      placeholder="Class URL"
                      required
                    />
                  </div>
                  <div className="w-full">
                    <DateTimePicker
                      time={true}
                      label="Class Date & Time"
                      placeholder="YYYY-MM-DD"
                      format="YYYY/MM/DD"
                      name="schedule_datetime"
                      onChange={(e) => {
                        setFieldValue("schedule_datetime", e[0]);
                      }}
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="flex justify-end mt-4">
                <Button
                  text="Submit"
                  type="submit"
                  btnClass="btn-primary"
                  isloading={isloading}
                />
              </div>
            </Form>
          )}
        </Formik>
      )}
    </Modal>
  );
};

export default EditLiveClass;
