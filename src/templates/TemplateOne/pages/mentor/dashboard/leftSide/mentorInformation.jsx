import React from "react";
import MentorIcon from "@/assets/MentorDashboard/MentorProfile.svg";
import ProfessionIcon from "@/assets/MentorDashboard/suitcase.svg";
import InstituteIcon from "@/assets/MentorDashboard/public.svg";
import { useSelector } from "react-redux";
import { ASSET_URL } from "@/config";

const mentorInformation = () => {
  const {isAuth} = useSelector(state => state.auth);
  return (
    <div className="p-2 flex flex-wrap justify-center items-center space-x-4 gap-6">
      {/* Mentor Profile Image */}
      <img src={isAuth?.image ? ASSET_URL + isAuth.image : MentorIcon} alt="Mentor" className="w-20 h-20 border rounded-full" />
      {/* Mentor Information */}
      <div>
        <span className="text-cello-900 text-lg font-semibold">
        {isAuth.name}
        </span>
        {/* Profession Name with Icon */}
        <div className="flex items-center text-fuscousGray-700 mt-2 space-x-2 w-full h-6">
          <img
            src={ProfessionIcon}
            alt="Mentor Profile"
            className="w-5 h-5 my-1"
          />
          <p className="text-base font-normal">{isAuth.user_type}</p>
        </div>
        {/* Institute Name with Icon*/}
        {/* <div className="flex items-center text-fuscousGray-700 mt-1 space-x-2 w-full h-6">
          <img src={InstituteIcon} alt="Mentor" className="w-5 h-5 my-1" />
          <p className="text-base font-normal">Institute Name</p>
        </div> */}
      </div>
    </div>
  );
};

export default mentorInformation;
