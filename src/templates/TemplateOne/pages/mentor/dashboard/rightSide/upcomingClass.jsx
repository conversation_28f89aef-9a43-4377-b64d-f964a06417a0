import React from "react";
import Icon from "@/components/ui/Icon";
import { Link } from "react-router-dom";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { setEditData, setShowModal } from "@/store/assignmentStore";
import ClassDetailsModal from "./ClassDetailsModal";

const UpcomingLiveClass = ({ liveClasses }) => {
  const dispatch = useDispatch();
  const { showModal } = useSelector((state) => state.assignmentSlice);
  // Sample Data for live classes
  // const liveClasses = [
  //   {
  //     title: "Live Class Title",
  //     date: "18 October 12:00 AM",
  //     status: "Upcoming",
  //     attendees: 50,
  //   },
  //   {
  //     title: "Live Class Title",
  //     date: "22 October 12:00 AM",
  //     status: "Upcoming",
  //     attendees: 50,
  //   },
  // ];
  // console.log(liveClasses)

  return (
    <div className="">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          <Icon icon="mdi:clock-outline" className="w-6 h-6 text-blue-500" />
          <h3 className="text-base font-semibold text-downriver-950">
            Upcoming Live Class
          </h3>
        </div>
        <Link
          to="/live-class-list"
          className="text-orange-500 font-medium underline"
        >
          See All
        </Link>
      </div>

      {/* Live Class Cards */}
      <div className="space-y-4">
        {liveClasses?.map((liveClass, index) => (
          <div
            key={index}
            className="bg-white p-3 rounded-lg shadow-sm border border-gray-200 relative"
          >
            {/* Top Section: Live Class Title */}
            <div className="flex justify-between items-start pr-24">
              <div className="flex items-center gap-2">
                <Icon
                  icon="ri:live-line"
                  className=" bg-red-100 text-red-600 text-2xl p-1 rounded-full"
                />
                <span className="text-normal font-semibold">
                  {liveClass.title}
                </span>
              </div>
              <span className="absolute right-0 top-2 bg-yellow-100 text-yellow-800 text-sm font-medium p-2 rounded-bl-md rounded-tr-md -mt-2">
                {!liveClass.has_started && !liveClass.has_completed
                  ? "Upcomming"
                  : liveClass.has_started && !liveClass.has_completed
                  ? "Ongoing"
                  : "Finished"}
              </span>
            </div>

            {/* Middle Section: Date and Attendees */}
            <div className="flex items-center justify-between flex-wrap gap-4 mt-3">
              <div className="flex items-center gap-2 bg-gray-100 p-2 rounded-md">
                <Icon
                  icon="mdi:calendar-blank-outline"
                  className="w-5 h-5 text-blue-500"
                />
                <p className="text-sm text-gray-700">
                  {liveClass.schedule_datetime.slice(0, 10)}
                </p>
              </div>
              <div className="flex items-center gap-2 bg-gray-100 p-2 rounded-md">
                <Icon
                  icon="mdi:account-multiple-outline"
                  className="w-5 h-5 text-blue-500"
                />
                <p className="text-sm text-gray-700">
                  {liveClass.students_number}
                </p>
              </div>
              <button
                onClick={() => (dispatch(setShowModal(true), dispatch(setEditData(liveClass))))}
                className="flex items-center gap-2 bg-orange-100 text-orange-600 px-4 py-1 rounded-md hover:bg-orange-200"
              >
                <span>See Details</span>
                <Icon icon="mdi:eye-outline" className="w-3 h-3" />
              </button>
            </div>

            {/* Bottom Section: Actions */}
            {/* <div className="flex justify-end items-center mt-5 pe-2">
              <button className="flex items-center gap-2 bg-gray-100 px-4 py-1 rounded-md text-gray-600 hover:bg-gray-200">
                <span>Edit Class</span>
                <Icon icon="mdi:pencil-outline" className="w-3 h-3" />
              </button>
              
            </div> */}
          </div>
        ))}
      </div>

      {showModal && (
        <ClassDetailsModal showModal={showModal} setShowModal={setShowModal} />
      )}
    </div>
  );
};

export default UpcomingLiveClass;
