import React from "react";
import Icon from "@/components/ui/Icon";

const SyncCalendarCard = () => {
  return (
    <div className="">
      {/* Joined Section */}
      <div className="flex items-center gap-2 mb-4">
        <Icon icon="mdi:link-plus" className="w-5 h-5 text-green-500" />
        <p className="text-green-600 text-sm font-medium">Joined 1 years ago</p>
      </div>

      {/* Sync Calendar Section */}
      <div className="flex items-center gap-2 mb-4">
        <Icon icon="mdi:sync" className="w-5 h-5 text-blue-500" />
        <p className="text-downriver-950 text-sm font-medium">Sync Calendar</p>
      </div>

      {/* Buttons Section */}
      <div className="flex gap-4 mt-2 justify-between">
        <button className="border border-blue-500 text-blue-500 px-2 py-2 rounded-md hover:bg-blue-50">
          Google Calendar
        </button>
        <button className="border border-blue-500 text-blue-500 px-2 py-2 rounded-md hover:bg-blue-50">
          Other Calendar
        </button>
      </div>
    </div>
  );
};

export default SyncCalendarCard;
