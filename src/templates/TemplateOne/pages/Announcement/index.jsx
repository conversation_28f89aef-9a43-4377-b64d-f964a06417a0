import React, { useState } from "react";
import useFetch from "@/hooks/useFetch";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { ASSET_URL } from "@/config";

const Announcement = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);

  const {
    data: announcements,
    isAnnouncementsLoading,
    errorAnnouncements,
  } = useFetch({
    queryKey: "announcements",
    endPoint: "announcements?page=all",
  });

  const handleOpenModal = (announcement) => {
    setSelectedAnnouncement(announcement);
    setIsOpen(true);
  };

  const handleCloseModal = () => {
    setIsOpen(false);
    setSelectedAnnouncement(null);
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-4xl font-bold text-center mb-8">Announcements</h1>

      <div className="min-h-[50vh] flex flex-col items-center justify-center">
        {isAnnouncementsLoading ? (
          <p className="text-center text-gray-500 text-lg">Loading...</p>
        ) : errorAnnouncements ? (
          <p className="text-center text-red-500 text-lg">Something went wrong!</p>
        ) : announcements?.data.length === 0 ? (
          <p className="text-center text-gray-500 text-lg">No announcements available.</p>
        ) : (
          <div className="grid grid-cols-1 gap-6 w-full">
            {announcements?.data.map((announcement) => (
              <div
                key={announcement.id}
                className="bg-white shadow-md hover:shadow-lg transition-all duration-300 rounded-lg p-6 cursor-pointer"
              >
                <h2 className="text-xl font-semibold mb-2">{announcement.title}</h2>
                <p className="text-gray-700 mb-4 line-clamp-3">
                  {announcement.description}
                </p>
                {(announcement.image || announcement.file) && (
                  <Button
                    className="btn-primary"
                    onClick={() => handleOpenModal(announcement)}
                  >
                    Read More
                  </Button>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal */}
      {isOpen && selectedAnnouncement && (
        <Modal
          activeModal={isOpen}
          onClose={handleCloseModal}
          className="max-w-3xl"
          title="Announcement Details"
        >
          <div className="flex flex-col space-y-4">

          <h2 className="text-xl font-bold">
              Title: {selectedAnnouncement.title}
            </h2>
            { selectedAnnouncement.description && <p className="text-gray-600"><strong> Description:</strong> {selectedAnnouncement.description}</p> }

            {selectedAnnouncement.image && (
              <img
                className="w-full h-64 object-cover rounded-lg"
                src={`${ASSET_URL}${selectedAnnouncement.image}`}
                alt={selectedAnnouncement.title}
              />
            )}
            {selectedAnnouncement.file && (
              <a
                href={`${ASSET_URL}${selectedAnnouncement.file}`}
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-secondary"
              >
                Download File
              </a>
            )}
          </div>
        </Modal>
      )}
    </div>
  );
};

export default Announcement;
