import React, { useEffect, useRef, useState } from "react";
import GoBack from "@/components/ui/GoBack";
import { Icon } from "@iconify/react/dist/iconify.js";
import { ASSET_URL } from "@/config";
import avatar from "@/assets/images/avatar/av-1.svg";
import api from "@/server/api";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { toast } from "react-toastify";

const CertificateDetails = ({ details }) => {
  const nameRef = useRef();
  const [loading, setLoading] = useState(false);
  const [share, setShare] = useState(false);
  const [edit, setEdit] = useState(false);
  const certificateInfo = details?.generated_certificate
    ? details.generated_certificate
    : null;
  const [candidateName, setCandidateName] = useState(
    certificateInfo
      ? certificateInfo.name
      : details?.candidate_name
      ? details?.candidate_name
      : ""
  );
  const today = new Date().toJSON().slice(0, 10);

  // Focus the input field when edit is true
  useEffect(() => {
    if (edit) {
      nameRef.current?.focus();
    }
  }, [edit]); // Run the effect when the `edit` state changes

  const handleGetCertificate = async () => {
    try {
      setLoading(true);

      const response = await api.post("/generate-certificates", {
        course_id: details.course_id,
        candidate_name: candidateName,
      });

      if (response.status) {
        setEdit(false);

        const fileURL = ASSET_URL + response.data.data.certificate_file;

        const link = document.createElement("a");
        link.href = fileURL;

        link.download = "certificate.pdf";

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <GoBack title={"Certificate Details"} />

      <div className="lg:grid lg:grid-cols-12 items-center gap-5">
        <div className="col-span-8">
          <div className="bg-blue-50 p-6">
            <div className="bg-white text-gray-600 border-2 border-dashed rounded-lg border-sky-300 relative p-7 text-base space-y-4">
              <div className="flex justify-end">
                <img
                  className="w-52 h-20 object-contain bg-gray-50 rounded-xl"
                  src={ASSET_URL + details.logo}
                  alt=""
                />
              </div>

              <h3 className="text-xl font-semibold font-serif text-center text-sky-600">
                Course Completion Recognition
              </h3>
              <p className="text-base">
                Date:{" "}
                {certificateInfo
                  ? certificateInfo.created_at.slice(0, 10)
                  : today}
              </p>
              <div className="flex items-center">
                {!edit ? (
                  <>
                    <h3 className="text-2xl font-serif font-semibold text-gray-600 mr-2">
                      {candidateName}
                    </h3>
                    {!details.generated_certificate && (
                      <p>
                        <Icon
                          icon="tabler:edit"
                          className="text-gray-400 cursor-pointer text-xl"
                          onClick={() => setEdit(true)}
                        />
                      </p>
                    )}
                  </>
                ) : (
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      ref={nameRef}
                      onChange={(e) => setCandidateName(e.target.value)}
                      className="border-none text-2xl font-serif font-semibold p-2 focus:border-2 focus:border-sky-400"
                      defaultValue={candidateName}
                    />
                    <span className="flex items-center gap-1.5">
                      <Icon
                        icon="mdi:success"
                        className="p-2 rounded-full border text-4xl cursor-pointer border-gray-300 hover:border-green-200"
                        onClick={() => setEdit(false)}
                      />
                      <Icon
                        icon="charm:cross"
                        className="p-2 rounded-full border text-4xl cursor-pointer border-gray-300 hover:border-rose-200"
                        onClick={() => (
                          setEdit(false),
                          setCandidateName(details.candidate_name)
                        )}
                      />
                    </span>
                  </div>
                )}
              </div>
              <h3 className="text-xl font-serif font-semibold text-sky-600">
                Course Name: {details.course_name}
              </h3>
              <p>{details.certification_text}</p>
              <p>
                an hybrid course authorized by{" "}
                <span className="font-semibold">
                  {details.authorize_person}
                </span>
              </p>
              <div>
                <img
                  className="w-52 h-20 border-b-2 object-contain pb-3 border-gray-300"
                  src={ASSET_URL + details.signature}
                  alt=""
                />
                <p>{details.authorize_person}</p>
                <p>{details.organization}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="col-span-4 space-y-3">
          {details.generated_certificate && (
            <h3 className="text-lg font-semibold text-center p-5 border border-red-600 rounded bg-rose-300">
              Can't change name after download.
            </h3>
          )}
          <div className="flex items-center text-lg gap-2 bg-green-100 rounded border border-green-300 p-4">
            <img className="w-20" src={avatar} alt="" />
            <div>
              <p className="font-semibold">Course Completed Successfully</p>
              <p className="font-semibold flex items-center gap-2">
                <Icon icon="fontisto:date" className="text-green-600" />{" "}
                Completation Date: 3rd October
              </p>
              <p className="font-semibold flex items-center gap-2">
                <Icon icon="solar:cup-bold" className="text-green-600" />{" "}
                Obtained Point : 12358
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center gap-4 text-lg mt-5">
        <button
          onClick={() =>
            navigator.clipboard
              .writeText(ASSET_URL + certificateInfo.certificate_file)
              .then(() => {
                toast.success("Link copied to clipboard!", {icon: <Icon icon="solar:copy-outline" className="text-2xl" />});
              })
          }
          className="sm:w-52 px-3 max-sm:py-2 py-3 text-center text-sky-600 block rounded-full border border-sky-600 flex items-center justify-center gap-2 hover:gap-5 hover:bg-sky-700 hover:text-white duration-300"
        >
          Share
          <Icon icon="mdi-light:share" className="text-2xl" />
        </button>
        {/* {console.log(ASSET_URL + certificateInfo.certificate_file)} */}

        <button
          onClick={() => handleGetCertificate()}
          className="sm:w-52 px-3 max-sm:py-2 py-3 text-center text-white block rounded-full bg-sky-600 flex items-center justify-center gap-2 hover:gap-5 hover:bg-sky-700 hover:text-white duration-300"
        >
          Download
          <Icon
            icon={loading ? "eos-icons:loading" : "material-symbols:download"}
            className="text-2xl"
          />
        </button>
      </div>

      {/* {share && (
        <div>
          <Modal
            activeModal={share}
            onClose={() => setShare(false)}
            className="max-w-xl"
            footer={
              <Button
                text="Close"
                btnClass="btn-primary"
                onClick={() => setShare(false)}
              />
            }
          >
            <p>{url}</p>
          </Modal>
        </div>
      )} */}
    </div>
  );
};

export default CertificateDetails;
