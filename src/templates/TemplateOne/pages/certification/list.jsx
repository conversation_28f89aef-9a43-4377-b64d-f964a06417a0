import React, { useState } from "react";
import useFetch from "@/hooks/useFetch";
import { useNavigate } from "react-router-dom";
import GoBack from "@/components/ui/GoBack";
import Icon from "@/components/ui/Icon";
import certificateLoadingImg from "@/assets/images/all-img/certificateLoading.png";
import CertificateDetails from "./certificate-details";
import Button from "@/components/ui/Button";

const CertificateList = () => {
  const [selectedCertificate, setSelectedCertificate] = useState(null);
  const navigate = useNavigate();
  const {
    data: response,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `certificates`,
    endPoint: `certificates`,
  });

  const certificates = response?.data;

  if (isLoading) {
    return (
      <div className="min-h-[450px] flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-4 border-t-sky-500 border-gray-200"></div>
        <p className="mt-4 text-gray-600">Loading certificates...</p>
      </div>
    );
  }

  if (isError || !certificates || certificates.length === 0) {
    return (
      <div className="min-h-[calc(100vh-200px)] flex items-center justify-center p-4">
        <div className="max-w-md mx-auto text-center bg-white p-8 rounded-xl shadow-sm border border-gray-100">
          <div className="inline-flex items-center justify-center bg-sky-50 w-20 h-20 rounded-full mb-6">
            <Icon 
              icon="heroicons:academic-cap" 
              className="text-4xl text-sky-500" 
            />
          </div>
          <h3 className="text-2xl font-bold text-gray-800 mb-3">
            No Certificates Yet
          </h3>
          <p className="text-gray-500 mb-6">
            You haven't earned any certificates yet. Complete a course to unlock your first achievement!
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              text="Explore Courses"
              onClick={() => navigate("/courses")}
              className="btn-sky"
            />
            <Button
              text="My Learning"
              onClick={() => navigate("/my-learning")}
              className="btn-outline"
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-6 xl:py-12">
      {!selectedCertificate ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {certificates.map((certificate, idx) => (
            <div
              key={idx}
              className="relative flex flex-col justify-between bg-white border border-gray-200 rounded-xl shadow hover:shadow-lg transition duration-300 p-6 group"
              onClick={() => setSelectedCertificate(certificate)}
            >
              <div className="absolute top-4 right-4">
                <Icon 
                  icon="heroicons:document-check" 
                  className="text-2xl text-sky-500 opacity-80 group-hover:opacity-100 transition-opacity" 
                />
              </div>
              
              <div className="flex flex-col gap-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-sky-100 rounded-lg">
                    <Icon icon="heroicons:academic-cap" className="text-sky-600 text-xl" />
                  </div>
                  <span className="text-xs font-medium text-sky-600 bg-sky-100 px-3 py-1 rounded-full">
                    Certificate
                  </span>
                </div>
                
                <h2 className="text-lg font-semibold text-gray-800 line-clamp-2">
                  {certificate?.course_name}
                </h2>

                {/* Progress Bar */}
                <div className="mt-4">
                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                    <span>Progress</span>
                    <span>{certificate.progress.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-sky-500 to-blue-600 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${certificate?.progress}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100 flex justify-between items-center">
                <span className="text-xs text-gray-500">
                  {certificate.completed_at ? `Completed on ${new Date(certificate.completed_at).toLocaleDateString()}` : "In Progress"}
                </span>
                <button className="text-sky-600 hover:text-sky-700 text-sm font-medium flex items-center gap-1">
                  View 
                  <Icon icon="heroicons:arrow-right" className="text-sm" />
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="relative">
          <GoBack onClick={() => setSelectedCertificate(null)} className="mb-6" />
          <CertificateDetails details={selectedCertificate} />
        </div>
      )}
    </div>
  );
};

export default CertificateList;