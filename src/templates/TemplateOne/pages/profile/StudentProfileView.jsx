import React, { useRef, useState, useEffect } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import useFetch from "@/hooks/useFetch";
import Loading from "@/components/Loading";
import GoBack from "@/components/ui/GoBack";
import { ASSET_URL } from "@/config";
import userImg from "@/assets/images/all-img/user5.jpeg";
import { toast } from "react-toastify";
import api from "@/server/api";
import { useQueryClient } from "@tanstack/react-query";
import { useDispatch, useSelector } from "react-redux";
import { setUser } from "@/pages/auth/common/store";

const ProfileInfoItem = ({
  title,
  data,
  icon,
  inputName,
  inputType,
  inputPlaceholder,
  textStyle,
  options,
}) => {
  const { labels } = useSelector((state) => state.language);
  const [edit, setEdit] = useState(false);
  const [inputValue, setInputValue] = useState(data || "");
  const [submitLoading, setSubmitLoading] = useState(false);
  const queryClient = useQueryClient();

  useEffect(() => {
    setInputValue(data || "");
  }, [data]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitLoading(true);
    
    try {
      const response = await api.post("/update-student-profile", { 
        [inputName]: inputValue 
      });
      
      if (response.data?.data) {
        setInputValue(response.data.data[inputName]);
        queryClient.invalidateQueries("/profile");
      }
    } catch (error) {
      console.log(error);
    } finally {
      setSubmitLoading(false);
      setEdit(false);
    }
  };

  return (
    <div className="flex items-start gap-4 p-4 hover:bg-gray-50 rounded-lg transition-colors">
      <div className="p-2.5 bg-gray-100 rounded-lg">
        <Icon icon={icon} className="text-lg text-gray-600" />
      </div>
      
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
          {labels[title.toLowerCase()] || title}
        </p>
        
        {edit ? (
          <form onSubmit={handleSubmit} className="space-y-2">
            {inputType === "select" ? (
              <select
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">{labels['select'] || "Select"}...</option>
                {options?.map((opt) => (
                  <option key={opt.value} value={opt.value}>
                    {labels[opt.label.toLowerCase()] || opt.label}
                  </option>
                ))}
              </select>
            ) : inputType === "textarea" ? (
              <textarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md text-sm h-20 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder={labels[inputPlaceholder.toLowerCase()] || inputPlaceholder}
              />
            ) : (
              <input
                type={inputType}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder={labels[inputPlaceholder.toLowerCase()] || inputPlaceholder}
              />
            )}
            
            <div className="flex gap-2 justify-end">
              <button
                type="button"
                onClick={() => {
                  setEdit(false);
                  setInputValue(data);
                }}
                className="px-3 py-1.5 text-sm text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition"
              >
                {labels['cancel'] || "Cancel"}
              </button>
              <button
                type="submit"
                disabled={submitLoading}
                className="px-3 py-1.5 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700 transition disabled:opacity-70"
              >
                {submitLoading ? (
                  <span className="flex items-center gap-1">
                    <Icon icon="svg-spinners:3-dots-bounce" className="text-sm" />
                    {labels['saving'] || "Saving..."}
                  </span>
                ) : (
                  labels['save'] || "Save"
                )}
              </button>
            </div>
          </form>
        ) : (
          <div className="group flex justify-between items-center">
            <p className={`text-sm ${textStyle || "text-gray-800"} truncate`}>
              {inputValue || (
                <span className="text-gray-400 italic">
                  {labels['not provided'] || "Not provided"}
                </span>
              )}
            </p>
            <button
              onClick={() => setEdit(true)}
              className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-blue-600 transition"
            >
              <Icon icon="mdi:pencil" className="text-lg" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

const StudentProfileView = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [imagePreview, setImagePreview] = useState(null);
  const [imageFile, setImageFile] = useState(null);
  const fileInputRef = useRef();
  const dispatch = useDispatch();
  const { labels } = useSelector((state) => state.language);

  const { data: data, isLoading } = useFetch({
    queryKey: "user-data",
    endPoint: "/profile",
  });
  const userData = data?.data;

  const handleImageUpload = async () => {
    if (!imageFile) return;
    
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append("image", imageFile);
      
      const response = await api.filepost("update-student-profile", formData);
      if (response.status === 200) {
        const localUser = JSON.parse(localStorage.getItem("isAuth"));
        localUser.image = response.data?.data?.image;
        dispatch(setUser(localUser));
        setImagePreview(null);
        setImageFile(null);
      }
    } catch (error) {
      toast.error(labels['upload failed'] || "Upload failed");
    } finally {
      setLoading(false);
    }
  };

  if (isLoading) return <Loading className="min-h-screen" />;

  const tabs = [
    {
      id: "basic",
      label: labels['basic information'] || "Basic Information",
      icon: "mdi:account-details",
      content: (
        <>
          <ProfileInfoItem
            title={labels['date of birth'] || "Date of Birth"}
            icon="mdi:calendar"
            data={userData?.date_of_birth}
            inputName="date_of_birth"
            inputType="date"
            inputPlaceholder={labels['enter date of birth'] || "Enter date of birth"}
          />
          <ProfileInfoItem
            title={labels['gender'] || "Gender"}
            icon="mdi:gender-male-female"
            data={userData?.gender}
            inputName="gender"
            inputType="select"
            options={[
              { label: "Male", value: "Male" },
              { label: "Female", value: "Female" },
              { label: "Other", value: "Other" },
            ]}
            inputPlaceholder={labels['select gender'] || "Select gender"}
          />
          <ProfileInfoItem
            title={labels['blood group'] || "Blood Group"}
            icon="mdi:water"
            data={userData?.blood_group}
            inputName="blood_group"
            inputType="select"
            options={[
              { label: "A+", value: "A+" },
              { label: "A-", value: "A-" },
              { label: "B+", value: "B+" },
              { label: "B-", value: "B-" },
              { label: "AB+", value: "AB+" },
              { label: "AB-", value: "AB-" },
              { label: "O+", value: "O+" },
              { label: "O-", value: "O-" },
            ]}
            inputPlaceholder={labels['select blood group'] || "Select blood group"}
          />
          <ProfileInfoItem
            title={labels['nid no'] || "NID No"}
            icon="mdi:card-account-details"
            data={userData?.nid_no}
            inputName="nid_no"
            inputType="text"
            inputPlaceholder={labels['enter nid number'] || "Enter NID number"}
          />
          <ProfileInfoItem
            title={labels['passport no'] || "Passport No"}
            icon="mdi:passport"
            data={userData?.passport_no}
            inputName="passport_no"
            inputType="text"
            inputPlaceholder={labels['enter passport number'] || "Enter passport number"}
          />
        </>
      )
    },
    {
      id: "address",
      label: labels['address'] || "Address",
      icon: "mdi:map-marker",
      content: (
        <>
          <ProfileInfoItem
            title={labels['current address'] || "Current Address"}
            icon="mdi:home-city"
            data={userData?.current_address}
            inputName="current_address"
            inputType="textarea"
            inputPlaceholder={labels['enter current address'] || "Enter current address"}
          />
          <ProfileInfoItem
            title={labels['permanent address'] || "Permanent Address"}
            icon="mdi:home-group"
            data={userData?.permanent_address}
            inputName="permanent_address"
            inputType="textarea"
            inputPlaceholder={labels['enter permanent address'] || "Enter permanent address"}
          />
        </>
      )
    },
    {
      id: "education",
      label: labels['education'] || "Education",
      icon: "mdi:school",
      content: (
        <>
          <ProfileInfoItem
            title={labels['education'] || "Education"}
            icon="mdi:school"
            data={userData?.education}
            inputName="education"
            inputType="text"
            inputPlaceholder={labels['enter education'] || "Enter education"}
          />
          <ProfileInfoItem
            title={labels['institute'] || "Institute"}
            icon="mdi:office-building"
            data={userData?.institute}
            inputName="institute"
            inputType="text"
            inputPlaceholder={labels['enter institute'] || "Enter institute"}
          />
          {userData?.user?.user_type != 'Student' &&
          <ProfileInfoItem
            title={labels['profession'] || "Profession"}
            icon="mdi:briefcase"
            data={userData?.profession}
            inputName="profession"
            inputType="text"
            inputPlaceholder={labels['enter profession'] || "Enter profession"}
          />}
        </>
      )
    },
    {
      id: "family",
      label: labels['family info'] || "Family Info",
      icon: "mdi:account-group",
      content: (
        <>
          <ProfileInfoItem
            title={labels['father\'s name'] || "Father's Name"}
            icon="mdi:account"
            data={userData?.father_name}
            inputName="father_name"
            inputType="text"
            inputPlaceholder={labels['enter father\'s name'] || "Enter father's name"}
          />
          <ProfileInfoItem
            title={labels['mother\'s name'] || "Mother's Name"}
            icon="mdi:account"
            data={userData?.mother_name}
            inputName="mother_name"
            inputType="text"
            inputPlaceholder={labels['enter mother\'s name'] || "Enter mother's name"}
          />
        </>
      )
    }
  ];

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <GoBack 
        title={(labels['student'] ? labels['student'] + " Profile" : "Student Profile")}
        className="mb-6"
      />
      
      {/* Profile Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-8">
        <div className="flex flex-col md:flex-row">
          {/* Profile Picture Section */}
          <div className="md:w-1/3 p-8 flex flex-col items-center bg-gray-50 border-b md:border-b-0 md:border-r border-gray-200">
            <div className="relative mb-6">
              <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-md">
                <img
                  src={
                    imagePreview ||
                    (userData?.image ? `${ASSET_URL}${userData.image}` : userImg)
                  }
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              </div>
              
              <div className="absolute -bottom-2 -right-2">
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={(e) => {
                    if (e.target.files?.[0]) {
                      setImageFile(e.target.files[0]);
                      setImagePreview(URL.createObjectURL(e.target.files[0]));
                    }
                  }}
                  className="hidden"
                  accept="image/*"
                />
                <button
                  onClick={() => fileInputRef.current.click()}
                  className="p-2 bg-white rounded-full shadow-md text-blue-600 hover:bg-blue-50 transition"
                >
                  <Icon icon="mdi:camera" className="text-xl" />
                </button>
              </div>
            </div>
            
            {imagePreview && (
              <div className="flex gap-2 w-full mb-4">
                <button
                  onClick={handleImageUpload}
                  disabled={loading}
                  className="flex-1 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition disabled:opacity-70"
                >
                  {loading ? (
                    <span className="flex items-center justify-center gap-1">
                      <Icon icon="svg-spinners:3-dots-bounce" className="text-sm" />
                      {labels['uploading'] || "Uploading..."}
                    </span>
                  ) : (
                    labels['save photo'] || "Save Photo"
                  )}
                </button>
                <button
                  onClick={() => {
                    setImagePreview(null);
                    setImageFile(null);
                  }}
                  className="px-3 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-200 transition"
                >
                  {labels['cancel'] || "Cancel"}
                </button>
              </div>
            )}
            
            {/* Name, Email, and Phone under the image */}
            <div className="w-full space-y-3">
              <div>
                <h2 className="text-xl font-semibold text-gray-800 text-center">
                  {userData?.name || ""}
                </h2>
                <ProfileInfoItem
                  title={labels['email'] || "Email"}
                  icon="mdi:email"
                  data={userData?.email}
                  inputName="email"
                  inputType="email"
                  inputPlaceholder={labels['enter email'] || "Enter email"}
                  textStyle="text-gray-600 text-sm"
                />
              </div>
              <ProfileInfoItem
                title={labels['contact number'] || "Contact Number"}
                icon="mdi:phone"
                data={userData?.contact_no}
                inputName="contact_no"
                inputType="tel"
                inputPlaceholder={labels['enter contact number'] || "Enter contact number"}
                textStyle="text-gray-600 text-sm"
              />
            </div>
          </div>
          
          {/* Profile Content Section */}
          <div className="md:w-2/3 p-8">
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-800 mb-1">
                {labels['profile details'] || "Profile Details"}
              </h1>
              <p className="text-gray-500">
                {labels['manage your personal information'] || "Manage your personal information"}
              </p>
            </div>
            
            {/* Tabs */}
            <div className="border-b border-gray-200 mb-6">
              <nav className="flex space-x-8">
                {tabs.map((tab, index) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(index)}
                    className={`py-3 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                      activeTab === index
                        ? "border-blue-600 text-blue-600"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    }`}
                  >
                    <Icon icon={tab.icon} className="text-lg" />
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>
            
            {/* Tab Content */}
            <div className="space-y-4">
              {tabs[activeTab]?.content}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentProfileView;