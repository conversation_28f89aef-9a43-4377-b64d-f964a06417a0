import React, { useState } from 'react'
import Card from "@/components/ui/Card";


import ChooseTemplate from "./StepForNewLMS/ChooseTemplate";
import One from "./StepForNewLMS/One";
import Two from "./StepForNewLMS/Two";
import Three from "./StepForNewLMS/Three";
import RegistrationSuccessful from "./RegistrationSuccessful";
import { useSelector } from "react-redux";

const TryNewLMS = () => {
  const [percent, setPercent] = useState(25);
  const [template, setTemplate] = useState(null);
  const [dataToSubmit, setDataToSubmit] = useState({});
  const items = [ {
    id: 1,
    title: 'Choose Template',
    description: 'Choose a template to get started',
    onClick: () => setPercent(0),
  }, {
    id: 2,
    title: 'LMS Details',
    description: 'Enter LMS details',
    onClick: () => setPercent(25),
  }, {
    id: 3,
    title: 'How It Looks',
    description: 'Finish creating LMS',
    onClick: () => setPercent(50),
  }, {
    id: 4,
    title: 'User Information',
    description: 'Choose a template to get started',
    onClick: () => setPercent(75),
  }
];

const createData = (value) => {
  let obj = {
    template_id: 1,
    ...dataToSubmit,
    ...value
  };
  setDataToSubmit(obj);

  setTimeout(() => console.log(dataToSubmit), 1000);
}
  return (
    <div className="container mx-auto">
    { percent == 100 ? <RegistrationSuccessful /> : <>
      <Card title="LMS Registration" className="b-0" noborder>
        
        <div className="grid grid-cols-4 text-center gap-0 px-[-4px] mb-6">
          {items.map((i, index) => (
            <div key={index}>
            <div className="flex items-center justify-center mb-2">
              
              <div className={i.id > 1 ? ( index <= percent / 25 ? "bg-primary-600 border border-2 border-primary-600 h-[10px] w-1/2 " :"bg-gray-200 border border-2 border-gray-200 h-[10px] w-1/2 " ) : `h-[10px] w-1/2`}></div>
              
              <button
                type="button"
                className={`inline-flex items-center justify-center rounded-full p-4 border border-transparent shadow-sm text-lg font-medium ${
                  index == percent / 25 ? "bg-primary-600 text-white" : index < percent / 25 ? "bg-green-600 text-white" : "bg-gray-200"
                }`}
                // onClick={() => setPercent(index * 25)}
                style={{width: '56px', height: '56px'}}
              >
                {i.id}
              </button>

              {/* <button
                type="button"
                className={`inline-flex items-center p-4 border border-transparent rounded-full shadow-sm font-medium ${
                  index == percent / 25 ? "bg-primary-600 text-white" : index < percent / 25 ? "bg-green-600 text-white" : "bg-gray-200"
                }`}
                onClick={() => setPercent(index * 25)}
              >
                {i.id}
              </button> */}
              <div className={  i.id < 4 ? (index < percent / 25 ? "bg-primary-600 border border-2 border-primary-600 h-[10px] w-1/2" : "bg-gray-200 border border-2 border-gray-200 h-[10px] w-1/2") : `h-[10px] w-1/2`}></div>
            </div>
            <span> {i.title }</span>
            </div>
          ))}
        </div>
      {percent == 0 && <ChooseTemplate template={template} setPercent={setPercent} setTemplate={setTemplate} createData={createData} dataToSubmit={dataToSubmit}/>}
      {percent == 25 && <One setPercent={setPercent} template={template} dataToSubmit={dataToSubmit} createData={createData}/>}
      {percent == 50 && <Two setPercent={setPercent} template={template} createData={createData}  dataToSubmit={dataToSubmit} />}
      {percent == 75 && <Three setPercent={setPercent} template={template} createData={createData} dataToSubmit={dataToSubmit} />}
      
      </Card>
      </>}
    </div>
  )
}
export default TryNewLMS;


