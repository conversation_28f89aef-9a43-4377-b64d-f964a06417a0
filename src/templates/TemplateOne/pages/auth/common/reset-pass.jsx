import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import api from "@/server/api";
import Input from "@/components/form/Common/Input";
import phoneMailIcon from "@/assets/images/auth/phone mail.svg";
import lockIcon from "@/assets/images/auth/lockIcon.svg";
import * as Yup from "yup";
import { Form, Formik } from "formik";
import { toast } from "react-toastify";

const ResetPass = ({ handleNextStep, otpData, loginMail }) => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const initialValues = {
    password: "",
    otp_id: otpData,
  };

  const validationSchema = Yup.object().shape({
    password: Yup.string()
      .min(8, "Password must be at least 8 characters")
      .matches(/[A-Z]/, "Password must contain at least one uppercase letter")
      .matches(/\d/, "Password must contain at least one number")
      .required("Password is required"),
    confirm_password: Yup.string().oneOf(
      [Yup.ref("password"), null],
      "Passwords must match"
    ),
  });

  const handleSubmit = async (values) => {
    try {
      setLoading(true);
        const response = await api.post(
          import.meta.env.VITE_BASE_URL + "/api/reset-password",
          {...values, otp_id: otpData}
        );
      if(response.data.data){
        navigate('/');
      }

      //   handleNextStep("mail_or_phone", {
      //     data: response.data.data,
      //     phone_or_email: values.phone_or_email,
      //   });

      // if (values) {
      //   handleNextStep();
      // }
      //   if(values){
      //     navigate()
      //   }
    } catch (error) {
      console.log(error);
      toast.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
    // console.log(values);
  };

  return (
    <div className="space-y-6 max-sm:py-16 w-[355px]">
      <p className="text-sky-600 font-semibold text-2xl max-sm:text-xl mb-4">
        Reset Password
      </p>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {() => (
          <Form>
            <p className="text-xl border-b-2 border-gray-300 pb-2">
              <img className="pr-2 pl-1" src={phoneMailIcon} alt="" />{" "}
              {loginMail}
            </p>
            <Input
              name="password"
              id="password"
              type="password"
              placeholder="Password"
              icon={lockIcon}
              required={true}
              label="Password"
            />
            <Input
              name="confirm_password"
              id="confirm_password"
              type="password"
              placeholder="Confirm Password"
              icon={lockIcon}
              required={true}
              label="Confirm Password"
            />
            <button
              type="submit"
              className={`w-full bg-[#1B69B3] text-xl text-white py-2.5 rounded-md shadow-lg mt-5 ${
                loading ? "bg-gray-400" : "hover:bg-sky-700"
              } focus:outline-none`}
              disabled={loading}
            >
              {loading ? "Loading..." : "Next"}
            </button>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default ResetPass;
