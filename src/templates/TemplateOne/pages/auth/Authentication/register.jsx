import React, { useEffect, useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { Icon } from "@iconify/react";
import studentImg from "@/assets/images/auth/student.png";
import teacherImg from "@/assets/images/auth/teacher.png";
import phoneMailIcon from "@/assets/images/auth/phone mail.svg";
import personIcon from "@/assets/images/auth/person.svg";
import lockIcon from "@/assets/images/auth/lock.svg";
import { useNavigate, Link, useLocation } from "react-router-dom";
import img4 from "@/assets/images/auth/img4.png";
import img2 from "@/assets/images/auth/img2.png";
import img3 from "@/assets/images/auth/img3.png";
import Input from "@/components/form/Common/Input";
import api from "@/server/api";
import RegistrationSuccessful from "../RegistrationSuccessful";
import { useDispatch } from "react-redux";
import { handleRegister } from "../common/store";

const Register = ({ loginMail, otpId }) => {
  const [loading, setLoading] = useState(false);
  const [student, setStudent] = useState(true); // Default to student
  const [mentor, setMentor] = useState(false);
  const navigate = useNavigate();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [currentStep, setCurrentStep] = useState(1); // Start from step 1
  const images = [img2, img3, img4];
  const dispatch = useDispatch();
  const location = useLocation();

  const initialValues = {
    name: "",
    // phone_or_email: "",
    password: "",
    confirm_password: "",
    user_type: "Student",
    otp_id: otpId,
  };

  const validationSchema = Yup.object().shape({
    name: Yup.string().required("Name is required"),
    password: Yup.string()
      .min(8, "Password must be at least 8 characters")
      .matches(/[A-Z]/, "Password must contain at least one uppercase letter")
      .matches(/\d/, "Password must contain at least one number")
      .required("Password is required"),
    confirm_password: Yup.string().oneOf(
      [Yup.ref("password"), null],
      "Passwords must match"
    ),
    user_type: Yup.string().required("Please select user type"),
  });

  const onSubmit = async (values, { setSubmitting }) => {
    try {
      setLoading(true);
      console.log(values);
      const response = await api.post(
        import.meta.env.VITE_BASE_URL + "/api/register-user",
        { ...values, otp_id: otpId }
      );
      const token = response.data.data.token;
      if (token) {
        api.setTokenHeader(token);
        dispatch(handleRegister(response.data.data));
        navigate(location.state?.from ? location.state?.from : "/");
        // localStorage.setItem("_token", token);
        // navigate("/");
      }
    } catch (error) {
      // console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Function to handle role selection
  const handleUserTypeSelection = (role, setFieldValue) => {
    setFieldValue("user_type", role);
    if (role === "Student") {
      setStudent(true);
      setMentor(false);
    } else if (role === "Mentor") {
      setStudent(false);
      setMentor(true);
    }
  };

  useEffect(() => {
    const intervalId = setInterval(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
        setIsTransitioning(false);
      }, 500);
    }, 3000);

    return () => clearInterval(intervalId);
  }, [images.length]);

  return (
    <div className="max-sm:w-[355px] md:max-w-xl">
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({ values, setFieldValue }) => (
          <Form autoComplete="off">
            <div className="flex gap-4">
              <div
                onClick={() =>
                  handleUserTypeSelection("Student", setFieldValue)
                }
                className={`relative shadow-md border-2 ${
                  student
                    ? "border-green-300 bg-sky-50"
                    : "border-sky-100 bg-white"
                } rounded-xl cursor-pointer px-3 py-2 pt-8`}
              >
                <img
                  className={`w-20 h-20 max-sm:w-16 max-sm:h-16 rounded-full object-cover border-2 ${
                    student ? "border-green-400" : "border-sky-100"
                  } absolute left-5 -top-12 max-sm:-top-9`}
                  src={studentImg}
                  alt="Student"
                />
                {student && (
                  <>
                    <input
                      type="checkbox"
                      className="peer absolute right-2 top-3 hidden"
                      checked={student}
                      readOnly
                    />
                    <Icon
                      icon="el:ok"
                      className="absolute right-2 top-3 w-4 h-4 bg-green-500 rounded text-white p-1"
                    />
                  </>
                )}
                <h2 className="text-lg text-sky-600">Student</h2>
                <p className="text-gray-500">
                  Would you like to sign up as a student?
                </p>
              </div>

              <div
                onClick={() => handleUserTypeSelection("Mentor", setFieldValue)}
                className={`relative shadow-md border-2 ${
                  mentor
                    ? "border-green-300 bg-sky-50"
                    : "border-sky-100 bg-white"
                } rounded-xl cursor-pointer px-3 py-2 pt-8`}
              >
                <img
                  className={`w-20 h-20 max-sm:w-16 max-sm:h-16 rounded-full object-cover border-2 ${
                    mentor ? "border-green-400" : "border-sky-100"
                  } absolute left-5 -top-12 max-sm:-top-9`}
                  src={teacherImg}
                  alt="Teacher"
                />
                {mentor && (
                  <>
                    <input
                      type="checkbox"
                      className="peer absolute right-2 top-3 hidden"
                      checked={mentor}
                      readOnly
                    />
                    <Icon
                      icon="el:ok"
                      className="absolute right-2 top-3 w-4 h-4 bg-green-500 rounded text-white p-1"
                    />
                  </>
                )}
                <h2 className="text-lg text-sky-600">Teacher</h2>
                <p className="text-gray-500">
                  Would you like to sign up as a teacher?
                </p>
              </div>
            </div>

            <h2 className="text-3xl max-sm:text-2xl text-gray-500 mb-4 mt-8">
              Enter Your Information
            </h2>

            <div className="space-y-3 max-sm:space-y-1">
              {/* <Input
                name="phone_or_email"
                id="phone_or_email"
                type="text"
                placeholder="Enter your Mobile/Email"
                icon={phoneMailIcon}
                defaultValue={loginMail}
                required={true}
                disabled={true}
                label="Enter Your Email/Number"
              /> */}
              <p className="text-xl border-b-2 border-gray-300 pb-2">
                <img className="pr-2 pl-1" src={phoneMailIcon} alt="" />{" "}
                {loginMail}
              </p>
              <Input
                name="name"
                id="name"
                type="text"
                placeholder="Write your name"
                icon={personIcon}
                required={true}
                label="Your Name"
                autoComplete="off"
              />
              <Input
                name="password"
                id="password"
                type="password"
                placeholder="Password"
                icon={lockIcon}
                required={true}
                label="Password"
                autoComplete="off"
              />
              <Input
                name="confirm_password"
                id="confirm_password"
                type="password"
                placeholder="Confirm Password"
                icon={lockIcon}
                required={true}
                label="Confirm Password"
              />
            </div>

            <button
              type="submit"
              className={`w-full bg-[#1B69B3] text-xl text-white py-2.5 rounded-md shadow-lg mt-8 ${
                loading ? "bg-gray-400" : "hover:bg-sky-700"
              } focus:outline-none`}
              disabled={loading}
            >
              {loading ? "Loading..." : "Submit"}
            </button>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Register;
