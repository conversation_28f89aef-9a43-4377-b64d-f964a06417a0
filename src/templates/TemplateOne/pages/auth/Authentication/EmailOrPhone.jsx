import React, { useState } from "react";
import icon from "@/assets/images/auth/pgone emal.svg";
import Input from "@/components/form/Common/Input";
import { Form, Formik } from "formik";
import * as Yup from "yup";
import { useNavigate, useLocation } from "react-router-dom";
import api from "@/server/api";
import { useQuery } from "@tanstack/react-query";
import { Icon } from "@iconify/react/dist/iconify.js";


const EmailOrPhone = ({ handleNextStep }) => {

  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const { data = [] } = useQuery({
    queryKey: ["organization_details"],
    queryFn: async () => {
      const { data } = await api.get(
        import.meta.env.VITE_BASE_URL + "/api/website/organization-details"
      );
      return data;
    },
  });

  const organization_id = data.data?.id;

  const initialValues = {
    phone_or_email: "",
  };

  const validationSchema = Yup.object().shape({
    phone_or_email: Yup.string()
      .required("Email or phone number is required")
      .test(
        "email-or-phone",
        "Must be a valid phone number or email address",
        function (value) {
          // Regex for Bangladeshi phone number
          const phoneRegex = /^(?:\+88|88)?(01[3-9]\d{8})$/;
  
          // Regex for valid email address
          const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  
          // Check if the value matches either regex
          const isValidPhone = phoneRegex.test(value);
          const isValidEmail = emailRegex.test(value);
  
          return isValidPhone || isValidEmail;
        }
      ),
  });

  const handleSubmit = async (values) => {
    try {
      // console.log(location.state?.from);
      setLoading(true);
      const response = await api.post(
        import.meta.env.VITE_BASE_URL + "/api/login-with-mobile",
        {
          phone_or_email: values.phone_or_email.replace(/^\+88/, ""),
          organization_id,
        }, 
        false
      );

      handleNextStep("mail_or_phone", {
        data: response.data.data,
        phone_or_email: values.phone_or_email,
      });

 
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
    // console.log(values);
  };

  return (
    <div className="space-y-6 max-sm:py-16 w-[355px]">
      <p className="text-sky-600 font-semibold text-2xl max-sm:text-xl mb-4">
        Register or Login to Continue
      </p>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {() => (
          <Form>
            <Input
              name="phone_or_email"
              id="phone_or_email"
              type="text"
              placeholder={"Verify your email address"}
              icon={icon}
              required={true}
              label={"Enter Your Email/Number"}
            />
            <button
              type="submit"
              className={`w-full bg-[#1B69B3] text-xl text-white py-2.5 rounded-md shadow-lg mt-5 ${
                loading ? "bg-gray-400" : "hover:bg-sky-700"
              } focus:outline-none`}
              disabled={loading}
            >
              {loading ? "Loading..." : "Next"}
            </button>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EmailOrPhone;
