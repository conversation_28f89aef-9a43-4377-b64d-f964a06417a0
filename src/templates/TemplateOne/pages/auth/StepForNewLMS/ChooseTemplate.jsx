import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import useFetch from '@/hooks/useFetch';
import {ASSET_URL} from '@/config';
import Modal from "@/components/ui/Modal";
import { useDispatch } from "react-redux";
import Loading2 from "@/components/Loading2";
// import { setTemplate } from "@/pages/auth/StepForNewLMS/store";

const ChooseTemplate = ({template, setPercent, setTemplate, createData, dataToSubmit}) => {
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const [isNotSelected, setIsNotSelected] = useState( false );
  const [templateIndex, setTemplateIndex] = useState(1);
  const [previewTemplate, setPreviewTemplate] = useState(null);

  console.log(template);
  console.log(dataToSubmit);
  const { data: templates, isLoading, isError } = useFetch({
    queryKey: 'templates',
    endPoint: 'templates',
  });

  const setData = (value) => {
    setTemplate(value);
    setIsNotSelected(false);
  }
  const goNextPage = () => {
    if (templateIndex != null) {
  
      createData({template_id: templates.data[templateIndex].id});
      setPercent(25);
    } else {
      setIsNotSelected(true);
    }
  }
  useEffect(() => {
    if (template) {
      const index = templates?.data?.findIndex(t => t.id === template.id);
      setTemplateIndex(index);
    }
  }, [template, templates]);

  if (isLoading) return <Loading2 />;
  return ( 
    <div className={`w-full p-4 space-y-4 ${isNotSelected && "border border-red-300"}`}>
      <h1 className="text-3xl font-bold">Choose a Template</h1>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {templates?.data.map((template, index) => (
          <div
            key={index}
            className="card border border-gray-300 rounded-lg shadow-md relative"
          >
            <div className={`flex justify-end items-start ${templateIndex == index && 'relative'}`}>
              {templateIndex == index && (
                <div className="absolute top-2 right-2 bg-green-500 rounded-full w-8 h-8 flex justify-center items-center text-white">
                  <Icon icon="fa-solid:check" />
                </div>
              )}
              <img
                src={ASSET_URL + template.theme_image}
                alt={template.title}
                className="mx-auto mt-4 card-img-top rounded-lg h-[200px] w-full object-center"
              />
            </div>


            <div className="card-body p-4">
              <h5 className="card-title text-lg font-semibold">{template.title}</h5>
              <p className="card-text text-gray-600">{template.description}</p>
            </div>

            <div className="flex justify-between">
                <button onClick={() => { 
                  setData(template);
                  setTemplateIndex(index);
                  }} className={`mt-4 w-1/2 px-10 py-1 text-blue-400 hover:bg-blue-400 border-t border-r ${templateIndex == index && 'border-r bg-blue-400'} border-slate-300 block hover:text-white`}>
                  {templateIndex == index && (
                    <span className="text-white">
                      Selected
                    </span>
                  )}
                  {templateIndex != index && (
                    <span>
                      Select
                    </span>
                  )}
                </button>
                <button onClick={() => { setIsOpen(true); setPreviewTemplate(template)}} className="mt-4 w-1/2 px-10 py-1 text-blue-400 hover:bg-blue-400 border-t border-slate-300 block hover:text-white hover:border-blue-400">
                  Preview 
                </button>

     
              </div>
          </div>
        ))}

      </div>
      
      <div className="flex justify-end mt-4">
          <button onClick={() => goNextPage()} className="flex items-center px-4 py-2 text-white rounded-lg bg-blue-500 hover:bg-blue-600">
            
            Next 
            <Icon icon="akar-icons:chevron-right" className="ml-2" />
          </button>
        </div>
      {isOpen && (
              <Modal activeModal={isOpen} onClose={() => setIsOpen(false)} className='max-w-5xl' title={previewTemplate?.title}>
                <div className="p-4">
                  <div className="grid grid-cols-3 gap-4">
                    {previewTemplate?.items.length === 0 ? (
                      <p className='text-center text-lg'>No item has been added</p>
                    ) : (
                      previewTemplate?.items.map((item, index) => (
                        <div key={index} className="flex flex-col items-center space-y-2">
                          <img src={ASSET_URL + item.image} className="mx-auto mt-4 h-[150px] w-auto object-center" alt={item.title} />
                          <p className="text-center text-lg font-semibold">{item.title}</p>
                        </div>
                      ))
                    )}
                  </div>
                </div>
                
              </Modal>
            )}
    </div>
  );
};

export default ChooseTemplate;

