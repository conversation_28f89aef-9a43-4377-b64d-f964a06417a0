import React, { useEffect, useState } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { Icon } from '@iconify/react';
import api from '@/server/api';
import { BASE_URL } from '@/config';
import Button from "@/components/ui/Button";

const Three = ({ template, dataToSubmit, setPercent, createData }) => {
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const initialValues = {
    user_name: dataToSubmit?.user_name || '',
    user_username: dataToSubmit?.user_username || '',
    user_email: dataToSubmit?.user_email || '',
    user_password: '',
    password_confirmation: '',
  };

  const validationSchema = Yup.object().shape({
    user_name: Yup.string().required('Please enter your name'),
    user_username: Yup.string().required('Please enter your username'),
    user_email: Yup.string().email('Invalid email format').required('Please enter your email'),
    user_password: Yup.string().required('Please enter your password'),
    password_confirmation: Yup.string()
      .oneOf([Yup.ref('user_password'), null], 'Passwords must match')
      .required('Please confirm your password'),
  });

  const onSubmit = async (values, { setSubmitting, setErrors }) => {
    setLoading(true);
    const formData = new FormData();
    const mergedData = { ...dataToSubmit, ...values };

    Object.keys(mergedData).forEach((key) => {
      formData.append(key, mergedData[key]);
    });

    try {
      await api.filepost(`${BASE_URL}/organization-register`, formData);
      setPercent(100); // Progress indicator
    } catch (error) {
      if (error.response?.data?.errors) {
        setErrors(error.response.data.errors);
      }
      console.error(error);
    } finally {
      setLoading(false);
      setSubmitting(false);
    }
  };

  return (
    <div className="relative w-full h-full p-4">
      {/* Background Shapes */}
      <div className="absolute top-0 left-0 w-full h-full z-0">
        <div className="absolute top-1/4 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-transparent transform rotate-45 opacity-30"></div>
        <div className="absolute bottom-1/4 right-0 w-full h-1 bg-gradient-to-l from-green-500 to-transparent transform rotate-45 opacity-30"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 w-full max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Create Your Account</h1>
          <p className="text-gray-500">
            To log in and explore the LMS, please create an account by providing the required details.
          </p>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={onSubmit}
        >
          {({ isSubmitting, values, setFieldValue }) => (
            <Form className="space-y-6">
              {/* User Name */}
              {/* <div className="relative">
                <Field
                  name="user_name"
                  type="text"
                  className="peer w-full px-4 py-3 border-b-2 border-gray-300 bg-transparent focus:outline-none focus:border-blue-500"
                  placeholder=" "
                />
                <label
                  htmlFor="user_name"
                  className="absolute text-sm text-gray-500 top-0 left-4 transform -translate-y-1/2 transition-all peer-placeholder-shown:top-3/4 peer-placeholder-shown:text-gray-400 peer-focus:top-0 peer-focus:text-blue-500"
                >
                  Your Name
                </label>
                <ErrorMessage name="user_name" component="div" className="text-red-500 text-sm mt-1" />
              </div> */}

              <div className="relative w-full mx-auto">
                  <Field
                    id="user_name"
                    name="user_name"
                    type="text"
                    value={values.user_name}
                    className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${values.user_name ? 'outline-none ring-2 ring-blue-500 border-blue-500' : ''}
                      px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`
                    } 
                    
                    placeholder=""
                    onChange={(e) => {
                      setFieldValue('user_name', e.target.value);
                      setLmsTitle(e.target.value);
                    }}
                  />
                  <label
                    htmlFor="user_name"
                    className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                  >
                    Your Name
                  </label>
                  <ErrorMessage name="user_name">
                    {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                  </ErrorMessage>
                  <Icon icon="akar-icons:user" className="w-4 h-4 absolute right-4 top-1/2 transform -translate-y-1/2" />
                </div>



              {/* Username */}


              <div className="relative w-full mx-auto">
                  <Field
                    id="user_username"
                    name="user_username"
                    type="text"
                    value={values.user_username}
                    className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${values.user_username ? 'outline-none ring-2 ring-blue-500 border-blue-500' : ''}
                      px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`
                    } 
                    
                    placeholder=""
                    onChange={(e) => {
                      setFieldValue('user_username', e.target.value);
                      setLmsTitle(e.target.value);
                    }}
                  />
                  <label
                    htmlFor="user_username"
                    className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                  >
                    Username
                  </label>
                  <ErrorMessage name="user_username">
                    {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                  </ErrorMessage>
                  <Icon icon="akar-icons:user" className="w-4 h-4 absolute right-4 top-1/2 transform -translate-y-1/2" />
                </div>


              {/* <div className="relative">
                <Field
                  name="user_username"
                  type="text"
                  className="peer w-full px-4 py-3 border-b-2 border-gray-300 bg-transparent focus:outline-none focus:border-blue-500"
                  placeholder=" "
                />
                <label
                  htmlFor="user_username"
                  className="absolute text-sm text-gray-500 top-0 left-4 transform -translate-y-1/2 transition-all peer-placeholder-shown:top-3/4 peer-placeholder-shown:text-gray-400 peer-focus:top-0 peer-focus:text-blue-500"
                >
                  Username
                </label>
                <ErrorMessage name="user_username" component="div" className="text-red-500 text-sm mt-1" />
              </div> */}

              {/* Email */}

              <div className="relative w-full mx-auto">
                  <Field
                    id="user_email"
                    name="user_email"
                    type="email"
                    value={values.user_email}
                    className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${values.user_email ? 'outline-none ring-2 ring-blue-500 border-blue-500' : ''}
                      px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`
                    } 
                    
                    placeholder=""
                    onChange={(e) => {
                      setFieldValue('user_email', e.target.value);
                      setLmsTitle(e.target.value);
                    }}
                  />
                  <label
                    htmlFor="user_email"
                    className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                  >
                    Email
                  </label>
                  <ErrorMessage name="user_email">
                    {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                  </ErrorMessage>
                  <Icon icon="akar-icons:user" className="w-4 h-4 absolute right-4 top-1/2 transform -translate-y-1/2" />
                </div>


              {/* <div className="relative">
                <Field
                  name="user_email"
                  type="email"
                  className="peer w-full px-4 py-3 border-b-2 border-gray-300 bg-transparent focus:outline-none focus:border-blue-500"
                  placeholder=" "
                />
                <label
                  htmlFor="user_email"
                  className="absolute text-sm text-gray-500 top-0 left-4 transform -translate-y-1/2 transition-all peer-placeholder-shown:top-3/4 peer-placeholder-shown:text-gray-400 peer-focus:top-0 peer-focus:text-blue-500"
                >
                  Email
                </label>
                <ErrorMessage name="user_email" component="div" className="text-red-500 text-sm mt-1" />
              </div> */}

              {/* Password */}
              <div className="relative w-full mx-auto">
                <Field
                  id="user_password"
                  name="user_password"
                      type={showPassword ? 'text' : 'password'}
                  value={values.user_password}
                  className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${values.user_password ? 'outline-none ring-2 ring-blue-500 border-blue-500' : ''}
                      px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`
                  } 
                  placeholder=""
                  onChange={(e) => {
                    setFieldValue('user_password', e.target.value);
                    
                  }}
                />
                <label
                  htmlFor="user_password"
                  className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                >
                  Password
                </label>
                <ErrorMessage name="user_password">
                  {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                </ErrorMessage>
                <button
                  type="button"
                  className="absolute right-4 top-1/2 transform -translate-y-1/2"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  <Icon icon={showPassword ? 'akar-icons:eye-closed' : 'akar-icons:eye'} className="w-4 h-4" />
                </button>
              </div>

              {/* Confirm Password */}
              <div className="relative w-full mx-auto">
                <Field
                  id="password_confirmation"
                  name="password_confirmation"
                  type={showPassword ? 'text' : 'password'}
                  className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${values.password_confirmation ? 'outline-none ring-2 ring-blue-500 border-blue-500' : ''}
                      px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`
                  }
                  placeholder=""
                  onChange={(e) => {
                    setFieldValue('password_confirmation', e.target.value);
                  }}
                />
                <label
                  htmlFor="password_confirmation"
                  className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                >
                  Confirm Password
                </label>
                <ErrorMessage name="password_confirmation">
                  {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                </ErrorMessage>
                <button
                  type="button"
                  className="absolute right-4 top-1/2 transform -translate-y-1/2"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  <Icon icon={showPassword ? 'akar-icons:eye-closed' : 'akar-icons:eye'} className="w-4 h-4" />
                </button>
              </div>

              {/* Buttons */}
              <div className="flex justify-between items-center mt-6">
                <button
                  type="button"
                  className="flex items-center px-4 py-2 text-gray-700 rounded-lg bg-gray-200 hover:bg-gray-300"
                  onClick={() => setPercent(50)}
                >
                  <Icon icon="akar-icons:chevron-left" className="mr-2" />
                  Back
                </button>
                <Button
                  type="submit"
                  isLoading={loading}
                  className="flex items-center px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default Three;
