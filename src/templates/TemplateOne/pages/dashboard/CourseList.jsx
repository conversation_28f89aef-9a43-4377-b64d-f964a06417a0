import React from 'react';
import { Icon } from "@iconify/react";
import useFetch from "@/hooks/useFetch";
import Loading from '@/components/Loading';
import Course from "../home/<USER>";
const CourseList = () => {
  const { data: studentCourseList, isLoading, isError } = useFetch({ queryKey: 'studentCourseList', endPoint: 'student-course-list' });

  if (isLoading) return <Loading/>;
  if (isError) return <div>Error fetching data</div>;

  return (


    <div className="">
      <h1 className="text-3xl font-bold mb-4">Your Enrolled Courses</h1>

         <div className='grid grid-cols-1 md:grid-cols-3 gap-4 text-sm'>
            {studentCourseList.data.map((course, index) => (
              <Course key={index} course={course} index={index} purchased={true} />
            ))}
            </div>

    </div>


  );
}

export default CourseList;