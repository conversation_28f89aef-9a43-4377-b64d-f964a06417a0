import React from "react";
import { Icon } from "@iconify/react";
import useFetch from "@/hooks/useFetch";
import moment from "moment";
import { BASE_URL } from "@/config";
import { Link } from "react-router-dom";

const PurchaseList = () => {
  const {
    data: purchaseList,
    isLoading,
    isError,
  } = useFetch({
    queryKey: "purchaseList",
    endPoint: `${BASE_URL}/student-purchase-list`,
  });
  console.log(purchaseList);

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error fetching data</div>;

  return (
    <div className="overflow-x-auto">
      <div className="border-l-4 border-primary-500 rounded ps-2 my-5">
        <h6 className="mt-2">My Purchase Item List</h6>
      </div>
      <table className="min-w-full bg-white border">
        <thead>
          <tr>
            <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">
              SL
            </th>
            <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">
              Course List
            </th>
            {/* <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">
              Date of Participation
            </th> */}
            <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">
              Type
            </th>
            <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">
              Amount
            </th>
            <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">
              Paid
            </th>
            {/* <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">
              Category
            </th>
            <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">
              Price
            </th> */}
          </tr>
        </thead>
        <tbody>
          {purchaseList.data.map((purchase, index) => (
            <tr key={purchase.id}>
              <td className="py-4 px-4 border-b border-gray-200">
                {index + 1}
              </td>
              <td className="py-4 px-4 border-b border-gray-200">
                
              <span className="hover:underline hover:text-blue-600">
                <Link to={'/course-details/' + purchase.id}>{purchase.title}</Link>
              </span>
              </td>
              {/* <td className="py-4 px-4 border-b border-gray-200">
                {moment(purchase.created_at).format("MMMM Do YYYY")}
              </td> */}
              <td className="py-4 px-4 border-b border-gray-200">
                {purchase.category_name}
              </td>
              <td className="py-4 px-4 border-b border-gray-200">
                {purchase.regular_price}
              </td>
              {/* <td className="py-4 px-4 border-b border-gray-200">
                <button className="text-blue-500">Details</button>
              </td> */}
              <td className="py-4 px-4 border-b border-gray-200">
                {/* {purchase.sale_price} */}
                { purchase.sale_price - (purchase.discount_percentage ? (purchase.discount_percentage * purchase.sale_price ) / 100 : 0 ) }
              </td>
              {/* <td className="py-4 px-4 border-b border-gray-200">
                {"₹" + purchase.sale_price}
              </td> */}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default PurchaseList;
