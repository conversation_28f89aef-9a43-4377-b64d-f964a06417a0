import React from 'react';
import { ASSET_URL } from "@/config";
import { Icon } from "@iconify/react";

const TrueFalseQuestion = ({ question, onChange, answer }) => {
    if (!question) {
        return null;
    }

    const handleChange = (value) => {
        onChange(question.id, value);
    };

    return (
        <div className="p-4 mx-3 rounded-xl border border-gray-200 bg-white shadow-md hover:shadow-lg transition-shadow duration-300 mb-4">
            <div className="mb-3 flex items-start">
                <div className="bg-green-100 p-1.5 rounded-full mr-2 mt-0.5">
                    <Icon icon="mdi:check-circle-outline" className="h-4 w-4 text-green-600" />
                </div>
                <div>
                    <div className="inline-block px-2 py-0.5 bg-green-50 text-green-700 rounded-full text-xs font-medium mb-1">
                        True/False Question
                    </div>
                    <h3 className="text-base font-semibold text-gray-800">{question?.question_text}</h3>
                </div>
            </div>

            {/* Display question image if available */}
            {question.question_image && (
                <div className="mb-3 rounded-lg overflow-hidden border border-gray-200 shadow-sm">
                    <img
                        src={ASSET_URL + question.question_image}
                        alt="Question"
                        className="w-full h-auto object-contain"
                    />
                </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 mt-3">
                <label
                    className={`flex items-center p-2.5 border-2 rounded-lg cursor-pointer transition-all duration-200 w-full sm:w-auto ${
                        answer === true
                            ? 'border-green-500 bg-green-50 shadow-md'
                            : 'border-gray-200 hover:border-green-300 hover:bg-gray-50'
                    }`}
                >
                    <div className={`flex-shrink-0 w-4 h-4 rounded-full ${
                        answer === true ? 'bg-green-500' : 'border-2 border-gray-300'
                    } mr-2 flex items-center justify-center transition-colors`}>
                        {answer === true && <Icon icon="mdi:check" className="text-white text-xs" />}
                    </div>
                    <span className="text-gray-800 font-medium text-sm">True</span>
                    <input
                        type="radio"
                        name={`true_false_${question?.id}`}
                        value="true"
                        checked={answer === true}
                        onChange={() => handleChange(true)}
                        className="sr-only"
                    />
                </label>

                <label
                    className={`flex items-center p-2.5 border-2 rounded-lg cursor-pointer transition-all duration-200 w-full sm:w-auto ${
                        answer === false
                            ? 'border-red-500 bg-red-50 shadow-md'
                            : 'border-gray-200 hover:border-red-300 hover:bg-gray-50'
                    }`}
                >
                    <div className={`flex-shrink-0 w-4 h-4 rounded-full ${
                        answer === false ? 'bg-red-500' : 'border-2 border-gray-300'
                    } mr-2 flex items-center justify-center transition-colors`}>
                        {answer === false && <Icon icon="mdi:close" className="text-white text-xs" />}
                    </div>
                    <span className="text-gray-800 font-medium text-sm">False</span>
                    <input
                        type="radio"
                        name={`true_false_${question?.id}`}
                        value="false"
                        checked={answer === false}
                        onChange={() => handleChange(false)}
                        className="sr-only"
                    />
                </label>
            </div>

            <div className="mt-2 text-xs text-gray-500 flex items-center">
                <Icon icon="mdi:information-outline" className="mr-1.5 text-gray-400" />
                <p>Select either True or False for this statement.</p>
            </div>
        </div>
    );
};

export default TrueFalseQuestion;
