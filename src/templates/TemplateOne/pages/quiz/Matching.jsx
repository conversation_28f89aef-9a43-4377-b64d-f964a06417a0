import React from 'react';
import { useDrag, useDrop, DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Icon } from '@iconify/react';
import './matching.css';

// Component for draggable answer items in the answer pool
const AnswerItem = ({ item }) => {
    const [{ isDragging }, drag] = useDrag(() => ({
        type: 'answer',
        item: { text: item },
        collect: monitor => ({ isDragging: !!monitor.isDragging() })
    }));

    return (
        <div
            ref={drag}
            className={`p-2.5 mb-2 border-2 rounded-lg shadow-sm draggable-item ${
                isDragging ? 'dragging opacity-50' : ''
            } bg-white cursor-grab hover:border-purple-300 hover:bg-purple-50 transition-all duration-200`}
        >
            <div className="flex items-center">
                <Icon icon="mdi:drag" className="mr-1.5 text-gray-400 text-sm" />
                <span className="font-medium text-sm">{item}</span>
            </div>
        </div>
    );
};

// Component for answers that have been matched to a question
const MatchedAnswer = ({ text }) => {
    return (
        <div className="matched-item bg-purple-50 border border-purple-200 rounded-lg p-2 w-full">
            <div className="flex items-center">
                <Icon icon="mdi:check-circle" className="mr-1.5 text-purple-500 text-sm" />
                <span className="font-medium text-gray-800 text-sm">{text}</span>
            </div>
        </div>
    );
};

// Component for question drop targets
const QuestionDropTarget = ({ question, index, onDrop, answer }) => {
    const [{ isOver }, drop] = useDrop(() => ({
        accept: ['answer'], // Only accept new answers from the pool, not matched answers
        drop: item => onDrop(index, item.text),
        collect: monitor => ({ isOver: !!monitor.isOver() }),
        // Only allow dropping if there's no answer already
        canDrop: () => !answer
    }));

    return (
        <div className="flex flex-col mb-3">
            <div className="flex items-center mb-1.5 bg-gray-50 p-2 rounded-lg border border-gray-200">
                <div className="flex-shrink-0 w-5 h-5 rounded-full bg-purple-100 flex items-center justify-center mr-2">
                    <span className="font-bold text-purple-700 text-xs">{index + 1}</span>
                </div>
                <span className="font-medium text-gray-800 text-sm">{question}</span>
            </div>
            <div
                ref={drop}
                className={`p-2 border-2 rounded-lg flex items-center justify-center drop-target min-h-[50px] transition-all duration-300 ${
                    isOver ? 'active border-purple-500 bg-purple-50' : answer ? 'border-purple-300' : 'border-dashed border-gray-300 bg-gray-50'
                } ${answer ? 'has-item' : 'hover:border-purple-300 hover:bg-purple-50/50'}`}
            >
                {answer ? (
                    <MatchedAnswer text={answer} />
                ) : (
                    <div className="flex flex-col items-center text-gray-500">
                        <Icon icon="mdi:tray-arrow-down" className="h-4 w-4 mb-0.5 text-gray-400" />
                        <span className="text-xs">Drop answer here</span>
                    </div>
                )}
            </div>
        </div>
    );
};

const Matching = ({ question, onChange, matches = {} }) => {
    if (!question) return null;

    // Get the left items (questions) from the matching_answers array
    const leftItems = question.matching_answers?.map(item => item.left_item) || [];

    // Get the right items (answers) from the random_right_items array
    const rightItems = question.random_right_items || [];

    // Check if any answers have been matched
    const hasAnswers = Object.keys(matches).length > 0;

    // Calculate available answers directly without using state
    // This prevents the infinite update loop
    const availableAnswers = React.useMemo(() => {
        // Start with all answers
        const allAnswers = [...rightItems];

        // Filter out answers that are already matched to questions
        const usedAnswers = Object.values(matches);
        return allAnswers.filter(answer => !usedAnswers.includes(answer));
    }, [matches, rightItems]);

    // Handle dropping an answer onto a question
    const handleDrop = (questionIndex, answerText) => {
        // Set the answer for this question
        onChange(question.id, questionIndex, answerText);
    };

    // Reset all answers
    const handleReset = () => {
        // For each question that has an answer, clear it
        Object.keys(matches).forEach(questionIndex => {
            onChange(question.id, parseInt(questionIndex), undefined);
        });
    };

    // Calculate progress percentage
    const progressPercentage = leftItems.length > 0
        ? Math.round((Object.keys(matches).length / leftItems.length) * 100)
        : 0;

    return (
        <div className="p-4 mx-3 rounded-xl border border-gray-200 bg-white shadow-md hover:shadow-lg transition-shadow duration-300 mb-4">
            <div className="mb-3 flex items-start">
                <div className="bg-purple-100 p-1.5 rounded-full mr-2 mt-0.5">
                    <Icon icon="mdi:connection" className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                    <div className="inline-block px-2 py-0.5 bg-purple-50 text-purple-700 rounded-full text-xs font-medium mb-1">
                        Matching Question
                    </div>
                    <h3 className="text-base font-semibold text-gray-800">{question?.question_text}</h3>
                </div>
            </div>

            {/* Progress bar */}
            {hasAnswers && (
                <div className="mb-3">
                    <div className="flex justify-between items-center mb-1">
                        <span className="text-xs font-medium text-gray-700">Your progress</span>
                        <span className="text-xs font-medium text-purple-600">{progressPercentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div
                            className="bg-purple-600 h-1.5 rounded-full transition-all duration-500"
                            style={{ width: `${progressPercentage}%` }}
                        ></div>
                    </div>
                </div>
            )}

            <div className="flex items-center mb-3 bg-gray-50 p-2.5 rounded-lg border border-gray-200">
                <Icon icon="mdi:information-outline" className="h-4 w-4 text-gray-500 mr-2 flex-shrink-0" />
                <p className="text-gray-700 text-xs">
                    <span className="font-medium">Instructions:</span> Drag answers from the right side and drop
                    them under the matching questions. Use the Reset button to clear all answers and start over.
                </p>
            </div>

            {/* Reset button - always show but disable if no answers */}
            <div className="mb-3">
                <button
                    onClick={handleReset}
                    disabled={!hasAnswers}
                    className={`px-3 py-1.5 rounded-lg flex items-center text-sm ${
                        hasAnswers
                            ? 'bg-red-500 text-white hover:bg-red-600'
                            : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    } transition-colors`}
                >
                    <Icon icon="mdi:refresh" className="mr-1.5 h-4 w-4" />
                    Reset All Answers
                </button>
            </div>

            <DndProvider backend={HTML5Backend}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-1">
                        <h6 className="font-bold mb-2 flex items-center text-gray-800 text-sm">
                            <Icon icon="mdi:help-circle-outline" className="mr-1.5 text-purple-600" />
                            Questions
                        </h6>
                        {leftItems.map((item, index) => (
                            <QuestionDropTarget
                                key={index}
                                question={item}
                                index={index}
                                onDrop={handleDrop}
                                answer={matches[index]}
                            />
                        ))}
                    </div>
                    <div className="space-y-1">
                        <h6 className="font-bold mb-2 flex items-center text-gray-800 text-sm">
                            <Icon icon="mdi:format-list-bulleted" className="mr-1.5 text-purple-600" />
                            Answer Options
                        </h6>

                        {/* Only show available answers in the pool */}
                        {availableAnswers.map(item => (
                            <AnswerItem
                                key={item} // Using the item itself as key for stability
                                item={item}
                            />
                        ))}

                        {/* Show message when all answers are used */}
                        {availableAnswers.length === 0 && (
                            <div className="p-3 border border-amber-200 rounded-lg bg-amber-50 text-center shadow-sm">
                                <div className="flex flex-col items-center">
                                    <Icon
                                        icon="mdi:check-circle"
                                        className="h-6 w-6 text-amber-500 mb-1"
                                    />
                                    <p className="font-medium text-amber-800 text-sm">All answers have been used</p>
                                    <p className="text-amber-700 mt-0.5 text-xs">
                                        {Object.keys(matches).length === leftItems.length
                                            ? "You've completed this matching question!"
                                            : "Continue matching or use the Reset button to start over"}
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </DndProvider>
        </div>
    );
};

export default Matching;
