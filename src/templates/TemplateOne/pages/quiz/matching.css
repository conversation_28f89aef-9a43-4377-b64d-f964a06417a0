.draggable-item {
  cursor: grab;
  transition: all 0.3s ease;
  user-select: none;
  position: relative;
  z-index: 1;
}

.draggable-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
  z-index: 2;
}

.draggable-item:active {
  cursor: grabbing;
}

.draggable-item.dragging {
  opacity: 0.6;
  transform: scale(1.05);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  z-index: 10;
}

.drop-target {
  transition: all 0.3s ease;
  min-height: 70px;
  position: relative;
  overflow: hidden;
}

.drop-target:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom right, rgba(147, 51, 234, 0.05), rgba(147, 51, 234, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.drop-target:hover:before {
  opacity: 1;
}

.drop-target.active {
  background-color: rgba(147, 51, 234, 0.1);
  border-color: #9333ea;
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(147, 51, 234, 0.15);
}

.drop-target.has-item {
  background-color: #faf5ff;
  transition: all 0.3s ease;
}

.drop-target.has-item:hover {
  box-shadow: 0 2px 6px rgba(147, 51, 234, 0.1);
}

.matched-item {
  background-color: white;
  border-radius: 0.5rem;
  padding: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  transition: all 0.3s ease;
  border: 1px solid rgba(147, 51, 234, 0.2);
}

.matched-item:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}
