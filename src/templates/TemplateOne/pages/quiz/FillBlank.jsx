import React from 'react';
import { ASSET_URL } from "@/config";
import { Icon } from "@iconify/react";

const FillBlank = ({ question, onChange, answers = {} }) => {
    if (!question) {
        return null;
    }

    // Split the question text by blanks (represented by _______)
    const parts = question?.question_text?.split(/_+/g) || [];

    // Count the number of blanks (one less than the number of parts)
    const blankCount = parts.length - 1;

    const handleAnswerChange = (index, value) => {
        onChange(question.id, index, value);
    };

    // Check if any blanks have been filled
    const hasFilledBlanks = Object.values(answers).some(answer => answer && answer.trim() !== '');

    return (
        <div className="p-4 mx-3 rounded-xl border border-gray-200 bg-white shadow-md hover:shadow-lg transition-shadow duration-300 mb-4">
            <div className="mb-3 flex items-start">
                <div className="bg-indigo-100 p-1.5 rounded-full mr-2 mt-0.5">
                    <Icon icon="mdi:format-text" className="h-4 w-4 text-indigo-600" />
                </div>
                <div>
                    <div className="inline-block px-2 py-0.5 bg-indigo-50 text-indigo-700 rounded-full text-xs font-medium mb-1">
                        Fill in the Blanks
                    </div>
                    <h3 className="text-base font-semibold text-gray-800">Complete the sentence</h3>
                </div>
            </div>

            {/* Display question image if available */}
            {question.question_image && (
                <div className="mb-3 rounded-lg overflow-hidden border border-gray-200 shadow-sm">
                    <img
                        src={ASSET_URL + question.question_image}
                        alt="Question"
                        className="w-full h-auto object-contain"
                    />
                </div>
            )}

            <div className="mt-3 p-3 bg-gray-50 rounded-lg border border-gray-200 text-gray-800 leading-relaxed text-base">
                <div className="space-y-4">
                    {/* Render the parts and blanks with line breaks at appropriate points */}
                    {parts.map((part, index) => {
                        // Determine if this part should start a new line
                        const isNewLine = part.includes("\n") || part.trim().endsWith(".") || part.trim().endsWith("?") || part.trim().endsWith("!");

                        return (
                            <React.Fragment key={index}>
                                <span className={isNewLine ? "block mb-1" : ""}>{part}</span>
                                {index < parts.length - 1 && (
                                    <span className="relative inline-block mx-1">
                                        <input
                                            type="text"
                                            className={`border-b-2 px-2 py-1 min-w-[120px] focus:outline-none transition-all duration-200 rounded-t-md ${
                                                answers[index]
                                                    ? 'border-indigo-500 bg-indigo-50'
                                                    : 'border-gray-400 bg-white hover:border-indigo-300'
                                            }`}
                                            placeholder="Type answer here"
                                            value={answers[index] || ''}
                                            onChange={(e) => handleAnswerChange(index, e.target.value)}
                                            aria-label={`Blank ${index + 1} of ${blankCount}`}
                                        />
                                        <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gray-200"></div>
                                    </span>
                                )}
                            </React.Fragment>
                        );
                    })}
                </div>
            </div>

            {/* Progress indicator */}
            {hasFilledBlanks && (
                <div className="mt-3 bg-indigo-50 border border-indigo-100 rounded-lg p-2">
                    <div className="flex items-center">
                        <Icon icon="mdi:progress-check" className="h-4 w-4 text-indigo-600 mr-1.5" />
                        <span className="text-indigo-700 font-medium text-xs">
                            {Object.keys(answers).filter(key => answers[key] && answers[key].trim() !== '').length} of {blankCount} blanks filled
                        </span>
                    </div>
                </div>
            )}

            {/* Help text */}
            <div className="mt-2 text-xs text-gray-500 flex items-center">
                <Icon icon="mdi:information-outline" className="mr-1.5 text-gray-400" />
                <p>Fill in all {blankCount} blank(s) with appropriate answers.</p>
            </div>
        </div>
    );
};

export default FillBlank;
