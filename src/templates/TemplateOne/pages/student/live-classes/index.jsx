import React from "react";
import useFetch from "@/hooks/useFetch";
import Loading from "@/components/Loading";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Breadcrumbs from "@/components/ui/Breadcrumbs";
import bgShape from "@/assets/images/svg/shape2.svg";
import bgShape2 from "@/assets/images/svg/shape3.svg";
import { useDispatch } from "react-redux";

import Badge from "@/components/ui/Badge";
import { useSelector } from "react-redux";
import api from "@/server/api";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

const StudentLiveClasses = () => {
  const { showDeleteModal, deleteData, showEditModal, showModal, editData } =
    useSelector((state) => state.assignmentSlice);
  const dispatch = useDispatch();
  //   const [showModal, setShowModal] = useState(false);
  //   const [apiParam, setApiParam] = useState("");
  const navigate = useNavigate();

  const {
    data: liveClasses,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `live-class-list`,
    endPoint: `/class-schedules`,
  });

  //   const res = useGetMentorListQuery(apiParam);
  //   const changePage = (val) => {
  //     setApiParam(val);
  //   };
  const handleJoinClass = async (id) => {
    const { data } = await api.post("/join-class", { class_id: id });
    if (data && data.class_url) {
      window.open(data.class_url, "_blank");
    } else {
      toast.error("Something went wrong!");
    }
  };

  const data = liveClasses?.data;
  const columns = [
    {
      label: "#",
      field: "serial",
    },
    {
      label: "Class Title",
      field: "title",
    },
    {
      label: "Course Name",
      field: "course",
    },
    {
      label: "Date",
      field: "schedule_datetime",
    },
    {
      label: "Start Time",
      field: "start_time",
    },
    {
      label: "Action",
      field: "has_started",
    },
  ];

  const tableData = data?.map((item, index) => {
    return {
      serial: <p className="font-semibold">{index + 1}</p>,
      title: <p>{item.title}</p>,
      course: <p>{item.course}</p>,
      schedule_datetime: <p>{item.schedule_datetime.slice(0, 10)}</p>,
      start_time: <p>{item.start_time}</p>,
      //   status: (
      //     <p className="lowercase">{item.status || "https://meet.google.com"}</p>
      //   ),
      has_started: (
        <span className="space-x-2">
          {item.has_started && !item?.has_completed && (
            <button
              onClick={() => handleJoinClass(item.id)}
              className="px-3 py-1 bg-green-400 rounded font-semibold text-white"
            >
              Join Now
            </button>
          )} 
          {!item?.has_started && (
            <button disabled className="px-3 py-1 bg-orange-400 rounded font-semibold text-white">Upcoming</button>
          )}

          {item?.has_completed && <button disabled className="px-3 py-1 bg-gray-600 rounded font-semibold text-white">Completed</button>}
        </span>
      ),
    };
  });

  //   const actions = [
  //     {
  //       name: "edit",
  //       icon: "heroicons:pencil-square",
  //       onClick: (val) => {
  //         console.log(val);
  //         // dispatch(setEditData(data[val]));
  //         // dispatch(setShowEditModal(true));
  //       },
  //     },
  //     {
  //       name: "delete",
  //       icon: "heroicons-outline:trash",
  //       onClick: (val) => {
  //         console.log(val);
  //         // dispatch(setDeleteData(data[val]));
  //         // dispatch(setDeleteModalShow(true));
  //       },
  //     },
  //   ];

  const handleSubmit = () => {
    // setShowModal(false);
  };

  //   const createPage = <AddLiveClass />;
  //   const editPage = <EditLiveClass />;

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div className="container mt-5">
      {/* <img className="absolute left-0 z-0" src={bgShape} alt="" />
      <img className="absolute right-0 z-0" src={bgShape2} alt="" /> */}
      <Breadcrumbs />
      <div className="relative text-center">
        <BasicTablePage
          title="Live Class List"
          createButton=""
          //   editPage={editPage}
          //   actions={actions}
          columns={columns}
          data={tableData}
          openCreateModal={() => ""}
          // changePage={changePage}
          currentPage={data?.current_page}
          submitForm={handleSubmit}
          totalPages={Math.ceil(data?.total / data?.per_page)}
          // filter={filter}
          // setFilter={setApiParam}
        />
      </div>
    </div>
  );
};

export default StudentLiveClasses;
