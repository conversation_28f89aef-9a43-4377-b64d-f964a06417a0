import React from "react";
import Icon from "@/components/ui/Icon";
import CourseImage from "@/assets/MentorDashboard/myCOURSE.svg";
import { ASSET_URL } from "@/config";
import { Link } from "react-router-dom";
import { useNavigate } from "react-router-dom";

const MyCourses = ({ courses }) => {
  const navigate = useNavigate();
  // Sample course data to match the design
  const courseData = [
    {
      title: "Course Name will appear here",
      thumbnail: CourseImage,
      progress: 80,
    },
    {
      title: "Course Name will appear here",
      thumbnail: CourseImage,
      progress: 50,
    },
    {
      title: "Course Name will appear here",
      thumbnail: CourseImage,
      progress: 10,
    },
    {
      title: "Course Name will appear here",
      thumbnail: CourseImage,
      progress: 90,
    },
  ];

  return (
    <div className="p-4 rounded-lg shadow-md bg-white">
      {/* Header Section */}
      <div className="flex items-center justify-between mb-4 bg-[#DBE6FF] p-2 rounded-md">
        <h3 className="text-xl font-semibold text-gray-800">My Courses</h3>
        <Link
          to={"/my-courses"}
          className="px-4 py-1 rounded-md font-semibold hover:underline text-blue-600"
        >
          See All
        </Link>
      </div>

      {/* Courses List */}
      <div className="space-y-4">
        {courses?.length > 0 ? (
          courses.map((course, index) => (
            <div
              key={index}
              className="flex items-center bg-white p-4 rounded-lg shadow-sm space-x-4 border border-gray-200"
            >
              {/* Course Image */}
              <img
                src={ASSET_URL + course?.thumbnail}
                alt="Course"
                className="w-24 h-16 object-cover rounded-lg"
              />

              {/* Course Info */}
              <div className="flex xl:flex-row xl:justify-between xl:items-center flex-col items-start w-full">
                <div className="flex-1 ">
                  <h4 className="text-lg font-semibold text-gray-800">
                    {course.title}
                  </h4>

                  {/* Progress Bar */}
                  <div className="flex lg:flex-row lg:items-center flex-col items-start gap-4 mt-2">
                    <p className="text-sm text-gray-600">
                      {course.progress.toFixed(2)}% Progress
                    </p>
                    <div className="w-40 bg-gray-200 h-3 rounded-full">
                      <div
                        className="bg-green-600 h-3 rounded-full"
                        style={{ width: `${course.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Routine Link */}
                  <div className="flex items-center mt-2">
                    <Icon
                      icon="mdi:calendar-outline"
                      className="w-5 h-5 text-blue-600 mr-1"
                    />
                    <a href="#" className="text-blue-600 text-sm font-medium">
                      See Routine
                    </a>
                  </div>
                </div>

                {/* Start Now Button */}
                <Link to={course.slug ? `/course/${course?.slug}` : `/course-details/${course?.id}`}  className="hover:bg-blue-600 bg-blue-500 text-white px-4 py-2 mt-3 xl:mt-0 rounded-md flex h-10 items-center">
                  Start Now
                  <Icon icon="mdi:arrow-right" className="ml-2 w-4 h-4" />
                </Link>
              </div>
            </div>
          ))
        ) : (
          <div className="text-gray-600">
            No courses available at the moment.
          </div>
        )}
      </div>

      {/* See All Button */}
      {/* <div className="flex justify-center mt-6">
        <button className="bg-blue-100 text-blue-600 px-6 py-2 rounded-md shadow-sm hover:bg-blue-200">
          See All
        </button>
      </div> */}
    </div>
  );
};

export default MyCourses;
