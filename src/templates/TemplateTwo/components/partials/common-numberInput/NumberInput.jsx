import React, { forwardRef } from "react";
import { useField } from "formik";

const NumberInput = forwardRef(({ label, required, type, ...props }, ref) => {
   const [field, meta] = useField(props);
   const isError = meta.touched && meta.error;

   const handleKeyDown = (e) => {
      const key = e.key;
      if (!/^[0-9]*\.?[0-9]*$/.test(key) && key !== 'Backspace' && key !== 'Delete' && key !== 'ArrowLeft' && key !== 'ArrowRight' && key !== 'Tab') {
         e.preventDefault();
      }
   };

   const handlePaste = (e) => {
      const paste = e.clipboardData.getData('text');
      if (!/^\d+$/.test(paste)) {
         e.preventDefault();
      }
   };

   return (
      <div>
         <label
            htmlFor={props.id || props.name}
            className="block  mb-2"
         >
            {label} {required && <span className="text-red-500">*</span>}
         </label>
         <input
            {...field}
            {...props}
            type={type}
            className={`appearance-none border rounded h-10 w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:-outline ${
               isError ? "border-red-500" : ""
            }`}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            ref={ref}
         />
         {isError && <span className="text-red-500 text-xs">{meta.error}</span>}
      </div>
   );
});

export default NumberInput;
