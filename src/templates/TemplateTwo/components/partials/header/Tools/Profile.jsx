import React from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import Button from "@/components/ui/Button";
import Dropdown from "@/components/ui/Dropdown";
import Icon from "@/components/ui/Icon";
import { Menu } from "@headlessui/react";
import { useDispatch } from "react-redux";
import { handleLogout } from "@/pages/auth/common/store";

const profileLabel = () => {
  let user = JSON.parse(localStorage.getItem("name"));

  return (
    <div className="flex items-center">
      <div className="flex-1 ltr:mr-[10px] rtl:ml-[10px]">
        <div className="lg:h-8 lg:w-8 h-7 w-7 rounded-full">
          <Icon
            icon="bi:person-circle"
            className="block w-7 h-7 object-cover rounded-full text-white "
          />
        </div>
      </div>
      <div className="flex-none text-black-800 dark:text-white text-sm font-medium items-center lg:flex hidden overflow-hidden text-ellipsis whitespace-nowrap">
        <span className="overflow-hidden text-ellipsis whitespace-nowrap block">
          {user}
        </span>
        <span className="text-base inline-block ltr:ml-[10px] rtl:mr-[10px] ml-4">
          <Icon icon="heroicons-outline:chevron-down"></Icon>
        </span>
      </div>
    </div>
  );
};

const Profile = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isAuth } = useSelector((state) => state.auth);

  const ProfileMenu = [
    {
      label: "Profile",
      icon: "heroicons-outline:user",
      action: () => {
        navigate("/dashboard");
      },
    },
    {
      label: "Logout",
      icon: "heroicons-outline:login",
      action: () => {
        dispatch(handleLogout(false));
        window.location.reload();
      },
    },
  ];

  if (!isAuth) {
    return (
      <Button
        onClick={() => navigate("/login", { state: { from: window.location.pathname } })}
        className="bg-primary-500 text-white px-4 py-2 rounded"
      >
        Login
      </Button>
    );
  }

  return (
    <Dropdown label={profileLabel()} classMenuItems="w-[180px] top-[48px]">
      {ProfileMenu.map((item, index) => (
        <Menu.Item key={index}>
          {({ active }) => (
            <div
              onClick={() => item.action()}
              className={`${
                active
                  ? "bg-slate-100 text-slate-900 dark:bg-slate-600 dark:text-slate-300 dark:bg-opacity-50"
                  : "text-slate-600 dark:text-slate-300"
              } block ${
                item.hasDivider
                  ? "border-t border-slate-100 dark:border-slate-700"
                  : ""
              }`}
            >
              <div className={`block cursor-pointer px-4 py-2`}>
                <div className="flex items-center">
                  <span className="block text-xl ltr:mr-3 rtl:ml-3">
                    <Icon icon={item.icon} />
                  </span>
                  <span className="block text-sm">{item.label}</span>
                </div>
              </div>
            </div>
          )}
        </Menu.Item>
      ))}
    </Dropdown>
  );
};

export default Profile;
