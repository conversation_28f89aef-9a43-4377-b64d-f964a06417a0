import React from "react";
import useDarkMode from "@/hooks/theme/useDarkMode";
import { Link } from "react-router-dom";
import useWidth from "@/hooks/theme/useWidth";

import MainLogo from "@/assets/images/logo/logo.svg";
import LogoWhite from "@/assets/images/logo/logo-white.svg";
// import MobileLogo from "@/assets/images/logo/logo-c.svg";
// import MobileLogoWhite from "@/assets/images/logo/logo-c-white.svg";
import MobileLogo from "@/assets/images/logo/lms-logo.png";
import MobileLogoWhite from "@/assets/images/logo/logo-c-white.svg";
const Logo = () => {
    const [isDark] = useDarkMode();
    const { width, breakpoints } = useWidth();

    return (
        <div>
            <Link to="/dashboard">
                {width >= breakpoints.xl ? (
                    <img
                        src={isDark ? MobileLogoWhite : MobileLogo}
                        alt=""
                        className="h-12"
                    />
                ) : (
                    <img
                        src={isDark ? MobileLogoWhite : MobileLogo}
                        alt=""
                        className="h-12"
                    />
                )}
            </Link>
        </div>
    );
};

export default Logo;
