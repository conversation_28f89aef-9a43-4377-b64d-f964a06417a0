import React, { useState, useEffect, useRef } from "react";
import { Icon } from "@iconify/react";
import logo from "../footer/logo.svg";
import eduLogo from "@/assets/images/logo/edu_logo.png";
import usImg from "@/assets/images/all-img/us.png";
import { useNavigate, Link, NavLink } from "react-router-dom";
import Button from "@/components/ui/Button";
import Profile from "./Tools/Profile";
import userImg from "@/assets/images/all-img/user5.jpeg";
import { useSelector, useDispatch } from "react-redux";
import { handleLogout } from "../../../pages/auth/common/store";
import { ASSET_URL } from "@/config";
import api from "@/server/api";
import Dropdown from "../../ui/Dropdown";
import MenuDropdown from "../../ui/MenuDropdown";
import NavBar from "../../ui/NavBar";
import SearchModal from "@/components/partials/header/Tools/SearchModal";
import Loading from "../../Loading";
import ProfileDropdown from "../../ui/ProfileDropdown";
import dashboardImg from "@/assets/images/svg/dashboard.svg";
import myCoursesImg from "@/assets/images/svg/myCourses.svg";
import profileImg from "@/assets/images/svg/profile.svg";
import assignmentImg from "@/assets/images/svg/assignment.svg";
import settingsImg from "@/assets/images/svg/settings.svg";
import supportImg from "@/assets/images/svg/support.svg";
import Payment from "@/assets/images/svg/payment.svg";
import { useTranslation } from 'react-i18next';
import Notification from "./Notification";

const Header = ({ organization, menuItems, isLoading, error, classNameName = "custom-className" }) => {
  const [isCategoriesOpen, setCategoriesOpen] = useState(false);
  const [isLanguageOpen, setLanguageOpen] = useState(false);
  const [isUserOpen, setUserOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [mobileCategoriesOpen, setMobileCategoriesOpen] = useState(false);
  const { isAuth } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { t, i18n } = useTranslation();

  const mobileMenuRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        mobileMenuRef.current &&
        !mobileMenuRef.current.contains(event.target)
      ) {
        setCategoriesOpen(false);
        setLanguageOpen(false);
        setUserOpen(false);
        setActiveDropdown(null); // Close mobile dropdown
        setMobileCategoriesOpen(false); // Close mobile categories
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const logout = async () => {
    const response = await api.post(
      import.meta.env.VITE_BASE_URL + "/api/logout",
      {}
    );
    if (response.status || response.status == 200) {
      navigate("/");
      api.removeTokenHeader();
      dispatch(handleLogout());
    }
  };

  const currentDate = new Date();
  const expiringDate = new Date(organization?.last_payment?.expiry_date);

  if (isLoading) {
    return <Loading />;
  }


  return (
    <>
      <header
        className={` bg-[#13497C] text-white shadow-sm fixed top-0 left-0 w-full z-50 px-4 lg:px-10`}
      >
        <div className="mx-auto flex items-center justify-between h-16 lg:h-20">
          {/* Logo and Search */}
          <div className="flex items-center space-x-4 lg:space-x-8 max-sm:space-x-2">
            <Link to={"/"} className="flex items-center w-full max-sm:max-w-24">
              <img
                src={
                  organization?.logo ? ASSET_URL + organization.logo : eduLogo
                }
                alt="EduPack Logo"
                className="h-10 w-full object-contain"
              />
            </Link>
            <SearchModal />
          </div>

          {/* Navigation Links - hidden on mobile */}
          <nav className="hidden lg:flex space-x-4 xl:space-x-7 text-sm md:text-base">
            <Link to="/" className="py-2 text-sky-600 hover:text-sky-500">
              Home
            </Link>
            <NavBar menuItems={
              menuItems?.data?.filter((item) => item.is_footer == false)
            } />
          </nav>

          {/* User Actions */}
          <div className="flex items-center space-x-4 max-sm:space-x-1">


            {/* <Button
              size="sm"
              variant="outline"
              className="btn btn-sm btn-dark items-center gap-2"
              onClick={() => {
                i18n.changeLanguage(i18n.language == "bn" ? "en" : "bn");
              }}
            >
              <Icon icon="ic:baseline-language" />
              {i18n.language == "bn" ? "en" : "bn"}
            </Button> */}


            <a
              href="tel:12345"
              className="text-green-400 hidden xl:flex items-center gap-2"
            >
              <Icon icon="ic:baseline-call" /> {organization?.contact_no || 12345}
            </a>
            <Notification />

            <div className="relative">
              {isAuth ? (
                <>
                  <div className="flex items-center space-x-2">
                    {/* <img
                      src={isAuth?.image ? ASSET_URL + isAuth.image : userImg}
                      alt="User"
                      className="h-10 rounded-full"
                    /> */}
                    {isAuth.user_type == "Student" ? (
                      <ProfileDropdown
                        label={isAuth.name.split(" ")[0]}
                        userImage={
                          isAuth?.image ? ASSET_URL + isAuth.image : userImg
                        }
                        onLogout={() => logout()}
                        classItem="hover:bg-sky-100 px-3 py-2 text-sm md:text-base"
                        classMenuItems="mt-2 w-[180px] mr-0"
                        items={[
                          {
                            name: "Profile",
                            link: "/profile",
                            icon: profileImg,
                          },

                          {
                            name: "Dashboard",
                            link: "/dashboard",
                            icon: dashboardImg,
                          },
                          {
                            name: "My Courses",
                            link: "/my-courses",
                            icon: myCoursesImg,
                          },
                          {
                            name: "Assignments",
                            link: "/my-assignments",
                            icon: assignmentImg,
                          },
                          {
                            name: "Exam Result",
                            link: "/exam-result",
                            icon: supportImg,
                          },
                          {
                            name: "Live Class List",
                            link: "student/live-class-list",
                            icon: myCoursesImg,
                          },
                          {
                            name: "My Payments",
                            link: "/my-payments",
                            icon: Payment,
                          },
                          {
                            name: "Certificates",
                            link: "/my-certificates",
                            icon: "",
                          },
                        ]}
                      />
                    ) : (
                      <ProfileDropdown
                        label={isAuth.name.split(" ")[0]}
                        userImage={
                          isAuth?.image ? ASSET_URL + isAuth.image : userImg
                        }
                        onLogout={() => logout()}
                        classItem="hover:bg-sky-100 px-3 py-2 text-sm md:text-base"
                        classMenuItems="mt-2 w-[180px] mr-0"
                        items={[
                          {
                            name: "Profile",
                            link: "/profile",
                            icon: profileImg,
                          },
                          {
                            name: "Dashboard",
                            link: "/mentor-dashboard",
                            icon: dashboardImg,
                          },
                          {
                            name: "Assignments",
                            link: "/assignment-list",
                            icon: assignmentImg,
                          },
                          {
                            name: "Attendance",
                            link: "/attendance-list",
                            icon: profileImg,
                          },
                          {
                            name: "Live Class",
                            link: "/live-class-list",
                            icon: myCoursesImg,
                          },
                        ]}
                      />
                    )}
                  </div>
                </>
              ) : (
                <Button
                  onClick={() =>
                    navigate("/login", {
                      state: { from: window.location.pathname },
                    })
                  }
                  className="py-2 px-4 max-sm:px-2.5 max-sm:py-2 rounded-lg bg-sky-600 text-white"
                >
                  {t('layout.login')}
                </Button>
              )}
            </div>
            <div
              className="fixed inset-0 w-full h-full z-10"
              onClick={() => setUserOpen(false)}
              style={{ visibility: isUserOpen ? "visible" : "hidden" }}
            ></div>
          </div>

          {/* Mobile Menu */}
          <div ref={mobileMenuRef} className="lg:hidden">
            <button
              onClick={() =>
                setActiveDropdown(activeDropdown === "mobile" ? null : "mobile")
              }
              className="text-gray-700 cursor-pointer p-1"
            >
              {" "}
              <Icon
                icon={
                  activeDropdown === "mobile"
                    ? "charm:cross"
                    : "icon-park-outline:hamburger-button"
                }
                className="text-2xl transition-all text-white duration-300"
              />
            </button>
            {activeDropdown === "mobile" && (
              <div className="absolute right-0 my-4 w-full bg-white text-gray-600 border rounded-md shadow-lg py-2 pb-4 z-10">
                <Link to="/" className="block px-4 py-2 hover:bg-sky-100">
                  Home
                </Link>

                <div className="flex flex-col px-4">
                  {menuItems?.data?.map((item, index) =>
                    item.sub_categories.length > 0 ? (
                      <MenuDropdown
                        key={index}
                        label={item.name}
                        labelClass="py-2"
                        classItem="hover:bg-sky-100 px-3 py-2 text-sm md:text-base"
                        items={item}
                      />
                    ) : (
                      <Link
                        to={item.link}
                        className="text-sky-600 hover:text-sky-500"
                      >
                        {item.name}
                      </Link>
                    )
                  )}

                </div>
              </div>
            )}
          </div>
        </div>
      </header>
      <div className="h-16 lg:h-20"></div>
    </>
  );
};

export default Header;
