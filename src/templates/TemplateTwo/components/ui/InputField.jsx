import React, { forwardRef } from "react";
import { useField } from "formik";

const InputField = forwardRef(
  ({ label, required, type, error = "", ...props }, ref) => {
    const [field, meta] = useField(props);
    const isError = meta.touched && meta.error;

    return (
      <div>
        {/* Label styling */}
        {label && (
          <label
            htmlFor={props.id || props.name}
            className={`block text-gray-600 text-sm font-medium mb-2`}
          >
            {label} {required && <span className="text-red-500">*</span>}
          </label>
        )}

        {/* Input field with enhanced styles */}
        <input
          {...field}
          {...props}
          type={type}
          ref={ref}
          className={`appearance-none w-full border rounded-lg py-2 px-4 leading-tight focus:outline-none focus:ring-2 transition-all duration-300 ease-in-out shadow-sm focus:border-blue-500 ${
            isError ? "border-red-500" : "border-gray-300 focus:ring-blue-300"
          }`}
        />

        {/* Error handling */}
        {error && <span className="text-red-500 text-xs">{error}</span>}
        {isError && (
          <span className="text-red-500 text-sm mt-2 block">{meta.error}</span>
        )}
      </div>
    );
  }
);

export default InputField;
