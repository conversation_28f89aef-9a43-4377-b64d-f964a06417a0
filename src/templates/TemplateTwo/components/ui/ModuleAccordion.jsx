import { useState } from "react";
import Icon from "@/components/ui/Icon";
import moduleIcon from "@/assets/images/svg/module icon.svg";
import { Link } from "react-router-dom";
import QuizModalContent from "../../pages/course/QuizModalContent";

const ModuleAccordion = ({ module, isOpen = false, selectedItem, course }) => {
  const [open, setOpen] = useState(isOpen);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [quizData, setQuizData] = useState({});

  const openQuiz = (content) => {
    setQuizData(content);
    setShowLoginModal(true);
  };

const renderContentItem = (content, idx) => {
  const isLocked = content?.is_locked || (idx > 0 && !module.contents[idx - 1]?.is_completed);
  const isCompleted = content?.is_completed;
  const isSelected = content?.id === selectedItem;

  // ⛔️ Not accessible if not enrolled and not free
  const notEnrolledAndNotFree = !course?.is_enrolled && !content?.is_free;
  const canAccess = !notEnrolledAndNotFree;

  const isFullyLocked = isLocked || notEnrolledAndNotFree;

  const contentClass = `text-sm text-slate-700 font-normal rounded-b-md px-4 py-4 flex items-start justify-between gap-4 ${
    isSelected ? "bg-white" : "bg-slate-50 mb-1"
  } ${isFullyLocked ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}`;

  const iconProps = {
    video: { icon: "material-symbols:hangout-video-rounded", color: "text-red-500" },
    quiz: { icon: "fluent:document-text-32-filled", color: "text-green-500" },
    script: { icon: "fluent:document-text-32-filled", color: "text-indigo-600" },
  };

  const inner = (
    <div className="flex gap-4 items-start justify-between w-full">
      <div className="flex gap-4 items-start">
        <Icon icon={iconProps[content.type].icon} className={`text-xl ${iconProps[content.type].color}`} />
        <div>
          <p className="text-md mb-2">{content.title}</p>
        </div>
      </div>
      <div className="flex items-center gap-2">
        {content.is_free && (
          <span className="inline-block text-xs text-white bg-green-500 px-2 py-0.5 rounded">
            Free
          </span>
        )}
        {isCompleted && <Icon icon="lineicons:checkmark-circle" className="text-xl text-green-600" />}
        {isFullyLocked && !isCompleted && (
          <Icon icon="lineicons:locked-2" className="text-xl text-gray-600" />
        )}
      </div>
    </div>
  );

  if (content.type === "quiz") {
    return (
      <div
        key={content.id}
        className={contentClass}
        onClick={() => canAccess && !isLocked && openQuiz(content)}
      >
        {inner}
      </div>
    );
  }

  return (
    <Link
      key={content.id}
      to={canAccess ? `/content-details/${content.id}` : "#"}
      onClick={(e) => {
        if (!canAccess) e.preventDefault();
      }}
      className={contentClass}
    >
      {inner}
    </Link>
  );
};


  const renderHeaderInfo = () => {
    const info = [];

    if (module?.scripts_number > 0) {
      info.push(
        <span key="scripts" className="flex gap-2 items-center pr-4 text-sm">
          <Icon icon="fluent:document-text-32-filled" className="text-xl text-indigo-600" />
          {module?.scripts_number} Scripts
        </span>
      );
    }

    if (module?.videos_number > 0) {
      info.push(
        <span key="videos" className="flex gap-2 items-center pr-4 text-sm">
          <Icon icon="material-symbols:hangout-video-rounded" className="text-xl text-red-500" />
          {module?.videos_number} Videos
        </span>
      );
    }

    if (module?.quizs_number > 0) {
      info.push(
        <span key="quizzes" className="flex gap-2 items-center pr-4 text-sm">
          <Icon icon="fluent:document-text-32-filled" className="text-xl text-green-500" />
          {module?.quizs_number} Quizzes
        </span>
      );
    }

    return info.length > 0 ? (
      <div className="px-4 max-sm:px-2 lg:pl-16 py-2 pt-0 flex items-center gap-4 max-sm:gap-2">
        {info.map((el, idx) => (
          <div key={idx} className="flex items-center">
            {el}
            {idx < info.length - 1 && <div className="h-4 w-0.5 bg-gray-300 mx-2"></div>}
          </div>
        ))}
      </div>
    ) : (
      <div className="pb-2"></div>
    );
  };

  return (
    <div className="mb-1 bg-white">
      <div className={`accordion shadow-md dark:shadow-none bg-white rounded-sm border ${open ? "rounded-t-md" : "rounded-md"}`}>
        <div onClick={() => setOpen(!open)} className="cursor-pointer">
          <div className="flex justify-between font-medium w-full text-start text-base text-slate-600 dark:text-slate-300 px-8 max-sm:px-4 py-4 pb-1">
            <div className="flex gap-3">
              <img src={moduleIcon} alt="module" />
              <span className="font-semibold text-primary-500 text-md">{module?.name}</span>
            </div>
            <span className={`text-slate-900 dark:text-white text-[22px] transition-transform duration-300 h-5 ${open ? "rotate-180 transform" : ""}`}>
              <Icon icon="heroicons-outline:chevron-down" className="text-[18px] text-gray-600" />
            </span>
          </div>
          {renderHeaderInfo()}
        </div>

        <div
          className={`overflow-hidden transition-all duration-500 ease-in-out ${open ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0"}`}
          style={{ transitionProperty: "max-height, opacity" }}
        >
          {open && (
            module?.contents?.length > 0 ? (
              module.contents.map((content, idx) => renderContentItem(content, idx))
            ) : (
              <p className="text-center text-sm py-3 border-t border-dashed">No Content Available</p>
            )
          )}
        </div>
      </div>

      {showLoginModal && (
        <QuizModalContent
          data={quizData}
          activeModal={showLoginModal}
          onClose={() => setShowLoginModal(false)}
        />
      )}
    </div>
  );
};

export default ModuleAccordion;
