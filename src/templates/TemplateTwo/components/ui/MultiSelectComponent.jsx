import React from "react";
import { useField, useFormikContext } from "formik";
import Select from "react-select";

const MultiSelectComponent = ({
  name,
  options,
  required,
  label,
  valueKey = "id",
  labelKey,
  ...props
}) => {
  const [field, meta, helpers] = useField(name);
  const { setFieldValue } = useFormikContext();

  const handleChange = (selectedOptions) => {
    // Map selected options to their IDs
    const selectedIds = selectedOptions ? selectedOptions?.map(option => option[valueKey]) : [];
    setFieldValue(name, selectedIds);
  };

  // Updated custom styles for a polished look
  const customStyles = {
    control: (base, state) => ({
      ...base,
      backgroundColor: "#fff",
      borderColor: state.isFocused ? "#93c5fd" : "#d1d5db", // Focus: blue-300, default: gray-300
      boxShadow: state.isFocused ? "0 0 0 2px rgba(96,165,250, 0.5)" : "none", // Blue ring on focus
      borderRadius: "0.375rem", // Rounded-md
      transition: "all 0.2s ease-in-out", // Smooth transition
      "&:hover": {
        borderColor: "#93c5fd", // Hover effect with blue-300
      },
      width: "100%",
    }),
    menu: (base) => ({
      ...base,
      borderRadius: "0.375rem", // Rounded corners for the dropdown
      zIndex: 10, // Ensure dropdown is above other elements
    }),
    multiValue: (base) => ({
      ...base,
      backgroundColor: "#e5e7eb", // Background for selected items (gray-200)
      borderRadius: "0.375rem", // Rounded-md for selected tags
    }),
    multiValueLabel: (base) => ({
      ...base,
      color: "#1f2937", // Text color for selected items (gray-800)
    }),
    multiValueRemove: (base) => ({
      ...base,
      color: "#6b7280", // Remove button color (gray-500)
      ":hover": {
        backgroundColor: "#f87171", // Red-400 on hover
        color: "#fff", // White close icon on hover
      },
    }),
  };

  // Transform field value from IDs to option objects
  const transformValue = () => {
    return options?.filter(option => field?.value?.includes(option[valueKey]));
  };

  return (
    <div>
      <label
        className="block text-[#1D1D1F] text-base font-medium mb-2"
        htmlFor={props.id || props.name}
      >
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <Select
        {...props}
        name={name}
        options={options}
        value={transformValue()}
        onChange={handleChange}
        getOptionValue={(option) => option[valueKey]}
        getOptionLabel={(option) => option[labelKey]}
        isMulti
        styles={customStyles}
      />
      {meta.touched && meta.error && (
        <span className="text-red-500 text-xs mt-2">{meta.error}</span>
      )}
    </div>
  );
};

export default MultiSelectComponent;