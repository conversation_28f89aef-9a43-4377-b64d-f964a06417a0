import React, { useEffect, useState } from "react";
import Icon from "@/components/ui/Icon";

const Pagination = ({
  totalPages,
  currentPage,
  handlePageChange,
  isReactTable = false,
  className = "custom-class",
}) => {
  const [pages, setPages] = useState([]);
  const [canPreviousPage, setCanPreviousPage] = useState(false);
  const [canNextPage, setCanNextPage] = useState(false);

  const siblingsCount = 1;

  useEffect(() => {
    let pagesArray = [];

    if (isReactTable) {
      for (let i = 0; i < totalPages; i++) {
        pagesArray.push(i);
      }
    } else {
      for (let i = 1; i <= totalPages; i++) {
        pagesArray.push(i);
      }
    }
    setPages(pagesArray);
  }, [totalPages, isReactTable]);

  useEffect(() => {
    if (isReactTable) {
      setCanPreviousPage(currentPage > 0);
      setCanNextPage(currentPage < totalPages - 1);
    } else {
      setCanPreviousPage(currentPage > 1);
      setCanNextPage(currentPage < totalPages);
    }
  }, [currentPage, totalPages, isReactTable]);

  const renderPageNumbers = pages.map((number) => {
    if (
      number >= currentPage - siblingsCount &&
      number <= currentPage + siblingsCount
    ) {
      return (
        <li key={number}>
          <button
            className={`page-link ${number === currentPage ? "active" : ""}`}
            onClick={() => handlePageChange(number)}
            disabled={number === currentPage}
          >
            {number}
          </button>
        </li>
      );
    }
    return null;
  });

  const renderReactTablePageNumbers = pages.map((number) => {
    if (
      number >= currentPage - siblingsCount &&
      number <= currentPage + siblingsCount
    ) {
      return (
        <li key={number}>
          <button
            className={`page-link ${number === currentPage ? "active" : ""}`}
            onClick={() => handlePageChange(number)}
            disabled={number === currentPage}
          >
            {number + 1}
          </button>
        </li>
      );
    }
    return null;
  });

  return (
    <div className={className}>
      <ul className="pagination flex items-center gap-2 justify-center">
        <li>
          <button
            className={`p-2 ${!canPreviousPage ? "opacity-50 cursor-not-allowed" : ""}`}
            onClick={() =>
              handlePageChange(isReactTable ? 0 : 1)
            }
            disabled={!canPreviousPage}
          >
            <Icon icon="heroicons:chevron-double-left-solid" />
          </button>
        </li>
        <li>
          <button
            className={`p-2 ${!canPreviousPage ? "opacity-50 cursor-not-allowed" : ""}`}
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={!canPreviousPage}
          >
            Prev
          </button>
        </li>

        {isReactTable ? renderReactTablePageNumbers : renderPageNumbers}

        <li>
          <button
            className={`p-2 ${!canNextPage ? "opacity-50 cursor-not-allowed" : ""}`}
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={!canNextPage}
          >
            Next
          </button>
        </li>
        <li>
          <button
            className={`p-2 ${!canNextPage ? "opacity-50 cursor-not-allowed" : ""}`}
            onClick={() =>
              handlePageChange(isReactTable ? totalPages - 1 : totalPages)
            }
            disabled={!canNextPage}
          >
            <Icon icon="heroicons:chevron-double-right-solid" />
          </button>
        </li>
      </ul>
    </div>
  );
};

export default Pagination;
