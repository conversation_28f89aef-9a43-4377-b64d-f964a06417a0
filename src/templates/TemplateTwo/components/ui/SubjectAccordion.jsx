import React, { useState } from "react";
import { ASSET_URL } from "@/config";
import { Icon } from "@iconify/react/dist/iconify.js";
import ModuleAccordion from "./ModuleAccordion";
import subjectIcon from "@/assets/images/subject-icon.png"; // subject-icon.png
const SubjectAccordion = ({ subject, course }) => {
  const [subjectOpen, setSubjectOpen] = useState(false);


  const getTextColor = (bgColor) => {
    if (!bgColor) return "#000"; 
    
    const hex = bgColor.replace("#", "");
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    console.log(luminance);
    return luminance > 0.5 ? "#000" : "#FFF";
  };


  return (
    <div>
      <div
        className={`accordion shadow-md dark:shadow-none bg-white rounded-sm border mb-3 ${
          subjectOpen ? "rounded-t-md" : "rounded-md"
        }`}
      >
        <div
          onClick={() => setSubjectOpen(!subjectOpen)}
          className={`cursor-pointer `}
          style={{
            backgroundColor: subject?.color_code,
            color: getTextColor(subject?.color_code),
          }}
        >
          <div
            className={`flex justify-between transition duration-150 font-medium w-full 
              text-start text-base px-8 max-sm:px-4 py-4  `}
          >
            <div className="flex items-center gap-3">
              <img
                src={subject?.icon ? ASSET_URL + subject?.icon : subjectIcon}
                className="w-14 h-14 rounded-full object-contain border"
                alt=""
              />
              <span
                className={`font-semibold text-md`}
              >
                {subject?.name}
              </span>
            </div>

            <span
              className={`text-[22px] transition-transform duration-300 h-5 ${
                subjectOpen ? "rotate-180 transform" : ""
              }`}
            >
              <Icon icon="heroicons-outline:chevron-down" />

            </span>
          </div>
        </div>

        <div
          className={`overflow-hidden transition-all duration-500 ease-in-out ${
            subjectOpen ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0"
          }`}
          style={{
            transitionProperty: "max-height, opacity",
          }}
        >
          {subjectOpen && (subject?.outlines?.length > 0 ? (
            <div>
              {subject?.outlines?.map((outline, idx) => (
                <ModuleAccordion key={idx} module={outline} course={course} />
              ))}
            </div>
          ) : (
            <p className="py-2 border-t border-dashed text-center text-sm">No Module Available</p>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SubjectAccordion;
