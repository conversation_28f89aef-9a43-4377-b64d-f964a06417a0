import { useMemo, useState } from "react";
import Icon from "@/components/ui/Icon";
import calendarIcon from "@/assets/images/svg/calender.svg";
import { useDispatch } from "react-redux";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
const localizer = momentLocalizer(moment);
import { setShowModal } from "@/store/assignmentStore";
import { redirect } from "react-router-dom";

const RoutineAccordion = ({
  title = "Course Routine",
  course,
  classSchedule,
  isOpen = false,
}) => {
  const [open, setOpen] = useState(isOpen);
  const [selectedDate, setSelectedDate] = useState(null);
  const [eventData, setEventData] = useState(null);
  const [contextMenu, setContextMenu] = useState(null);
  const dispatch = useDispatch();

  const events = useMemo(() => {
    return classSchedule?.map((schedule) => {
      const eventStart = new Date(schedule.schedule_datetime);
      const eventEnd = new Date(eventStart.getTime() + schedule.duration * 60000);
      const now = new Date();
  
      let eventColor = "#2196F3"; // Default: Future (Blue)
      if (now > eventEnd) {
        eventColor = "#BDBDBD"; // Past: Grey
      } else if (now >= eventStart && now <= eventEnd) {
        eventColor = "#4CAF50"; // Ongoing: Green
      }
  
      return {
        id: schedule.id,
        title: ` ${schedule.mentor} - ${schedule.title}`,
        start: eventStart,
        end: eventEnd,
        resource: { ...schedule, color: eventColor },
      };
    });
  }, [classSchedule]);
  

  const handleSlotSelect = (slotInfo) => {
    setSelectedDate(slotInfo.start);
    dispatch(setShowModal(true));
  };

  const closeContextMenu = () => {
    setContextMenu(null);
  };

  const handleEventSelect = (event) => {
    console.log(event);
    const now = new Date();
    const eventStart = new Date(event.start);
    const eventEnd = new Date(event.end);
  
    if (now >= eventStart && now <= eventEnd) {
      const selectedEvent = classSchedule.find((item) => item.id === event.id);
      setEventData(selectedEvent);
      if(selectedEvent.class_url) {
        window.open(selectedEvent.class_url, '_blank');
      }
      console.log(eventData);
    }
  };

  const formatTime = (time24) => {
    const [hours, minutes] = time24.split(":");
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? "PM" : "AM";
    const formattedHour = hour % 12 || 12;

    return `${formattedHour}:${minutes} ${ampm}`;
  };

  const toggleAccordion = () => setOpen(!open);

  return (
    <div className="space-y-5">
      <div
        className={`accordion shadow-md dark:shadow-none rounded-md ${
          open
            ? "bg-slate-50 dark:bg-slate-700 dark:bg-slate-700 opacity-80 rounded-t-md"
            : "bg-slate-50 dark:bg-slate-700 rounded-md"
        }`}
      >
        <div
          className="flex justify-between cursor-pointer gap-2 transition duration-150 font-medium w-full text-start bg-sky-50 rounded-lg text-base text-slate-600 px-6 py-5"
          onClick={toggleAccordion}
        >
          <span className="text-primary-500 text-xl font-semibold">
            <img src={calendarIcon} alt="" /> {title}
          </span>
          <span
            className={`text-slate-900 dark:text-white text-[22px] transition-all duration-300 h-5 ${
              open ? "rotate-180 transform" : ""
            }`}
          >
            <Icon icon="heroicons-outline:chevron-down" />
          </span>
        </div>

        <div
          className={`overflow-hidden transition-all duration-500 ease-in-out ${
            open ? "max-h-screen opacity-100" : "max-h-0 opacity-0"
          }`}
        >
          <div style={{ height: "800px" }} className="m-10" onClick={closeContextMenu}>
          <Calendar
            localizer={localizer}
            events={events}
            startAccessor="start"
            endAccessor="end"
            style={{ height: 800 }}
            selectable
            onSelectSlot={handleSlotSelect}
            onSelectEvent={handleEventSelect}
            views={["month", "week", "day"]}
            defaultView="month"
            defaultDate={new Date()}
            dayLayoutAlgorithm="no-overlap"
            eventPropGetter={(event) => {
              const backgroundColor = event.resource?.color || "#e53e3e"; // Default color if not provided
              return {
                style: {
                  backgroundColor,
                  color: "#fff",
                  borderRadius: "5px",
                  padding: "5px",
                },
              };
            }}
          />

          </div>
        </div>
      </div>

      <div
        id="printableRoutine"
        className="hidden"
        style={{
          padding: "20px",
          fontFamily: "Arial, sans-serif",
          border: "1px solid #ccc",
          width: "100%",
        }}
      >
        <div style={{ marginBottom: "20px", textAlign: "center" }}>
          <h2 style={{ margin: 0, fontSize: "20px", fontWeight: "bold" }}>
            Class List
          </h2>
        </div>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
            gap: "20px",
          }}
        >
          {classSchedule?.map((item, idx) => (
            <div
              key={idx}
              style={{
                border: "1px solid #ccc",
                borderRadius: "8px",
                padding: "10px",
                boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
              }}
            >
              <p
                style={{
                  fontSize: "14px",
                  fontWeight: "bold",
                  margin: "5px 0",
                }}
              >
                Date:{" "}
                {new Intl.DateTimeFormat("en-US", {
                  weekday: "short",
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                }).format(new Date(item?.schedule_datetime))}
              </p>
              <p style={{ fontSize: "14px", margin: "5px 0" }}>
                Time: {formatTime(item?.start_time)}
              </p>
              <p style={{ fontSize: "14px", margin: "5px 0" }}>
                Duration: {item?.duration} mins
              </p>
              <p style={{ fontSize: "14px", margin: "5px 0" }}>
                Mentor: {item?.mentor || "Unknown"}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RoutineAccordion;

