import { Icon } from "@iconify/react/dist/iconify.js";
import { useField } from "formik";
import React, { useState } from "react";

const Input = ({ icon, placeholder, required, type, label, defaultValue, disabled, ...props }, ref) => {
  const [field, meta] = useField(props);
  const isError = meta.touched && meta.error;
  const [password, setPassword] = useState(true);

//   console.log(ref);

  const inputClasses = `"block py-2 px-0 pl-10 w-full text-lg text-gray-900 bg-transparent border-0 border-b-2 ${
    isError ? "border-red-500" : "border-gray-300"
  } appearance-none focus:outline-none focus:ring-0 focus:border-blue-600 transition-colors duration-300 ease-in-out peer"
  }`;
  return (
    <div>
      {type !== 'password' && <div className="w-full flex items-center relative">
        <img className="absolute left-1" src={icon} alt="" />
        <input
          {...field}
          {...props}
          type={type}
          className={inputClasses}
          placeholder={placeholder}
          value={defaultValue}
          // disabled={disabled}
        //   ref={ref}
        />
        {/* <label
          htmlFor={props.id || props.name}
          className="peer-focus:font-medium absolute text-md text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:start-0 rtl:peer-focus:translate-x-1/4 peer-focus:text-blue-600 peer-focus:dark:text-blue-500 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label> */}
      </div>}


      {type === 'password' && <div className="w-full flex items-center relative">
        <img className="absolute left-1" src={icon} alt="" />
        <input
          {...field}
          {...props}
          type={password ? 'password' : 'text'}
          className={inputClasses + ' pr-10'}
          placeholder={placeholder}
        //   ref={ref}
        />
        {type === 'password' && <Icon icon={`${password ? 'lucide:eye' : 'lucide:eye-off'}`} className="absolute right-1 text-xl cursor-pointer" onClick={() => setPassword(!password)} />}
      </div>}
      {isError && <span className="text-red-500 text-sm">{meta.error}</span>}
    </div>
  );
};

export default Input;
