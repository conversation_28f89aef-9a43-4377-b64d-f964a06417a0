import React from "react";

const EduPackLoader = () => {
  return (
    <div className="fixed top-0 left-0 right-0 bottom-0 bg-white z-50 flex flex-col items-center justify-center">
      {/* Three dots animation */}
      <div className="flex space-x-2">
        <div className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 bg-primary-500 rounded-full animate-bounce delay-100"></div>
        <div className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 bg-primary-500 rounded-full animate-bounce delay-200"></div>
        <div className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 bg-primary-500 rounded-full animate-bounce delay-300"></div>
      </div>
      {/* EduPack branding */}
      <div className="mt-4 text-xl sm:text-2xl lg:text-3xl font-semibold text-primary-500 animate-pulse">
        EduPack
      </div>
      <div className="mt-2 text-sm sm:text-base text-gray-500">
        Empowering Learning Experiences
      </div>
    </div>
  );
};

export default EduPackLoader;
