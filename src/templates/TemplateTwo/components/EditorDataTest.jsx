import React from 'react';
import EditorData from './EditorData';

const EditorDataTest = () => {
  const testHtmlContent = `
    <h1>CKEditor Content Test</h1>
    <p>This is a test to verify that CKEditor content displays properly with all formatting.</p>
    
    <h2>Bullet Lists</h2>
    <ul>
      <li>First bullet point</li>
      <li>Second bullet point with <strong>bold text</strong></li>
      <li>Third bullet point with <em>italic text</em>
        <ul>
          <li>Nested bullet point</li>
          <li>Another nested point</li>
        </ul>
      </li>
      <li>Fourth bullet point</li>
    </ul>
    
    <h2>Numbered Lists</h2>
    <ol>
      <li>First numbered item</li>
      <li>Second numbered item</li>
      <li>Third numbered item with nested list:
        <ol>
          <li>Nested numbered item</li>
          <li>Another nested numbered item</li>
        </ol>
      </li>
      <li>Fourth numbered item</li>
    </ol>
    
    <h3>Mixed Lists</h3>
    <ul>
      <li>Bullet point</li>
      <li>Another bullet with numbered sub-list:
        <ol>
          <li>Numbered sub-item</li>
          <li>Another numbered sub-item</li>
        </ol>
      </li>
      <li>Final bullet point</li>
    </ul>
    
    <h2>Text Formatting</h2>
    <p>This paragraph contains <strong>bold text</strong>, <em>italic text</em>, and <u>underlined text</u>.</p>
    
    <h2>Table</h2>
    <table>
      <thead>
        <tr>
          <th>Header 1</th>
          <th>Header 2</th>
          <th>Header 3</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Cell 1</td>
          <td>Cell 2</td>
          <td>Cell 3</td>
        </tr>
        <tr>
          <td>Cell 4</td>
          <td>Cell 5</td>
          <td>Cell 6</td>
        </tr>
      </tbody>
    </table>
    
    <h2>Blockquote</h2>
    <blockquote>
      This is a blockquote to test the styling. It should have a left border and be styled differently from regular text.
    </blockquote>
    
    <h2>Code</h2>
    <p>Here is some inline <code>code example</code> within a paragraph.</p>
    
    <pre><code>// This is a code block
function example() {
  console.log("Hello, world!");
  return true;
}</code></pre>
    
    <h4>Heading 4</h4>
    <h5>Heading 5</h5>
    <h6>Heading 6</h6>
    
    <p>This test content should display with proper formatting including list markers, heading sizes, table borders, and all other CKEditor styling.</p>
  `;

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">EditorData Component Test</h1>
        <p className="text-gray-600 mb-4">
          This test verifies that CKEditor content displays properly with bullet points, numbered lists, 
          headings, tables, and other formatting elements.
        </p>
      </div>
      
      <div className="border border-gray-300 rounded-lg p-4 bg-white">
        <EditorData htmlData={testHtmlContent} />
      </div>
      
      <div className="mt-6 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-bold mb-2">Expected Results:</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>✅ Bullet points should show disc markers</li>
          <li>✅ Numbered lists should show numbers</li>
          <li>✅ Nested lists should have proper indentation</li>
          <li>✅ Headings should have different sizes</li>
          <li>✅ Bold and italic text should display correctly</li>
          <li>✅ Tables should have borders and proper spacing</li>
          <li>✅ Blockquotes should have left border and indentation</li>
          <li>✅ Code should have background color and monospace font</li>
        </ul>
      </div>
    </div>
  );
};

export default EditorDataTest;
