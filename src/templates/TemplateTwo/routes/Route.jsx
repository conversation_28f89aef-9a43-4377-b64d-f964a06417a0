import React, { Suspense, useEffect } from "react";
import { Navigate, Route, Routes, useLocation  } from "react-router-dom";

import Loading from "../components/Loading";
import Layout from "../layout/Layout";
import ProtectedRoute from "@/layout/ProtectedRoute";
import DeviceManager from "../pages/device-manager";
import CustomDynamicPage from "../pages/CustomDynamicPage";
import VideoScript from "../pages/content/VideoScript";
import AssignmentList from "../pages/assignment/list";
import AssignmentsList from "../pages/mentor/Assignment";
import LiveClassList from "../pages/mentor/LiveClasses";
import CertificateDetails from "../pages/certification/certificate-details";
import StudentLiveClasses from "../pages/student/live-classes";
import StudentProfile from "../pages/profile/StudentProfile";
import UnderConstruction from "../pages/under-construction";
import MentorCourseList from "../pages/course/MentorCourseList";
import SearchResults from "@/components/partials/header/Tools/SearchResults";
import ExamResult from "../pages/student/exam-result";
import AttendanceList from "../pages/mentor/Attendance";
import AnswerDetails from "../pages/mentor/Assignment/AnswerDetails";
import ExamDetails from "../pages/exam-details/ExamDetails";
import ContentSubjectDetails from "../pages/content-subject/ContentSubjectDetails";
import ContentDetails from "../pages/content/ContentDetails";
import Quiz from "../pages/quiz/Quiz";
import Home from "../pages/home";
import CourseDetails from "../pages/course/CourseDetails";
import CoursePayment from "../pages/course/CoursePayment";
import CoursePaymentSuccess from "../pages/course/CoursePaymentSuccess";
import CourseList from "../pages/course/CourseList";
import CategoryList from "../pages/course/CategoryList";
import StudentCourseList from "../pages/course/StudentCourseList";
import StudentCertificates from "../pages/certification/list";

import WrittenExam from "../pages/written-exam/WrittenExam";
import Assingments from "../pages/assignment";
import AssignmentDetails from "../pages/assignment/details";
import MentorAssignmentDetails from "../pages/mentor/Assignment/AssignmentDetails";
import DiscussionPage from "../pages/discussion";
// Public routes
import Announcement from "../pages/Announcement";
import Login from "../pages/auth/Authentication/login";
import OrganizationAdminRegistration from "../pages/auth/OrganizationAdminRegistration";
import TryNewLMS from "../pages/auth/TryNewLMS";
import RegistrationSuccessful from "../pages/auth/RegistrationSuccessful";
import ForgotPass from "../pages/auth/forgot-password";
import Error from "../pages/404";
import Dashboard from "../pages/dashboard";
import Profile from "../pages/profile/Profile";
// Expert Items
import ExpertDashboard from "../pages/expert/dashboard";
import Schedule from "../pages/expert/Schedule";
//mentor file path
import MentorDashboard from "../pages/mentor/dashboard/index";
//student file path
import StudentDashboard from "../pages/student/dashboard/index";
import Payment from "../pages/Payments";
import PaymentSuccess from "../pages/PaymentSuccess";
import PaymentCancel from "../pages/PaymentCancel";
import PaymentError from "../pages/PaymentError";
import CyberSourcePaymentPage from "../pages/CyberSourcePaymentPage";
import PaymentDemo from "../pages/PaymentDemo";
import CourseRatingDemo from "../pages/course/CourseRatingDemo";

const AllRoutes = () => {
  const { pathname } = useLocation();
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  const user_type = JSON.parse(window.localStorage.getItem("user_type"));
  return (
    <Routes>
      <Route path="/" element={<Layout />}>
        <Route path="" element={<Home />} />
        <Route path="/announcements" element={<Announcement />} />
        <Route path="cybersourse-payment" element={<CyberSourcePaymentPage />} />
        <Route path="payment-success" element={<PaymentSuccess />} />
        <Route path="payment-cancel" element={<PaymentCancel />} />
        <Route path="payment-error" element={<PaymentError />} />
        <Route path="payment-demo" element={<PaymentDemo />} />
        <Route path="course-rating-demo" element={<CourseRatingDemo />} />
        {/* <Route
          path="dashboard"
          element={user_type == "Student" ? <Dashboard /> : <ExpertDashboard />}
        /> */}
        <Route
          path="dashboard"
          element={
            <ProtectedRoute>
              <StudentDashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="mentor-dashboard"
          element={
            <ProtectedRoute>
              <MentorDashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="profile"
          element={user_type == "Student" ? <StudentProfile /> : <Profile />}
        />

        {/* <Route path="expert-dashboard" element={<ExpertDashboard />} /> */}
        {/* <Route path="mentor-dashboard" element={<MentorDashboard />} /> */}

        {/* Student Path Route Start */}
        <Route path="student-dashboard" element={<StudentDashboard />} />
        <Route
          path="student/live-class-list"
          element={<StudentLiveClasses />}
        />

        {/* Student Path Route End */}
        <Route path="courses" element={<CategoryList />} />
        {/* <Route path="courses" element={<CourseList />} /> */}
        <Route path="courses/:menuId" element={<CourseList />} />
        <Route path="courses/:menuId/:subMenuId" element={<CourseList />} />
        <Route path="course-details/:id" element={<CourseDetails />} />
        <Route path="course/:slug" element={<CourseDetails />} />
        <Route path="course-discussion/:courseId" element={<DiscussionPage />} />
        <Route path="content-details/:id" element={<ContentDetails />} />
        {/* <Route path="my-courses" element={<StudentCourseList />} /> */}

        <Route
          path="my-courses"
          element={
            <ProtectedRoute>
              {user_type == "Student" ? (
                <StudentCourseList />
              ) : (
                <MentorCourseList />
              )}
            </ProtectedRoute>
          }
        />
        <Route
          path="completed-courses"
          element={
            <ProtectedRoute>
              {user_type == "Student" ? (
                <StudentCourseList />
              ) : (
                <MentorCourseList />
              )}
            </ProtectedRoute>
          }
        />
        <Route
          path="ongoing-courses"
          element={
            <ProtectedRoute>
              {user_type == "Student" ? (
                <StudentCourseList />
              ) : (
                <MentorCourseList />
              )}
            </ProtectedRoute>
          }
        />
        <Route
          path="my-certificates"
          element={
            <ProtectedRoute>
              <StudentCertificates />
            </ProtectedRoute>
          }
        />
        <Route
          path="my-payments"
          element={
            <ProtectedRoute>
              <Payment />
            </ProtectedRoute>
          }
        />
        <Route
          path="/my-certificates/:id"
          element={
            <ProtectedRoute>
              <CertificateDetails />
            </ProtectedRoute>
          }
        />
        <Route
          path="my-assignments"
          element={
            <ProtectedRoute>
              <Assingments />
            </ProtectedRoute>
          }
        />

        <Route
          path="my-assignment"
          element={
            <ProtectedRoute>
              <Assingments />
            </ProtectedRoute>
          }
        />
        <Route
          path="content-subject-details/:id"
          element={<ContentSubjectDetails />}
        />
        <Route
          path="course-checkout/:id"
          element={
            <ProtectedRoute>
              <CoursePayment />
            </ProtectedRoute>
          }
        />
        <Route
          path="course-payment-success"
          element={
            <ProtectedRoute>
              <CoursePaymentSuccess />
            </ProtectedRoute>
          }
        />
        <Route path="quiz/:quizId/:courseId/:resultId" element={<Quiz />} />
        {/* <Route path="script/:scriptId" element={<VideoScript />} /> */}
        <Route
          path="quiz/written-exam/:resultId/:quizId/:courseId"
          element={
            <ProtectedRoute>
              <WrittenExam />
            </ProtectedRoute>
          }
        />
        <Route path="exam-details/:id" element={<ExamDetails />} />
        <Route path="exam-result" element={<ExamResult />} />
        <Route path="assignment/:id" element={<AssignmentDetails />} />
        <Route path="mentor-schedule-list/:id" element={<Schedule />} />
        <Route path="device-manager" element={<DeviceManager />} />
        {/* <Route path="*" element={<Navigate to="/404" />} /> */}
        <Route path="assignment-list" element={<AssignmentsList />} />
        <Route
          path="assignment-details/:id"
          element={<MentorAssignmentDetails />}
        />
        <Route path="attendance-list" element={<AttendanceList />} />
        <Route path="answer-details/:id" element={<AnswerDetails />} />
        <Route path="live-class-list" element={<LiveClassList />} />
        <Route
          path="/try-new-lms"
          element={
            <Suspense fallback={<Loading />}>
              <TryNewLMS />
            </Suspense>
          }
        />

        <Route
          path="/search"
          element={
            <Suspense fallback={<Loading />}>
              <SearchResults />
            </Suspense>
          }
        />

        <Route
          path="/under-construction"
          element={
            <Suspense fallback={<Loading />}>
              <UnderConstruction />
            </Suspense>
          }
        />
      </Route>

      <Route
        path="/login"
        element={
          <Suspense fallback={<Loading />}>
            <Login />
          </Suspense>
        }
      />

      <Route
        path="/registraion-successful"
        element={
          <Suspense fallback={<Loading />}>
            <RegistrationSuccessful />
          </Suspense>
        }
      />
      {/* <Route
        path="/register"
        element={
          <Suspense fallback={<Loading />}>
            <Register />
          </Suspense>
        }
      /> */}
      <Route
        path="/institute-registration"
        element={
          <Suspense fallback={<Loading />}>
            <OrganizationAdminRegistration />
          </Suspense>
        }
      />
      <Route
        path="/forgot-password"
        element={
          <Suspense fallback={<Loading />}>
            <ForgotPass />
          </Suspense>
        }
      />

      <Route
        path="/404"
        element={
          <Suspense fallback={<Loading />}>
            <Error />
          </Suspense>
        }
      />

      <Route path="/:slug" element={<Layout />}>
        <Route path="" element={<CustomDynamicPage />} />
      </Route>
    </Routes>
  );
};

export default AllRoutes;
