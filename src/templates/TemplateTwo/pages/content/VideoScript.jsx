// import React, { useState } from "react";
// import Loading from "@/components/Loading";
// import { useParams, useNavigate } from "react-router-dom";
// import useFetch from "@/hooks/useFetch";
// import { Icon } from "@iconify/react";
// import VideoPlayer from "@/components/ui/VideoPlayer";
// import ReactPlayer from "react-player";
// import GoBack from "@/components/ui/GoBack";
// import { ASSET_URL } from "@/config";
// import ModuleAccordion from "@/components/ui/ModuleAccordion";

import ModuleAccordion from "@/components/ui/ModuleAccordion";

// const VideoScript = () => {
//   const [isExpanded, setIsExpanded] = useState(false);
//   const { id } = useParams();
//   const navigate = useNavigate();
//   const {
//     data: contentDetails,
//     isLoading,
//     isError,
//   } = useFetch({
//     queryKey: `content-details`,
//     endPoint: `get-content-details?id=${id}`,
//   });

//   console.log(contentDetails?.data);

//   if (isLoading) return <Loading />;
//   if (isError) return <div>Error fetching data</div>;
//   return (
//     <div className="">
//       {contentDetails?.data?.video && (
//         <div className="container pt-1 lg:pt-5 space-y-3">
//           <GoBack title={`Module ${contentDetails?.data?.id}`} />
//           <div className="h-[450px] max-sm:h-[220px] rounded-lg overflow-hidden">
//             <div className="h-[550px]">
//               <div className="h-full">
//                 <iframe
//                   className="w-full h-full"
//                   src="https://drive.google.com/file/d/1M1YyOQQ1YKFKW24NpNBPVBxcjBs3tb0R/preview"
//                   frameBorder="0"
//                   allowFullScreen
//                 ></iframe>
//               </div>
//             </div>
//           </div>
//           <div className="space-y-3">
//             <h2 className="text-3xl text-sky-700">
//               {contentDetails?.data?.video?.title}
//             </h2>
//             <p>
//               {isExpanded
//                 ? contentDetails?.data?.video?.description
//                 : contentDetails?.data?.video?.description?.slice(0, 300)}
//               {contentDetails?.data?.video?.description?.length > 300 && (
//                 <span
//                   className="text-sky-600 cursor-pointer"
//                   onClick={() => setIsExpanded(!isExpanded)}
//                 >
//                   {isExpanded ? " Show less" : " ...See more"}
//                 </span>
//               )}
//             </p>

//             <ModuleAccordion module={contentDetails.data.video} />
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// export default VideoScript;

{
  /* <div className="h-[550px]">
        <div className="h-full">
          <iframe
            className="w-full h-full"
            src="https://drive.google.com/file/d/1M1YyOQQ1YKFKW24NpNBPVBxcjBs3tb0R/preview"
            frameBorder="0"
            allowFullScreen
          ></iframe>
        </div>
      </div> */
}

const VideoScript = () => {
  return (
    <section className=" container">
      <div className="h-[550px] max-sm:h-[350px]">
        <div className="h-full">
          <iframe
            className="w-full h-full"
            src="https://drive.google.com/file/d/1M1YyOQQ1YKFKW24NpNBPVBxcjBs3tb0R/preview"
            frameBorder="0"
            allowFullScreen
          ></iframe>
        </div>
      </div>

      <div className="mt-8">
        <ModuleAccordion module={[]} isOpen={true} />
      </div>
    </section>
  );
};

export default VideoScript;
