import React, { useEffect, useState } from 'react'
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { Icon } from "@iconify/react";
import api from "@/server/api";
import logo from '@/components/partials/footer/logo.svg';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import bg from '@/assets/sass-lms/Signup_BG.png';

const OrganizationAdminRegistration = () => {

    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const initialValues = {
        name: '',
        email: '',
        contact_no: '',
        username: '',
        password: '',
        user_type: '',
        i_accept: false
    };

    const validationSchema = Yup.object().shape({
        name: Yup.string().required('Name is required'),
        email: Yup.string().required('Email is required'),
        contact_no: Yup.string().required('Contact Number is required'),
        username: Yup.string().required('Username is required'),
        password: Yup.string().required('Password is required'),
        password_confirmation: Yup.string()
           .oneOf([Yup.ref('password'), null], 'Passwords must match'),
        i_accept: Yup.boolean(true).oneOf([true], 'Please accept the terms and conditions')
        .required('Please accept the terms and conditions'),
    });
    const { isAuth } = useSelector((state) => state.auth);

    useEffect(() => {
        if (isAuth) {
            const from = location.state?.from?.pathname || '/';
            navigate(from, { replace: true });
        }
    }, [isAuth, navigate]);
    const location = useLocation();
    const onSubmit = async (values, { setSubmitting }) => {
        console.log(values);
        const formData = new FormData();
        formData.append('name', values.name);
        formData.append('email', values.email);
        formData.append('contact_no', values.contact_no);
        formData.append('username', values.username);
        formData.append('password', values.password);
        try {
            setLoading(true);
            const response = await api.post(import.meta.env.VITE_BASE_URL +'/api/auth/client-registration', formData);
            navigate('/registraion-successful');
            // window.location.reload();

        } catch (error) {
            console.error(error);
        } finally {
            setSubmitting(false);
            setLoading(false);
        }
    };

    return (
        <div className="max-w-7xl mx-auto h-screen">
        <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={onSubmit}
        >
            <Form>
            <div className="grid md:grid-cols-2 items-center gap-8 h-full">
                <div className="h-full md:py-6 flex items-center relative max-md:before:hidden before:absolute">
                        <div className="mb-12">
                            <a href="/" onClick={() => navigate('/')} className="flex items-center space-x-3 rtl:space-x-reverse">
                                <img src={logo} className="h-8" alt="Logo" />
                            </a>
                            <h3 className="text-gray-800 text-4xl mt-12 font-extrabold">Start Your Own LMS</h3>

                            <p className="text-gray-800 text-sm mt-6">Welcome to the LMS (Learning Management System) where you can learn, grow and connect with like-minded individuals. Our platform is designed to provide you with a seamless learning experience, enabling you to acquire new skills or deepen your knowledge in any subject. Join us and embark on an exciting journey of learning.</p>
                            {/* <p className="text-gray-800 text-sm mt-6">Immerse yourself in a hassle-free login journey with our intuitively designed login form. Effortlessly access your account.</p> */}
                            <img src={bg} className="rounded-md lg:w-4/5 md:w-11/12 z-50 relative mt-6" alt="Dining Experience" />
                        </div>
                    
                </div>
                    <div className="max-w-lg max-md:mx-auto w-full p-6">
                      
                        <div>
                            <label className="text-gray-800 text-[15px] mb-2 block">Full Name</label>
                            <Field name="name" type="text" className="w-full text-sm text-gray-800 bg-gray-100 focus:bg-transparent px-4 py-3.5 rounded-md outline-blue-600" placeholder="Enter Name" />
                            <ErrorMessage name="name">
                                { msg => <div className='text-red-500 text-sm'>{msg}</div> }
                            </ErrorMessage>
                            <Icon icon="akar-icons:user" />
                        </div>
                        <div className='mt-2'>
                            <label className="text-gray-800 text-[15px] mb-2 block">Email</label>
                            <Field name="email" type="text" className="w-full text-sm text-gray-800 bg-gray-100 focus:bg-transparent px-4 py-3.5 rounded-md outline-blue-600" placeholder="Ex. <EMAIL>" />
                            <ErrorMessage name="email">
                                { msg => <div className='text-red-500 text-sm'>{msg}</div> }
                            </ErrorMessage>
                            <Icon icon="akar-icons:user" />
                        </div>
                        <div className='mt-2'>
                            <label className="text-gray-800 text-[15px] mb-2 block">Contact Number</label>
                            <Field name="contact_no" type="text" className="w-full text-sm text-gray-800 bg-gray-100 focus:bg-transparent px-4 py-3.5 rounded-md outline-blue-600" placeholder="Enter Contact Number" />
                            <ErrorMessage name="contact_no">
                                { msg => <div className='text-red-500 text-sm'>{msg}</div> }
                            </ErrorMessage>
                            <Icon icon="akar-icons:user" />
                        </div>
                        <div className='mt-2'>
                            <label className="text-gray-800 text-[15px] mb-2 block">Username</label>
                            <Field name="username" type="text" className="w-full text-sm text-gray-800 bg-gray-100 focus:bg-transparent px-4 py-3.5 rounded-md outline-blue-600" placeholder="Enter username" />
                            <ErrorMessage name="username">
                                { msg => <div className='text-red-500 text-sm'>{msg}</div> }
                            </ErrorMessage>
                            <Icon icon="akar-icons:user" />
                        </div>
                        <div className='mt-2'>
                            <label className="text-gray-800 text-[15px] mb-2 block">Password</label>
                            <Field name="password" type="password" className="w-full text-sm text-gray-800 bg-gray-100 focus:bg-transparent px-4 py-3.5 rounded-md outline-blue-600" placeholder="Enter password" />
                            <ErrorMessage name="password">
                                { msg => <div className='text-red-500 text-sm'>{msg}</div> }
                            </ErrorMessage>
                            <Icon icon="akar-icons:lock" />
                        </div>

                        <div className='mt-2'>
                            <label className="text-gray-800 text-[15px] mb-2 block">Re-enter Password</label>
                            <Field name="password_confirmation" type="password" className="w-full text-sm text-gray-800 bg-gray-100 focus:bg-transparent px-4 py-3.5 rounded-md outline-blue-600" placeholder="Re-enter password" />
                            <ErrorMessage name="password_confirmation">
                                { msg => <div className='text-red-500 text-sm'>{msg}</div> }
                            </ErrorMessage>
                            <Icon icon="akar-icons:lock" />
                        </div>

                        <div className=" gap-4 justify-between mt-4">
                            <div className="flex items-center">
                                <Field id="i_accept" name="i_accept" type="checkbox" className="shrink-0 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded-md" />
                                <label htmlFor="i_accept" className="ml-3 block text-sm text-gray-800">
                                    I agree to the <a href="#" className="text-blue-600 font-semibold hover:underline">terms and conditions</a>
                                </label>
                                
                            </div>
                            
                            <ErrorMessage name="i_accept">
                                { msg => <div className='text-red-500 text-sm'>{msg}</div> }
                            </ErrorMessage>
                        </div>


                        <div className="mt-8">
                            <button type="submit" className={`w-full shadow-xl py-3 px-6 text-sm tracking-wide font-semibold rounded-md text-white ${loading ? 'bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'} focus:outline-none`} disabled={loading}>
                                {loading ? 'Loading...' : 'Sign Up'}
                            </button>
                        </div>
                        <p className="text-sm mt-8 text-center text-gray-800">I have already an account 
                            <a href="/login" className="text-blue-600 font-semibold tracking-wide hover:underline ml-1">Login </a>
                        </p>
                   </div>

            </div>
                </Form>
                </Formik>
        </div>
    )
}

export default OrganizationAdminRegistration;