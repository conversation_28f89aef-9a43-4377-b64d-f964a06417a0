import { createSlice } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "react-toastify";
// import { CLMS_URL } from "@/config";

const initialUsers = () => {
    const item = window.localStorage.getItem("users");
    return item ? JSON.parse(item) : [];
};
// save users in local storage

const initialIsAuth = () => {
    const item = window.localStorage.getItem("isAuth");
    return item ? JSON.parse(item) : false;
};

export const authSlice = createSlice({
    name: "auth",
    initialState: {
        registerData: null,
        user: null,
        token: null,
        users: initialUsers(),
        isAuth: initialIsAuth(),
    },
    reducers: {
 
        handleRegister: (state, action) => {
            state.registerData = action.payload;
            
            const data = action.payload;
            state.user = action.payload;
            state.token = data.token;
            state.isAuth = data;
            const _token = data.token;
            // save in local storage
            window.localStorage.setItem("isAuth", JSON.stringify(state.isAuth));
            window.localStorage.setItem("_token", JSON.stringify(_token));
            window.localStorage.setItem("id", JSON.stringify(data.id));
            window.localStorage.setItem("name", JSON.stringify(data.name));
            window.localStorage.setItem("email", JSON.stringify(data.email));
            window.localStorage.setItem("username", JSON.stringify(data.username));
            window.localStorage.setItem("interests", JSON.stringify(data.interests));
            window.localStorage.setItem("user_type", JSON.stringify(data.user_type));
            window.localStorage.setItem("image", JSON.stringify(data.image));
            window.localStorage.setItem("address", JSON.stringify(data.address));
            window.localStorage.setItem("contact_no", JSON.stringify(data.contact_no));
            // set token to axios header
            axios.defaults.headers.common["Authorization"] = `Bearer ${_token}`;
        },

        handleLogin: (state, action) => {

            const data = action.payload;
            state.user = action.payload;
            state.token = data.token;
            state.isAuth = data;
            const _token = data.token;
            // axios.defaults.headers.common["Authorization"] = `Bearer ${_token}`;
            // save in local storage
            window.localStorage.setItem("isAuth", JSON.stringify(state.isAuth));
            window.localStorage.setItem("_token", JSON.stringify(_token));
            window.localStorage.setItem("id", JSON.stringify(data.id));
            window.localStorage.setItem("name", JSON.stringify(data.name));
            window.localStorage.setItem("email", JSON.stringify(data.email));
            window.localStorage.setItem("username", JSON.stringify(data.username));
            window.localStorage.setItem("interests", JSON.stringify(data.interests));
            window.localStorage.setItem("user_type", JSON.stringify(data.user_type));
            window.localStorage.setItem("image", JSON.stringify(data.image));
            window.localStorage.setItem("address", JSON.stringify(data.address));
            window.localStorage.setItem("contact_no", JSON.stringify(data.contact_no));
            // set token to axios header

        },
        handleLogout: (state, action) => {
            state.isAuth = action.payload;
            // remove isAuth from local storage
            window.localStorage.removeItem("isAuth");
            window.localStorage.removeItem("_token");
            window.localStorage.removeItem("id");
            window.localStorage.removeItem("name");
            window.localStorage.removeItem("email");
            window.localStorage.removeItem("username");
            window.localStorage.removeItem("interests");
            window.localStorage.removeItem("user_type");
            window.localStorage.removeItem("image");
            window.localStorage.removeItem("address");
            window.localStorage.removeItem("contact_no");
            window.localStorage.removeItem("updated_at");
      
            // window.location.href=CLMS_URL+"?logout=true";
        },
    },
});

export const { handleRegister, handleLogin, handleLogout, getToken } = authSlice.actions;
export default authSlice.reducer;
