import React, { useState } from "react";
import icon from "@/assets/images/auth/pgone emal.svg";
import * as Yup from "yup";
import { Form, Formik } from "formik";
import Input from "@/components/form/Common/Input";
import { useQuery } from "@tanstack/react-query";
import api from "@/server/api";
import { toast } from "react-toastify";

const ForgotPass = ({ handleNextStep }) => {
  const [loading, setLoading] = useState(false);
  const { data = [] } = useQuery({
    queryKey: ["organization_details"],
    queryFn: async () => {
      const { data } = await api.get(
        import.meta.env.VITE_BASE_URL + "/api/website/organization-details"
      );
      return data;
    },
  });

  const organization_id = data.data?.id;

  const initialValues = {
    phone_or_email: "",
    organization_id: organization_id,
  };

  const validationSchema = Yup.object().shape({
    phone_or_email: Yup.string()
      .required("Email/Number is required")
      .test(
        "email-or-number",
        "Must be a valid email or number",
        function (value) {
          // Check if it's either a valid email or a number
          const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
          const isValidEmail = emailRegex.test(value);
          const isValidNumber = Yup.number().integer().isValidSync(value);
          return isValidEmail || isValidNumber;
        }
      ),
  });

  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      const response = await api.post(
        import.meta.env.VITE_BASE_URL + "/api/forgot-password-by-mobile",
        {...values, organization_id: organization_id}
      );
      if (response.data.data.otp_id) {
        const otpData = response.data.data;
        handleNextStep("verify_otp", otpData);
      }
    } catch (error) {
      toast.error(error.response.data.message);
      console.log(error);
    }
    finally{
      setLoading(false)
    }
  };

  return (
    <div className="w-[355px]">
      <p className="text-sky-600 font-semibold text-3xl max-sm:text-xl mb-4">
        Forgot Password
      </p>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        <Form className="space-y-3">
          <Input
            name="phone_or_email"
            id="phone_or_email"
            type="text"
            placeholder={"Continue with your Mobile/Email"}
            icon={icon}
            required={true}
            label={"Enter Your Email/Number"}
          />

          <button
            type="submit"
            className={`w-full bg-[#1B69B3] text-xl text-white py-2.5 rounded-md shadow-lg mt-5 ${
              loading ? "bg-gray-400" : "hover:bg-sky-700"
            } focus:outline-none`}
            disabled={loading}
          >
            {loading ? "Loading..." : "Verify"}
          </button>
        </Form>
      </Formik>
    </div>
  );
};
export default ForgotPass;
