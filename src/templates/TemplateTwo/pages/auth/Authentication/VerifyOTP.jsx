import { Form, Formik } from "formik";
import React, { useEffect, useState } from "react";
import * as Yup from "yup";
import api from "@/server/api";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { useQuery } from "@tanstack/react-query";
import { useDispatch } from "react-redux";
import { handleLogin } from "../common/store";
import { calculateTimeLeft, formatTimeHuman, processOTPResponse, debugDateTime } from "@/utils/datetime";

const VerifyOTP = ({ handleNextStep, otpData, loginMail, usedFor = "login" }) => {
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [expireTime, setExpireTime] = useState(otpData?.expires_at || null);
  const [timeLeft, setTimeLeft] = useState(0);
  const [canResend, setCanResend] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { data = [] } = useQuery({
    queryKey: ["organization_details"],
    queryFn: async () => {
      const { data } = await api.get(
        import.meta.env.VITE_BASE_URL + "/api/website/organization-details"
      );
      return data;
    },
  });

  const organization_id = data.data?.id;

  const formatAddress = (mailOrPhone) => {
    if (mailOrPhone.includes("@")) {
      return `${mailOrPhone[0]}${"*".repeat(
        mailOrPhone.split("@")[0].length - 1
      )}@${mailOrPhone.split("@")[1]}`;
    } else if (/^\d+$/.test(mailOrPhone)) {
      const visibleDigit = mailOrPhone.slice(-3);
      const hiddenDigit = "*".repeat(mailOrPhone.length - 3);
      return `${hiddenDigit}${visibleDigit}`;
    }
    return "";
  };

  const [isMail, setIsMail] = useState(loginMail.includes("@"));
  const [formatted, setFormatted] = useState(formatAddress(loginMail));

  const initialValues = {
    first_number: "",
    second_number: "",
    third_number: "",
    fourth_number: "",
  };

  const validationSchema = Yup.object().shape({
    first_number: Yup.string().required("Please provide full OTP").length(1),
    second_number: Yup.string().required("Please provide full OTP").length(1),
    third_number: Yup.string().required("Please provide full OTP").length(1),
    fourth_number: Yup.string().required("Please provide full OTP").length(1),
  });

  const handleResendOTP = async () => {
    if (resendLoading || timeLeft > 0) return;

    try {
      setResendLoading(true);

      // Determine the used_for parameter based on context
      let usedForParam = usedFor;
      if (otpData?.used_for) {
        usedForParam = otpData.used_for;
      }

      const response = await api.post("/api/resend-otp", {
        phone_or_email: loginMail,
        used_for: usedForParam,
        organization_id: organization_id || 1, // Fallback to organization_id 1 if not available
      });

      if (response.data.success) {
        // Process the resend response with proper datetime handling
        const processedResponse = processOTPResponse(response.data);

        setExpireTime(processedResponse.data.expired_at);
        setCanResend(false);

        toast.success(response.data.message || "OTP has been resent successfully");
        console.log("New OTP Data:", processedResponse.data);
      } else {
        throw new Error(response.data.message || "Failed to resend OTP");
      }
    } catch (error) {
      console.error("Resend OTP Error:", error);

      if (error.response?.status === 429) {
        const waitTime = error.response.data.data?.can_resend_after || 60;
        toast.error(`Please wait ${waitTime} seconds before requesting a new OTP`);
      } else {
        toast.error(error.response?.data?.message || error.message || "Failed to resend OTP");
      }
    } finally {
      setResendLoading(false);
    }
  };

  // Use utility function for time calculation with debug logging
  const calculateTimeLeftWithDebug = (expireTime) => {
    debugDateTime("VerifyOTP Timer", expireTime);
    return calculateTimeLeft(expireTime);
  };

  useEffect(() => {
    if (!expireTime) return;

    // Use utility function with debug logging
    const initialTimeLeft = calculateTimeLeftWithDebug(expireTime);
    setTimeLeft(initialTimeLeft);
    setCanResend(initialTimeLeft <= 0);

    if (initialTimeLeft > 0) {
      const timerId = setInterval(() => {
        setTimeLeft((prevTime) => {
          if (prevTime <= 1) {
            clearInterval(timerId);
            setCanResend(true);
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
      return () => clearInterval(timerId);
    }
  }, [expireTime]);

  // Use utility function for time formatting
  const formatTime = formatTimeHuman;

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    const otp = Object.values(values).join("");
    if (
      !values.first_number ||
      !values.second_number ||
      !values.third_number ||
      !values.fourth_number
    ) {
      setFieldError("first_number", "Please provide the OTP");
      return;
    }

    setLoading(true);
    try {
      const response = await api.post("/api/verify-otp", {
        otp,
        otp_id: otpData?.otp_id
      });

      const otp_id = response.data.data.otp_id;
      if (response.data.data.used_for === "forgot_password") {
        handleNextStep("reset-password", otp_id);
      } else if (response.data.data.used_for === "register") {
        handleNextStep("register", otp_id);
      } else if (response.data.data.used_for === "login" || !response.data.data.used_for) {
        if (response.data.data.token) {
          api.setTokenHeader(response.data.data.token);
          dispatch(handleLogin(response.data.data));
          if (response.data.data.user_type === "Mentor") {
            navigate("/mentor-dashboard");
          } else {
            navigate("/");
          }
        } else {
          handleNextStep("mail_and_password", { user_id: response.data.data.user_id });
        }
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Invalid OTP. Please try again.");
      console.error("OTP Verification Error:", error);
    } finally {
      setLoading(false);
      setSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 max-sm:space-y-4 max-w-[355px] max-sm:py-16">
      <h2 className="text-2xl text-gray-500">
        Verify Your {isMail ? "Mail" : "Mobile"} Address
      </h2>
      <p className="text-sky-600">
        Enter the 4-digit code that was sent to the {isMail ? "mail" : "number"}{" "}
        you provided ({formatted})
      </p>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, setFieldValue, setTouched, errors, touched }) => (

          <Form>
            <div className="flex md:gap-3 items-center">
              {[
                "first_number",
                "second_number",
                "third_number",
                "fourth_number",
              ].map((name, index) => (
                <input
                  key={name}
                  name={name}
                  className={`border-2 border-gray-300 p-5 max-sm:w-14 mx-auto max-sm:p-2 text-center rounded-lg focus:outline-none focus:ring-0 focus:border-sky-500 shadow-lg w-20 text-3xl no-spinner ${
                    touched[name] && errors[name] ? "border-red-400" : ""
                  }`}
                  maxLength={1}
                  value={values[name]}
                  onChange={(e) => {
                    const value = e.target.value.slice(0, 1);
                    setFieldValue(name, value);
                    if (value && index < 3) {
                      document.querySelector(`input[name="${
                        ["second_number", "third_number", "fourth_number"][index]
                      }"]`)?.focus();
                    }
                  }}
                  onKeyDown={(e) => {
                    if (e.key === "Backspace" && !values[name]) {
                      if (index > 0) {
                        const prevField = ["first_number", "second_number", "third_number"][index - 1];
                        setFieldValue(prevField, "");
                        document.querySelector(`input[name="${prevField}"]`)?.focus();
                      }
                    }
                  }}
                  onPaste={(e) => {
                    e.preventDefault();
                    const pastedData = e.clipboardData.getData("Text").trim();
                    if (/^\d{4}$/.test(pastedData)) {
                      const valuesArray = pastedData.split("").slice(0, 4);
                      const fields = [
                        "first_number",
                        "second_number",
                        "third_number",
                        "fourth_number",
                      ];
                      fields.forEach((field, idx) => {
                        setFieldValue(field, valuesArray[idx] || "");
                        setTouched({ ...touched, [field]: true }, false);
                      });
                  
                      // Trigger validation manually after pasting
                      setTimeout(() => {
                        document.querySelector(`input[name="fourth_number"]`)?.focus();
                        // Add this line
                        validateForm(); // ✅ trigger validation after all values are set
                      }, 0);
                    }
                  }}
                  
                  type="number"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  tabIndex={index + 1}
                />
              ))}
            </div>

            {["first_number", "second_number", "third_number", "fourth_number"].every(f => touched[f]) && (
              <p className="text-red-600 text-center mt-2">
                {errors.first_number ||
                  errors.second_number ||
                  errors.third_number ||
                  errors.fourth_number}
              </p>
            )}
            <div className="text-sky-600 text-end mt-4">
              {timeLeft > 0 ? (
                <p className="text-gray-600">
                  Resend OTP in {formatTime(timeLeft)}
                </p>
              ) : (
                <div className="flex items-center justify-end gap-2">
                  <span className="text-gray-600">Didn't receive the code?</span>
                  <button
                    onClick={handleResendOTP}
                    disabled={resendLoading || !canResend}
                    className={`font-medium transition-colors duration-200 ${
                      resendLoading || !canResend
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-blue-600 hover:text-blue-700 hover:underline cursor-pointer"
                    }`}
                  >
                    {resendLoading ? "Sending..." : "Resend OTP"}
                  </button>
                </div>
              )}

              <p className="text-red-500 text-start text-sm mt-4">
                The code has been sent to your {isMail ? "email" : "phone"}. Depending on the network it may take up to 2 minutes. Also check the junk/spam mailbox if you do not receive it in your inbox.
              </p>
            </div>

            <button
              type="submit"
              className={`w-full bg-[#1B69B3] text-xl text-white py-2.5 rounded-md shadow-lg mt-7 max-sm:mt-5 ${
                loading ? "bg-gray-400" : "hover:bg-sky-700"
              } focus:outline-none`}
              disabled={loading}
            >
              {loading ? "Loading..." : "Submit"}
            </button>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default VerifyOTP;