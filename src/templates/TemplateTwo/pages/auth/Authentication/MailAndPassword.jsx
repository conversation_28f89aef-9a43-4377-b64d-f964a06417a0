import React, { useState } from "react";
import icon from "@/assets/images/auth/pgone emal.svg";
import lockIcon from "@/assets/images/auth/lockIcon.svg";
import { Form, Formik } from "formik";
import * as Yup from "yup";
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "react-toastify";
import Input from "@/components/form/Common/Input";
import api from "@/server/api";
import { useDispatch } from "react-redux";
import { handleLogin } from "../common/store";
import axios from "axios";

const MailAndPassword = ({ handleNextStep, loginMail, userId }) => {
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const navigate = useNavigate();
  const dispatch = useDispatch();
  //   console.log(userId)
  const location = useLocation();

  const initialValues = {
    // phone_or_email: "",
    password: "",
    id: userId,
  };

  const validationSchema = Yup.object().shape({
    password: Yup.string()
      .required("Password is required"),
  });

  const handleSubmit = async (values) => {

    try {
      setLoading(true);
      const response = await api.post(
        import.meta.env.VITE_BASE_URL + "/api/verify-password",
        values
      );
      const token = response.data.data.token;
      api.setTokenHeader(token);
      if (token) {
        let user = response.data.data;
        dispatch(handleLogin(response.data.data));
        console.log(user.user_type);
        if (user.user_type === "Mentor") {
          navigate("/mentor-dashboard", { replace: true });
        } else {
          navigate("/dashboard", { replace: true });
        }
      }
    
    } catch (error) {
      setErrorMessage(error.response.data.message);
      toast.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6 max-sm:py-16 w-[355px]">
      <p className="text-gray-500 font-semibold text-3xl max-sm:text-2xl pb-1 mb-4 border-b-2 text-center border-gray-300">
        Enter Your Password
      </p>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({handleChange, ...values }) => (
          <Form className="space-y-3">
            <p className="text-xl border-b-2 border-gray-300 pb-1">
              <img className="pr-2 pl-1" src={icon} alt="" /> {loginMail}
            </p>
            <Input
              name="password"
              id="password"
              type="password"
              placeholder="Password"
              icon={lockIcon}
              required={true}
              label="Password"
              onChange={(e) => {
                handleChange(e); 
                setErrorMessage(''); 
              }}
            />

            {errorMessage && (
              <div className="text-red-500 text-sm mt-1">
                {errorMessage}
              </div>
            )}
            
            <div className="w-full text-end">
              <span
                className="text-sm text-end cursor-pointer text-sky-500"
                onClick={() => handleNextStep("forget-password", 5)}
              >
                Forgot Password?
              </span>
            </div>
            <button
              type="submit"
              className={`w-full bg-[#1B69B3] text-xl text-white py-2.5 rounded-md shadow-lg mt-5 ${
                loading ? "bg-gray-400" : "hover:bg-sky-700"
              } focus:outline-none`}
              disabled={loading}
            >
              {loading ? "Loading..." : "Next"}
            </button>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default MailAndPassword;
