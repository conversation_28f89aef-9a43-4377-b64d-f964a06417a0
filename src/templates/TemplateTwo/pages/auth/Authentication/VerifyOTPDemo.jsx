import React, { useState } from "react";
import VerifyOTP from "./VerifyOTP";

const VerifyOTPDemo = () => {
  const [selectedScenario, setSelectedScenario] = useState("register");
  const [demoEmail, setDemoEmail] = useState("<EMAIL>");
  const [demoPhone, setDemoPhone] = useState("+8801234567890");
  const [useEmail, setUseEmail] = useState(true);

  // Mock OTP data for different scenarios
  const mockOtpData = {
    register: {
      otp_id: 123,
      expires_at: new Date(Date.now() + 7 * 60 * 1000).toISOString(), // 7 minutes from now
      used_for: "register",
      sent_to: useEmail ? demoEmail : demoPhone,
      type: useEmail ? "email" : "sms"
    },
    forgot_password: {
      otp_id: 124,
      expires_at: new Date(Date.now() + 2 * 60 * 1000).toISOString(), // 2 minutes from now
      used_for: "forgot_password",
      sent_to: useEmail ? demoEmail : demoPhone,
      type: useEmail ? "email" : "sms"
    },
    login: {
      otp_id: 125,
      expires_at: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes from now
      used_for: "login",
      sent_to: useEmail ? demoEmail : demoPhone,
      type: useEmail ? "email" : "sms"
    }
  };

  const handleNextStep = (step, data) => {
    console.log("Next step:", step, data);
    alert(`Demo: Would navigate to ${step} with data:`, JSON.stringify(data, null, 2));
  };

  const currentOtpData = mockOtpData[selectedScenario];
  const currentContact = useEmail ? demoEmail : demoPhone;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Resend OTP Demo
          </h1>
          <p className="text-gray-600">
            Test the enhanced resend OTP functionality with different scenarios.
          </p>
        </div>

        {/* Demo Controls */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Demo Configuration
          </h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            {/* Scenario Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                OTP Scenario
              </label>
              <select
                value={selectedScenario}
                onChange={(e) => setSelectedScenario(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="register">Registration (7 min expiry)</option>
                <option value="forgot_password">Forgot Password (2 min expiry)</option>
                <option value="login">Login (5 min expiry)</option>
              </select>
            </div>

            {/* Contact Method */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Contact Method
              </label>
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={useEmail}
                    onChange={() => setUseEmail(true)}
                    className="mr-2"
                  />
                  Email
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={!useEmail}
                    onChange={() => setUseEmail(false)}
                    className="mr-2"
                  />
                  Phone
                </label>
              </div>
            </div>

            {/* Contact Details */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {useEmail ? "Email Address" : "Phone Number"}
              </label>
              <input
                type={useEmail ? "email" : "tel"}
                value={useEmail ? demoEmail : demoPhone}
                onChange={(e) => useEmail ? setDemoEmail(e.target.value) : setDemoPhone(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Current OTP Data */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Current OTP Data
              </label>
              <div className="bg-gray-50 p-3 rounded-md text-sm">
                <p><strong>OTP ID:</strong> {currentOtpData.otp_id}</p>
                <p><strong>Expires:</strong> {new Date(currentOtpData.expires_at).toLocaleTimeString()}</p>
                <p><strong>Type:</strong> {currentOtpData.type}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Features Overview */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-blue-800 mb-4">
            Enhanced Resend OTP Features
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-blue-700 mb-2">API Integration:</h4>
              <ul className="text-sm text-blue-600 space-y-1">
                <li>• Uses proper /api/resend-otp endpoint</li>
                <li>• Supports register, forgot_password, login</li>
                <li>• Handles rate limiting (429 responses)</li>
                <li>• Proper error handling and validation</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-blue-700 mb-2">User Experience:</h4>
              <ul className="text-sm text-blue-600 space-y-1">
                <li>• Countdown timer until resend available</li>
                <li>• Loading states during resend operation</li>
                <li>• Clear success/error messaging</li>
                <li>• Smart button enable/disable logic</li>
              </ul>
            </div>
          </div>
        </div>

        {/* OTP Component Demo */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="bg-gray-50 px-6 py-3 border-b">
            <h3 className="text-lg font-semibold text-gray-800">
              VerifyOTP Component - {selectedScenario.replace('_', ' ').toUpperCase()}
            </h3>
            <p className="text-sm text-gray-600">
              Sent to: {currentContact} | Expires: {new Date(currentOtpData.expires_at).toLocaleTimeString()}
            </p>
          </div>
          
          <div className="p-6">
            <VerifyOTP
              handleNextStep={handleNextStep}
              otpData={currentOtpData}
              loginMail={currentContact}
              usedFor={selectedScenario}
            />
          </div>
        </div>

        {/* Testing Instructions */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 mb-4">
            Testing Instructions
          </h3>
          <div className="text-sm text-yellow-700 space-y-2">
            <p><strong>1. Timer Test:</strong> Wait for the countdown to reach zero, then try resending</p>
            <p><strong>2. Loading State:</strong> Click resend and observe the "Sending..." state</p>
            <p><strong>3. Rate Limiting:</strong> Try clicking resend multiple times quickly</p>
            <p><strong>4. Different Scenarios:</strong> Switch between register/forgot_password/login</p>
            <p><strong>5. Contact Methods:</strong> Test both email and phone number scenarios</p>
          </div>
        </div>

        {/* API Documentation Link */}
        <div className="mt-8 bg-gray-100 rounded-lg p-6 text-center">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Implementation Details
          </h3>
          <p className="text-gray-600 mb-4">
            For complete implementation details, see the documentation file.
          </p>
          <div className="text-sm text-gray-500">
            <p><strong>File:</strong> RESEND_OTP_IMPLEMENTATION.md</p>
            <p><strong>Status:</strong> ✅ Complete for both TemplateOne and TemplateTwo</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VerifyOTPDemo;
