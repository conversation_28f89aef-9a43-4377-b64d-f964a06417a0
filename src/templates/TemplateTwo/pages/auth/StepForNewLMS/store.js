import { createSlice } from "@reduxjs/toolkit";
import { toast } from "react-toastify";
// import { CLMS_URL } from "@/config";


export const organizationSlice = createSlice({
    name: "auth",

    initialState: {
        registerData: null,
        template: '',
        organizationName: '',
        shortName: '',
        logo: '',
        banner: ''
    },
    reducers: {
        setTemplate: (state, action) => {
            state.template = action.payload;
        },
        setOrganizationName: (state, action) => {
            state.organizationName = action.payload;
        },
        setShortName: (state, action) => {
            state.shortName = action.payload;
        },
        setLogo: (state, action) => {
            state.logo = action.payload;
        },
        setBanner: (state, action) => {
            state.banner = action.payload;
        },
        handleRegister: (state, action) => {
            state.registerData = action.payload;
        },

        handleOrganizationRegister: (state, action) => {
            console.log(action.payload);
        },
    },
});

export const {setTemplate,
    setOrganizationName,
    setShortName,
    setLogo,
    setBanner,
    handleRegister,
    handleOrganizationRegister } = organizationSlice.actions;
export default organizationSlice.reducer;
