import React, { useEffect, useState } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import two from '@/assets/sass-lms/2.png';
import three from '@/assets/sass-lms/3.png';
import four from '@/assets/sass-lms/4.png';
import Logo from '@/assets/logo-icon.png';
import Banner from '@/assets/upload_icon_banner.png';
import Icon from "@/components/ui/Icon";
import * as Yup from 'yup';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { setLogo, setBanner, setOrganizationName } from "@/pages/auth/StepForNewLMS/store";

const Two = ({ setPercent, createData, dataToSubmit }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const { template, organizationName, shortName } = useSelector(
    (state) => state.organizationSlice
  );

  const initialValues = {
    headline: dataToSubmit ? dataToSubmit.headline : '',
    sub_headline: dataToSubmit ? dataToSubmit.sub_headline : '',
    logo: dataToSubmit ? dataToSubmit.logo : '',
    banner: dataToSubmit ? dataToSubmit.banner : ''
  };

  const validationSchema = Yup.object().shape({
    headline: Yup.string().required('Title is required'),
    sub_headline: Yup.string().required('Sub Title is required'),
    logo: Yup.mixed().required('Upload Logo').test('is-image', 'File must be an image', function (value) {
      if (value) {
        const file = value;
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            const image = new Image();
            image.onload = () => resolve(true);
            image.onerror = () => reject(new Error('Invalid image file'));
            image.src = reader.result;
          };
          reader.readAsDataURL(file);
        });
      } else {
        return true;
      }
    }),
    banner: Yup.mixed().required('Banner is required').test('is-image', 'File must be an image', function (value) {
      if (value) {
        const file = value;
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            const image = new Image();
            image.onload = () => resolve(true);
            image.onerror = () => reject(new Error('Invalid image file'));
            image.src = reader.result;
          };
          reader.readAsDataURL(file);
        });
      } else {
        return true;
      }
    }),
  });

  const onSubmit = async (values, { setSubmitting }) => {

    createData(values);
    setPercent(75);
    // const formData = new FormData();
    // formData.append('name', values.name);
    // formData.append('email', values.email);
  };

  return (
    <div className="w-full">
      <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={onSubmit}>
        {({ values, setFieldValue, errors, touched }) => (
         
        <Form className="">
          <div className="grid grid-cols-2">
            <div className="flex items-center justify-center">
              <div>
                <div className="flex mb-5 gap-2">
                  <div className="my-auto">
                    <input type="file" accept='image/*' className="hidden" id="lms-logo" 
                    onChange={(e) => {
                      const file = e.target.files[0];
                      setFieldValue('logo', file);
                      dispatch(setLogo(file));
                    }}
                    />
                    <label>
                      <div className={`h-[50px] w-[50px] rounded-full overflow-hidden cursor-pointer ${errors.logo && touched.logo ? 'border-1 border-red-500' : ''}`}
                      onClick={() => document.getElementById('lms-logo').click()}>
                        <img src={values?.logo ? URL.createObjectURL(values.logo) :  Logo} alt="BB Logo" className='h-full w-full object-cover'/>
                      </div>
                    </label>
                    {errors.logo && touched.logo && (
                      <span className="text-red-500 text-xs">{errors.logo}</span>
                    )}
                  </div>
                  <div className="">
                    <span className="text-2xl font-bold text-pictonBlue-500 ">
                      {dataToSubmit?.name}
                    </span>
                    <p className="text-xs ">Learning Management System</p>
                  </div>
                </div>
                <div className="">
                  {/* <h1 className="text-5xl text-primary-500 tracking-wider">
                    Great LMS,
                  </h1> */}
                  <textarea
                    name='headline'
                    className="mt-10 top-0 left-0 w-full h-[150px] text-5xl text-primary-500 border-none p-0 bg-transparent outline-none"
                    spellCheck="false"
                    rows={1}
                    defaultValue={dataToSubmit?.headline}
                    style={{ resize: 'none' }}
                    placeholder='Type your headline here'
                    onChange={(e) => setFieldValue('headline', e.target.value)}
                  />

                  {errors.headline && touched.headline && (
                    <div className="text-red-500 text-xs">{errors.headline}</div>
                  )}
                  
                </div>
                {/* <h1 className="text-5xl mb-6 text-primary-500 tracking-wider">
                  Great training
                </h1> */}
                <textarea
                  defaultValue={dataToSubmit?.sub_headline}
                  name='sub_headline'
                  className={`top-0 left-0  h-[150px] w-full text-xl p-0 bg-transparent outline-none ${errors.sub_headline ? 'border-1 border-red-500' : ''}`}
                  spellCheck="false"
                  rows={1}
                  style={{ resize: 'none' }}
                  placeholder='Write your short description here...'
                  onChange={(e) => setFieldValue('sub_headline', e.target.value)}
                />
                {errors.sub_headline && touched.sub_headline && (
                  <div className="text-red-500 text-xs">{errors.sub_headline}</div>
                )}

                  {/* {errors.sub_headline && touched.sub_headline && (
                    <div className="text-red-500 text-xs">{errors.sub_headline}</div>
                  )} */}
                  
                {/* <button className="bg-warning-600 text-white py-2 px-4 rounded-full hover:bg-primary-700 transition duration-300 shadow-lg transform hover:scale-105 me-3">
                  Learn With US
                </button> */}
              </div>
            </div>
            <div>
              <div className="grid grid-cols-3 gap-4">
                    <input type="file" accept='image/*' className="hidden" id="lms-banner" 
                    onChange={(e) => {
                      const file = e.target.files[0];
                      setFieldValue('banner', file);
                      dispatch(setBanner(file));
                    }}
                    />
                <div
                  className="relative col-span-2"
                  onMouseEnter={() =>
                    document.getElementById("upload-icon").classList.add("show")
                  }
                  onMouseLeave={() =>
                    document.getElementById("upload-icon").classList.remove("show")
                  }
                >
                  <img
                    src={values?.banner ? URL.createObjectURL(values.banner) : Banner} 
                    alt="Hero Background"
                    className="w-full h-[250px] object-cover rounded shadow cursor-pointer"
                    onClick={() => document.getElementById('lms-banner').click()}
                  />
                  {errors.banner && touched.banner && (
                    <div className="text-red-500 text-xs">{errors.banner}</div>
                  )}
                  <div id="upload-icon" className="absolute top-2 right-2 hidden">
                    <Icon icon="heroicons:arrow-up-tray" className="w-6 h-6 text-gray-500" />
                  </div>
                </div>
                <div className="flex flex-col items-center justify-center bg-[#FFDCBA] p-4 rounded-lg">
                  <img
                    src={two}
                    alt="Hero Background"
                    className="w-[100px] object-cover rounded "
                  />
                  <h4 className="mt-4">0</h4>
                  <p>Verified Mentors </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 mt-4 ">
                <div className="flex bg-[#D3EAFF] rounded-lg p-4 items-center justify-center gap-4 ">
                  <img
                    src={three}
                    alt="Hero Background"
                    className="w-[100px] object-cover"
                  />
                  <svg
                    width="10"
                    height="66"
                    viewBox="0 0 10 66"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <pathEnrolled
                      d="M1 1V65.3479"
                      stroke="#5C6777"
                      strokeLinecap="round"
                    />
                    <path
                      d="M5.05933 14V52.6087"
                      stroke="#5C6777"
                      strokeLinecap="round"
                    />
                    <path
                      d="M8.72168 17V49.1739"
                      stroke="#5C6777"
                      strokeLinecap="round"
                    />
                  </svg>

                  <div>
                    <h4 className="mt-4">0</h4>
                    <p>Enrolled Student </p>
                  </div>
                </div>
                <div className="flex bg-[#DEFDF3] rounded-lg p-4 items-center justify-center gap-4 ">
                  <img
                    src={four}
                    alt="Hero Background"
                    className="w-[100px] object-cover"
                  />
                  <svg
                    width="10"
                    height="66"
                    viewBox="0 0 10 66"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M1 1V65.3479"
                      stroke="#5C6777"
                      strokeLinecap="round"
                    />
                    <path
                      d="M5.05933 14V52.6087"
                      stroke="#5C6777"
                      strokeLinecap="round"
                    />
                    <path
                      d="M8.72168 17V49.1739"
                      stroke="#5C6777"
                      strokeLinecap="round"
                      Standard />
                  </svg>

                  <div>
                    <h4 className="mt-4">0</h4>
                    <p>Course Available </p>
                  </div>
                </div>
              </div>
            </div>


         
          </div>  
           <div className="w-full mt-4 flex justify-end">
                
                <button type="button" className="flex items-center px-4 py-2 text-gray-800 rounded-lg bg-gray-100 hover:bg-gray-200" 
                onClick={() => setPercent(25)}>
                  <Icon icon="akar-icons:chevron-left" className="mr-2" />
                  Back
                </button>
            
            <div className="w-4"></div>
              <button type='submit' className="flex items-center px-4 py-2 text-white rounded-lg bg-blue-500 hover:bg-blue-600">
            
                Next 
                <Icon icon="akar-icons:chevron-right" className="ml-2" />
              </button>
              </div>
        </Form> 
        )}
      </Formik>
    </div>
  );
};

export default Two;

