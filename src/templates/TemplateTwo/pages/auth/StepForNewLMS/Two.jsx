import React from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { useDispatch } from 'react-redux';
import { setLogo, setBanner } from '@/pages/auth/StepForNewLMS/store';
import Icon from "@/components/ui/Icon";

const Two = ({ setPercent, createData, dataToSubmit }) => {
  const dispatch = useDispatch();

  const initialValues = {
    headline: dataToSubmit?.headline || '',
    sub_headline: dataToSubmit?.sub_headline || '',
    logo: dataToSubmit?.logo || '',
    banner: dataToSubmit?.banner || '',
  };

  const validationSchema = Yup.object().shape({
    headline: Yup.string().required('Headline is required'),
    sub_headline: Yup.string().required('Sub Headline is required'),
    logo: Yup.mixed().required('Upload Logo').test('is-image', 'File must be an image', (value) => {
      if (!value) return true;
      return value?.type.startsWith('image/');
    }),
    banner: Yup.mixed().required('Banner is required').test('is-image', 'File must be an image', (value) => {
      if (!value) return true;
      return value?.type.startsWith('image/');
    }),
  });

  const onSubmit = (values) => {
    createData(values);
    setPercent(75);
  };

  return (
    <div className="w-full h-full p-4 relative">
      {/* Background Shapes */}
      <div className="absolute top-0 left-0 w-full h-full z-0">
        {/* Curved Lines */}
        <div className="absolute top-1/4 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-transparent transform rotate-45 origin-left opacity-30"></div>
        <div className="absolute bottom-1/4 right-0 w-full h-1 bg-gradient-to-l from-green-500 to-transparent transform rotate-45 origin-right opacity-30"></div>
        <div className="absolute top-1/2 left-1/4 w-40 h-40 bg-gradient-to-b from-purple-500 to-transparent transform rotate-45 origin-bottom-left opacity-20"></div>
        <div className="absolute top-3/4 left-3/4 w-48 h-48 bg-gradient-to-tl from-yellow-400 to-transparent transform rotate-45 origin-bottom-right opacity-25"></div>
      </div>

      {/* Main content */}
      <div className="relative z-10">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-800">Customize Your LMS</h2>
          <p className="text-gray-600">Add your logo, headline, and banner to make your LMS stand out.</p>
        </div>
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={onSubmit}
        >
          {({ setFieldValue, values, errors, touched }) => (
            <Form className="flex flex-col items-center space-y-8">
              {/* Logo Upload */}
              <div className="relative w-full md:w-1/2 lg:w-1/2 mx-auto">
                <label htmlFor="logo" className="cursor-pointer w-24 h-24 flex justify-center items-center border rounded-full hover:ring-2 hover:ring-blue-400">
                  {values.logo ? (
                    <img
                      src={URL.createObjectURL(values.logo)}
                      alt="Logo Preview"
                      className="object-cover w-full h-full rounded-full"
                    />
                  ) : (
                    <Icon icon="akar-icons:image" className="w-12 h-12 text-gray-600" />
                  )}
                </label>
                <input
                  type="file"
                  id="logo"
                  name="logo"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files[0];
                    setFieldValue('logo', file);
                  }}
                />
                <ErrorMessage name="logo" component="div" className="text-red-500 text-sm mt-2 text-center" />
              </div>

              {/* Headline */}
              <div className="relative w-full md:w-1/2 lg:w-1/2 mx-auto">
                <Field
                  id="headline"
                  name="headline"
                  type="text"
                  className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`}
                  placeholder=" "
                />
                <label
                  htmlFor="headline"
                  className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                >
                  Headline
                </label>
                <ErrorMessage name="headline" component="div" className="text-red-500 text-sm mt-2" />
              </div>

              {/* Sub-Headline */}
              <div className="relative w-full md:w-1/2 lg:w-1/2 mx-auto mt-10">
                <Field
                  id="sub_headline"
                  name="sub_headline"
                  as="textarea"
                  rows="4"
                  className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`}
                  placeholder=" "
                />
                <label
                  htmlFor="sub_headline"
                  className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                >
                  Sub Headline
                </label>
                <ErrorMessage name="sub_headline" component="div" className="text-red-500 text-sm mt-2" />
              </div>

              {/* Banner Upload */}
              <div className="relative w-full md:w-1/2 lg:w-1/2 mx-auto mt-10">
                <label htmlFor="banner" className="cursor-pointer w-full h-32 flex justify-center items-center border rounded-lg hover:ring-2 hover:ring-blue-400">
                  {values.banner ? (
                    <img
                      src={URL.createObjectURL(values.banner)}
                      alt="Banner Preview"
                      className="object-cover w-full h-full"
                    />
                  ) : (
                    <Icon icon="akar-icons:image" className="w-12 h-12 text-gray-600" />
                  )}
                </label>
                <input
                  type="file"
                  id="banner"
                  name="banner"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files[0];
                    setFieldValue('banner', file);
                    dispatch(setBanner(file));
                  }}
                />
                <ErrorMessage name="banner" component="div" className="text-red-500 text-sm mt-2 text-center" />
              </div>

              {/* Buttons */}
              <div className="mt-6 flex justify-between w-full md:w-1/2 lg:w-1/2 mx-auto">
                <button
                  type="button"
                  className="flex items-center px-5 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300"
                  onClick={() => setPercent(25)}
                >
                  <Icon icon="akar-icons:chevron-left" className="mr-2" />
                  Back
                </button>
                <button
                  type="submit"
                  className="flex items-center px-5 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
                >
                  Next
                  <Icon icon="akar-icons:chevron-right" className="ml-2" />
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default Two;

