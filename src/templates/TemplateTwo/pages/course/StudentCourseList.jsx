import React from "react";
import useFetch from "@/hooks/useFetch";
import { ASSET_URL } from "@/config";
import { useNavigate, useLocation } from "react-router-dom";
import GoBack from "@/components/ui/GoBack";
import Icon from "@/components/ui/Icon";

const CourseCard = ({ course }) => {
  const navigate = useNavigate();

  return (
    <div className="flex items-center p-4 bg-white border rounded-lg shadow-md gap-4">
      {/* Thumbnail */}
      <img
        src={`${ASSET_URL}${course.thumbnail}`}
        alt={course.title}
        className="w-24 h-24 object-cover rounded-md"
      />
      {/* Course Info */}
      <div className="flex-grow">
        <h3 className="text-lg font-semibold text-gray-800">{course.title}</h3>
        <div className="flex items-center gap-2 mt-2">
          <span className="text-sm text-gray-600">Progress:</span>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-green-500 h-2.5 rounded-full"
              style={{ width: `${course.progress}%` }}
            ></div>
          </div>
          <span className="text-sm text-gray-600">
            {course.progress.toFixed(1)}%
          </span>
        </div>
      </div>
      {/* Start Now Button */}
      <button
        className="flex bg-blue-500 text-white px-4 py-2 rounded-md shadow hover:bg-blue-600 items-center justify-center gap-2"
        onClick={() => navigate(`/course-details/${course.id}`)}
      >
        <span className="sm:hidden">Go</span>
        <span className="hidden sm:block">Start Now</span>
        <Icon icon="mdi:arrow-right" />
      </button>
    </div>
  );
};

const EmptyState = ({ pathname }) => {
  const messages = {
    "/completed-courses": {
      title: "No Completed Courses",
      description: "You haven't completed any courses yet. Keep learning!",
      icon: "mdi:check-circle-outline"
    },
    "/ongoing-courses": {
      title: "No Ongoing Courses",
      description: "You don't have any courses in progress. Explore our catalog to get started!",
      icon: "mdi:book-open-outline"
    },
    default: {
      title: "No Courses Enrolled",
      description: "You haven't enrolled in any courses yet. Browse our offerings to begin your learning journey.",
      icon: "mdi:school-outline"
    }
  };

  const { title, description, icon } = messages[pathname] || messages.default;

  return (
    <div className="min-h-[450px] flex flex-col items-center justify-center text-center p-6">
      <div className="bg-blue-100 p-5 rounded-full mb-4">
        <Icon icon={icon} className="text-blue-500 text-4xl" />
      </div>
      <h3 className="text-xl font-semibold text-gray-800 mb-2">{title}</h3>
      <p className="text-gray-600 max-w-md mb-6">{description}</p>
      <button
        className="flex bg-blue-500 text-white px-6 py-2 rounded-md shadow hover:bg-blue-600 items-center justify-center gap-2"
        onClick={() => window.location.href = '/courses'}
      >
        <span>Browse Courses</span>
        <Icon icon="mdi:arrow-right" />
      </button>
    </div>
  );
};

const StudentCourseList = () => {
  const {
    data: response,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `my-course-list`,
    endPoint: `my-course-list`,
  });

  const location = useLocation();
  const courses = response?.data || [];

  // Filter courses based on pathname
  const filteredCourses = location.pathname === "/completed-courses"
    ? courses.filter(course => course.progress >= 100)
    : location.pathname === "/ongoing-courses"
    ? courses.filter(course => course.progress < 100)
    : courses;

  if (isLoading) {
    return (
      <div className="min-h-[450px] flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-purple-500"></div>
        <p>Loading...</p>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="min-h-[450px] flex flex-col items-center justify-center">
        <div className="bg-red-100 p-5 rounded-full mb-4">
          <Icon icon="mdi:alert-circle-outline" className="text-red-500 text-4xl" />
        </div>
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Error Loading Courses</h3>
        <p className="text-gray-600 max-w-md mb-6">We couldn't load your courses. Please try again later.</p>
      </div>
    );
  }

  return (
    <div className="container py-5 xl:py-10">
      <GoBack
        title={
          location.pathname === "/completed-courses"
            ? "Completed Courses"
            : location.pathname === "/ongoing-courses"
            ? "Ongoing Courses"
            : "My Courses"
        }
      />
      
      {filteredCourses.length > 0 ? (
        <div className="flex flex-col gap-6 mt-6">
          {filteredCourses.map((course) => (
            <CourseCard key={course.id} course={course} />
          ))}
        </div>
      ) : (
        <EmptyState pathname={location.pathname} />
      )}
    </div>
  );
};

export default StudentCourseList;