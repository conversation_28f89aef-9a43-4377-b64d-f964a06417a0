import React, { useState } from "react";
import CourseRating from "./CourseRating";

const CourseRatingDemo = () => {
  // Sample data based on your provided structure
  const [sampleRatings, setSampleRatings] = useState([
    {
      "id": 2,
      "rating": 5,
      "review": "Wow! excellent course. I am recommending to everyone who are seeing my review. Please enroll this course.",
      "user": {
        "id": 7,
        "name": "Tushar Imran",
        "image": null
      }
    },
    {
      "id": 8,
      "rating": 5,
      "review": "Very nice course",
      "user": {
        "id": 25,
        "name": "Mehed<PERSON>",
        "image": "image/bb_1750054390.jpeg"
      }
    },
    {
      "id": 1,
      "rating": 4,
      "review": "This is a very effective course for me. I have learnt a lot from this course.",
      "user": {
        "id": 8,
        "name": null,
        "image": null
      }
    }
  ]);

  const sampleCourse = {
    id: 1,
    title: "React Development Masterclass",
    description: "Learn React from basics to advanced concepts"
  };

  const handleRatingSubmitted = () => {
    // In a real application, you would refetch the ratings from the API
    console.log("Rating submitted! The new rating will appear at the top of the list automatically.");

    // Note: The component now handles adding the new rating to the list automatically
    // This callback is mainly for additional actions like analytics, notifications, etc.
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Course Rating Component Demo
          </h1>
          <p className="text-gray-600">
            This demo shows the enhanced CourseRating component with real data structure.
          </p>
        </div>

        {/* Course Info */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            {sampleCourse.title}
          </h2>
          <p className="text-gray-600">
            {sampleCourse.description}
          </p>
        </div>

        {/* Rating Component */}
        <CourseRating
          ratings={sampleRatings}
          course={sampleCourse}
          onRatingSubmitted={handleRatingSubmitted}
        />

        {/* Features List */}
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Enhanced Features
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-700">Visual Enhancements:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Rating statistics with average and distribution</li>
                <li>• User avatars with fallback icons</li>
                <li>• Hover effects on star ratings</li>
                <li>• Gradient backgrounds and modern styling</li>
                <li>• Character counter for reviews</li>
                <li>• <strong>Modal with smooth transitions and blur</strong></li>
                <li>• <strong>Instant rating display after submission</strong></li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-700">Functionality:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Form validation with error messages</li>
                <li>• Show more/less reviews functionality</li>
                <li>• Loading states and success feedback</li>
                <li>• Empty state handling</li>
                <li>• Callback for rating submission</li>
                <li>• <strong>ESC key and click-outside to close modal</strong></li>
                <li>• <strong>New ratings appear instantly at top of list</strong></li>
              </ul>
            </div>
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-4">
            How to Use
          </h3>
          <div className="text-sm text-blue-700 space-y-2">
            <p><strong>Props:</strong></p>
            <ul className="ml-4 space-y-1">
              <li>• <code className="bg-blue-100 px-1 rounded">ratings</code> - Array of rating objects</li>
              <li>• <code className="bg-blue-100 px-1 rounded">course</code> - Course object with id</li>
              <li>• <code className="bg-blue-100 px-1 rounded">onRatingSubmitted</code> - Callback function (optional)</li>
            </ul>
            <p className="mt-3"><strong>Rating Object Structure:</strong></p>
            <pre className="bg-blue-100 p-2 rounded text-xs overflow-x-auto">
{`{
  "id": 1,
  "rating": 5,
  "review": "Great course!",
  "user": {
    "id": 1,
    "name": "John Doe",
    "image": "path/to/image.jpg"
  }
}`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseRatingDemo;
