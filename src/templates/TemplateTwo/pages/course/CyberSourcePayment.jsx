import React, { useEffect, useRef, useState } from 'react';
import useFetch from '@/hooks/useFetch';

const CyberSourcePaymentPage = () => {
  const [flexInitialized, setFlexInitialized] = useState(false);
  const [captureContext, setCaptureContext] = useState(null);
  const [error, setError] = useState('');
  const [tokenResult, setTokenResult] = useState(null);
  const microformInstanceRef = useRef(null);
  const cardNumberRef = useRef(null);
  const securityCodeRef = useRef(null);

  const { data: cybersourceToken, isLoading: tokenIsLoading } = useFetch({
    queryKey: 'cybersource/token',
    endPoint: 'cybersource/token',
  });

  const clientSecret = cybersourceToken?.data?.captureContext;

  const loadFlexScript = () => {
    return new Promise((resolve, reject) => {
      if (window.Flex) return resolve();

      const existingScript = document.querySelector(
        'script[src="https://flex.cybersource.com/cybersource/assets/microform/0.4.0/flex-microform.min.js"]'
      );
      if (existingScript) {
        existingScript.addEventListener('load', resolve);
        existingScript.addEventListener('error', reject);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://flex.cybersource.com/cybersource/assets/microform/0.4.0/flex-microform.min.js';
      script.crossOrigin = 'anonymous';
      script.async = true;
      script.onload = resolve;
      script.onerror = () => reject(new Error('Failed to load CyberSource Flex Microform script.'));
      document.head.appendChild(script);
    });
  };

  const initializeFlex = () => {
    if (!window.Flex || !captureContext) {
      setError('CyberSource Flex or capture context is not available.');
      return;
    }

    const flex = new window.Flex({ captureContext });
    const microform = flex.microform({
      styles: {
        input: {
          fontSize: '16px',
          padding: '10px',
          border: '1px solid #ccc',
          borderRadius: '4px',
        },
      },
    });
    microformInstanceRef.current = microform;

    const numberField = microform.createField('number', { placeholder: 'Card Number' });
    const securityCodeField = microform.createField('securityCode', { placeholder: 'CVV' });

    numberField.load('#card-number-container');
    securityCodeField.load('#security-code-container');

    cardNumberRef.current = numberField;
    securityCodeRef.current = securityCodeField;

    setFlexInitialized(true);
  };

  useEffect(() => {
    if (clientSecret && !flexInitialized) {
      setCaptureContext(clientSecret);

      loadFlexScript()
        .then(() => {
          setTimeout(() => {
            if (window.Flex) initializeFlex();
            else setError('CyberSource Flex is not available after load.');
          }, 1500);
        })
        .catch((err) => {
          console.error(err);
          setError('Failed to load CyberSource Flex library.');
        });
    }
  }, [clientSecret, flexInitialized]);

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');
    setTokenResult(null);

    const microform = microformInstanceRef.current;
    if (!microform) {
      setError('Microform is not initialized.');
      return;
    }

    const options = {
      cardExpirationMonth: document.getElementById('expMonth').value,
      cardExpirationYear: document.getElementById('expYear').value,
    };

    microform.createToken(options, (err, token) => {
      if (err) {
        console.error('Tokenization Error:', err);
        setError('Failed to tokenize card. Please try again.');
      } else {
        console.log('Token:', token);
        setTokenResult(token);
        // TODO: send token.token to your backend to process payment
      }
    });
  };

  return (
    <div style={{ padding: '2rem', maxWidth: '500px', margin: '0 auto' }}>
      <h2>CyberSource Payment</h2>

      {error && <p style={{ color: 'red' }}>{error}</p>}
      {!flexInitialized && <p>Loading payment form...</p>}

      <form onSubmit={handleSubmit}>
        <div>
          <label>Card Number</label>
          <div id="card-number-container" style={{ height: '40px', marginBottom: '10px' }} />
        </div>
        <div>
          <label>CVV</label>
          <div id="security-code-container" style={{ height: '40px', marginBottom: '10px' }} />
        </div>
        <div>
          <label>Expiry Month</label>
          <input id="expMonth" type="text" placeholder="MM" required style={{ marginBottom: '10px' }} />
        </div>
        <div>
          <label>Expiry Year</label>
          <input id="expYear" type="text" placeholder="YYYY" required style={{ marginBottom: '10px' }} />
        </div>

        <button type="submit" disabled={!flexInitialized}>
          Submit Payment
        </button>
      </form>

      {tokenResult && (
        <div style={{ marginTop: '20px' }}>
          <h4>Tokenization Result</h4>
          <pre>{JSON.stringify(tokenResult, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};

export default CyberSourcePaymentPage;
