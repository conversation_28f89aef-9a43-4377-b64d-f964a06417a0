import React, { useState, useMemo } from "react";
import api from "@/server/api";
import { toast } from "react-toastify";
import { FaStar, FaUser, FaQuoteLeft } from "react-icons/fa";
import { ASSET_URL } from "@/config";

const CourseRating = ({ ratings = [], course, onRatingSubmitted }) => {
  const [rating, setRating] = useState(0);
  const [review, setReview] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [showAllReviews, setShowAllReviews] = useState(false);

  // Calculate rating statistics
  const ratingStats = useMemo(() => {
    if (!ratings || ratings.length === 0) {
      return {
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: [0, 0, 0, 0, 0]
      };
    }

    const totalReviews = ratings.length;
    const totalRating = ratings.reduce((sum, r) => sum + r.rating, 0);
    const averageRating = totalRating / totalReviews;

    const ratingDistribution = [0, 0, 0, 0, 0];
    ratings.forEach(r => {
      if (r.rating >= 1 && r.rating <= 5) {
        ratingDistribution[r.rating - 1]++;
      }
    });

    return {
      averageRating: Math.round(averageRating * 10) / 10,
      totalReviews,
      ratingDistribution
    };
  }, [ratings]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (rating === 0) {
      toast.error("Please select a rating");
      return;
    }

    if (!review.trim()) {
      toast.error("Please write a review");
      return;
    }

    setIsSubmitting(true);

    try {
      await api.post(`course-rating`, {
        course_id: course.id,
        rating,
        review: review.trim(),
      });

      toast.success("Review submitted successfully!");
      setRating(0);
      setReview("");

      // Call callback to refresh ratings
      if (onRatingSubmitted) {
        onRatingSubmitted();
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to submit review");
    } finally {
      setIsSubmitting(false);
    }
  };

  const displayedReviews = showAllReviews ? ratings : ratings.slice(0, 3);

  return (
    <div className="bg-white shadow-lg rounded-xl overflow-hidden">
      {/* Rating Statistics Header */}
      {ratings.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-800">
                  {ratingStats.averageRating}
                </div>
                <div className="flex items-center justify-center space-x-1 mt-1">
                  {[1, 2, 3, 4, 5].map((num) => (
                    <FaStar
                      key={num}
                      className={`text-lg ${
                        num <= Math.round(ratingStats.averageRating)
                          ? "text-yellow-400"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {ratingStats.totalReviews} review{ratingStats.totalReviews !== 1 ? 's' : ''}
                </div>
              </div>
            </div>

            {/* Rating Distribution */}
            <div className="flex-1 max-w-xs ml-8">
              {[5, 4, 3, 2, 1].map((star) => (
                <div key={star} className="flex items-center space-x-2 mb-1">
                  <span className="text-sm text-gray-600 w-3">{star}</span>
                  <FaStar className="text-yellow-400 text-xs" />
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${ratingStats.totalReviews > 0
                          ? (ratingStats.ratingDistribution[star - 1] / ratingStats.totalReviews) * 100
                          : 0}%`
                      }}
                    />
                  </div>
                  <span className="text-sm text-gray-600 w-8">
                    {ratingStats.ratingDistribution[star - 1]}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Rating Form */}
      <div className="p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-6 flex items-center">
          <FaQuoteLeft className="text-blue-500 mr-2" />
          Rate This Course
        </h2>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Star Rating */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Your Rating *
            </label>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((num) => (
                <FaStar
                  key={num}
                  className={`cursor-pointer text-2xl transition-colors duration-200 ${
                    (hoveredRating || rating) >= num
                      ? "text-yellow-400 hover:text-yellow-500"
                      : "text-gray-300 hover:text-gray-400"
                  }`}
                  onClick={() => setRating(num)}
                  onMouseEnter={() => setHoveredRating(num)}
                  onMouseLeave={() => setHoveredRating(0)}
                />
              ))}
              {(rating > 0 || hoveredRating > 0) && (
                <span className="ml-3 text-sm text-gray-600">
                  {hoveredRating || rating} out of 5 stars
                </span>
              )}
            </div>
          </div>

          {/* Review Input */}
          <div className="space-y-2">
            <label htmlFor="review" className="block text-sm font-medium text-gray-700">
              Your Review *
            </label>
            <textarea
              id="review"
              value={review}
              onChange={(e) => setReview(e.target.value)}
              className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 resize-none"
              placeholder="Share your experience with this course. What did you like? What could be improved?"
              rows={4}
              maxLength={500}
            />
            <div className="text-right text-xs text-gray-500">
              {review.length}/500 characters
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting || rating === 0 || !review.trim()}
            className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] disabled:scale-100 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Submitting...</span>
              </>
            ) : (
              <>
                <FaStar className="text-yellow-300" />
                <span>Submit Review</span>
              </>
            )}
          </button>
        </form>
      </div>

      {/* Reviews List */}
      {ratings.length > 0 && (
        <div className="border-t bg-gray-50">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Student Reviews ({ratingStats.totalReviews})
            </h3>

            <div className="space-y-4">
              {displayedReviews.map((r) => (
                <div key={r.id} className="bg-white p-5 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-start space-x-4">
                    {/* User Avatar */}
                    <div className="flex-shrink-0">
                      {r.user?.image ? (
                        <img
                          src={`${ASSET_URL}${r.user.image}`}
                          alt={r.user.name || "User"}
                          className="w-12 h-12 rounded-full object-cover border-2 border-gray-200"
                        />
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                          <FaUser className="text-white text-lg" />
                        </div>
                      )}
                    </div>

                    {/* Review Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h4 className="text-sm font-semibold text-gray-800">
                            {r.user?.name || "Anonymous User"}
                          </h4>
                          <div className="flex items-center space-x-1 mt-1">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <FaStar
                                key={star}
                                className={`text-sm ${
                                  star <= r.rating ? "text-yellow-400" : "text-gray-300"
                                }`}
                              />
                            ))}
                            <span className="text-xs text-gray-500 ml-2">
                              {r.rating}/5
                            </span>
                          </div>
                        </div>
                      </div>

                      <p className="text-gray-700 text-sm leading-relaxed">
                        {r.review || "No review text provided."}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Show More/Less Button */}
            {ratings.length > 3 && (
              <div className="text-center mt-6">
                <button
                  onClick={() => setShowAllReviews(!showAllReviews)}
                  className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors duration-200"
                >
                  {showAllReviews
                    ? `Show Less Reviews`
                    : `Show All ${ratings.length} Reviews`
                  }
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Empty State */}
      {ratings.length === 0 && (
        <div className="p-8 text-center border-t">
          <div className="text-gray-400 mb-2">
            <FaStar className="text-4xl mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-600 mb-2">
            No Reviews Yet
          </h3>
          <p className="text-gray-500 text-sm">
            Be the first to share your experience with this course!
          </p>
        </div>
      )}
    </div>
  );
};

export default CourseRating;
