import React, { useState, useMemo, useEffect } from "react";
import api from "@/server/api";
import { toast } from "react-toastify";
import { FaStar, FaUser, FaQuoteLeft, FaTimes, FaEdit } from "react-icons/fa";
import { ASSET_URL } from "@/config";
import { useSelector } from "react-redux";

const CourseRating = ({ ratings = [], course, onRatingSubmitted }) => {
  const [rating, setRating] = useState(0);
  const [review, setReview] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [showAllReviews, setShowAllReviews] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [localRatings, setLocalRatings] = useState(ratings);


  const {isAuth: user} = useSelector((state) => state.auth);
  console.log(user);
  

  // Sync localRatings with props
  useEffect(() => {
    setLocalRatings(ratings);
  }, [ratings]);

  // Calculate rating statistics
  const ratingStats = useMemo(() => {
    if (!localRatings || localRatings.length === 0) {
      return {
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: [0, 0, 0, 0, 0]
      };
    }

    const totalReviews = localRatings.length;
    const totalRating = localRatings.reduce((sum, r) => sum + r.rating, 0);
    const averageRating = totalRating / totalReviews;

    const ratingDistribution = [0, 0, 0, 0, 0];
    localRatings.forEach(r => {
      if (r.rating >= 1 && r.rating <= 5) {
        ratingDistribution[r.rating - 1]++;
      }
    });

    return {
      averageRating: Math.round(averageRating * 10) / 10,
      totalReviews,
      ratingDistribution
    };
  }, [localRatings]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (rating === 0) {
      toast.error("Please select a rating");
      return;
    }

    if (!review.trim()) {
      toast.error("Please write a review");
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await api.post(`course-rating`, {
        course_id: course.id,
        rating,
        review: review.trim(),
      });

      // Create new rating object to add to the list
      const newRating = {
        id: response.data?.data?.id || Date.now(), // Use API response ID or fallback
        rating,
        review: review.trim(),
        user: {
          id: response.data?.data?.user?.id || 'current_user',
          name: response.data?.data?.user?.name || 'You',
          image: response.data?.data?.user?.image || null
        }
      };

      // Add new rating to the beginning of the list
      setLocalRatings(prev => [newRating, ...prev]);

      toast.success("Review submitted successfully!");
      setRating(0);
      setReview("");
      setIsModalOpen(false);

      // Call callback to refresh ratings
      if (onRatingSubmitted) {
        onRatingSubmitted();
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to submit review");
    } finally {
      setIsSubmitting(false);
    }
  };

  const openModal = () => {
    setIsModalOpen(true);
    setRating(0);
    setReview("");
    setHoveredRating(0);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setRating(0);
    setReview("");
    setHoveredRating(0);
  };

  const displayedReviews = showAllReviews ? localRatings : localRatings.slice(0, 3);

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape' && isModalOpen) {
        closeModal();
      }
    };

    if (isModalOpen) {
      document.addEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'unset';
    };
  }, [isModalOpen]);

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
      {/* Rating Statistics Header */}
      {localRatings.length > 0 && (
        <div className="bg-gray-50 p-6 border-b border-gray-200">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-6">
            <div className="flex items-center space-x-4">
              <div className="text-center">
                <div className="text-4xl font-bold text-gray-900">
                  {ratingStats.averageRating}
                  <span className="text-xl text-gray-500">/5</span>
                </div>
                <div className="flex items-center justify-center space-x-1 mt-1">
                  {[1, 2, 3, 4, 5].map((num) => (
                    <FaStar
                      key={num}
                      className={`text-lg ${
                        num <= Math.round(ratingStats.averageRating)
                          ? "text-yellow-500"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Based on {ratingStats.totalReviews} review{ratingStats.totalReviews !== 1 ? 's' : ''}
                </div>
              </div>
            </div>

            {/* Rating Distribution */}
            <div className="flex-1 max-w-md w-full">
              {[5, 4, 3, 2, 1].map((star) => (
                <div key={star} className="flex items-center gap-2 mb-2">
                  <span className="text-sm font-medium text-gray-700 w-4">{star}</span>
                  <FaStar className="text-yellow-500 text-xs" />
                  <div className="flex-1 bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-yellow-500 h-2.5 rounded-full"
                      style={{
                        width: `${ratingStats.totalReviews > 0
                          ? (ratingStats.ratingDistribution[star - 1] / ratingStats.totalReviews) * 100
                          : 0}%`
                      }}
                    />
                  </div>
                  <span className="text-sm text-gray-600 w-8 text-right">
                    {ratingStats.ratingDistribution[star - 1]}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Write Review Button */}
      <div className="p-4 border-b border-gray-200">
        <button
          onClick={openModal}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-md transition-colors duration-200 flex items-center justify-center gap-2"
        >
          <FaEdit />
          <span>Write a Review</span>
        </button>
      </div>

      {/* Reviews List */}
      {localRatings.length > 0 ? (
        <div className="divide-y divide-gray-200">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Student Reviews
            </h3>

            <div className="space-y-6">
              {displayedReviews.map((r) => (
                <div key={r.id} className="first:pt-0">
                  <div className="flex items-start gap-2 bg-gray-50 p-4 rounded-lg">
                    {/* User Avatar */}
                    <div className="flex-shrink-0">
                      {r.user?.image ? (
                        <img
                          src={`${ASSET_URL}${r.user.image}`}
                          alt={r.user.name || "User"}
                          className="w-10 h-10 rounded-full object-cover border border-gray-200"
                        />
                      ) : (
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                          <FaUser className="text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Review Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
                        <h4 className="text-sm font-medium text-gray-900">
                          {r.user?.name || "Anonymous User"}
                        </h4>
                        <div className="flex items-center gap-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <FaStar
                              key={star}
                              className={`text-sm ${
                                star <= r.rating ? "text-yellow-500" : "text-gray-300"
                              }`}
                            />
                          ))}
                        </div>
                      </div>

                      <p className="text-gray-700 text-sm leading-relaxed">
                        {r.review || "No review text provided."}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Show More/Less Button */}
            {localRatings.length > 3 && (
              <div className="mt-6">
                <button
                  onClick={() => setShowAllReviews(!showAllReviews)}
                  className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors duration-200"
                >
                  {showAllReviews
                    ? `Show fewer reviews`
                    : `View all ${localRatings.length} reviews`
                  }
                </button>
              </div>
            )}
          </div>
        </div>
      ) : (
        /* Empty State */
        <div className="p-8 text-center">
          <div className="text-gray-400 mb-3">
            <FaStar className="text-4xl mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-700 mb-1">
            No reviews yet
          </h3>
          <p className="text-gray-500 text-sm">
            Be the first to share your thoughts about this course
          </p>
          <button
            onClick={openModal}
            className="mt-4 text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors duration-200"
          >
            Write a review
          </button>
        </div>
      )}

      {/* Rating Modal */}
      {isModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 backdrop-blur-md flex items-center justify-center z-50 p-4 animate-fadeIn"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              closeModal();
            }
          }}
        >
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto animate-slideUp">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <FaQuoteLeft className="text-blue-600" />
                <span>Rate this course</span>
              </h2>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <FaTimes />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Star Rating */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your rating <span className="text-red-500">*</span>
                  </label>
                  <div className="flex items-center gap-2">
                    {[1, 2, 3, 4, 5].map((num) => (
                      <button
                        type="button"
                        key={num}
                        className={`p-1 rounded-md transition-colors duration-200 ${
                          (hoveredRating || rating) >= num
                            ? "text-yellow-500 hover:text-yellow-600"
                            : "text-gray-300 hover:text-gray-400"
                        }`}
                        onClick={() => setRating(num)}
                        onMouseEnter={() => setHoveredRating(num)}
                        onMouseLeave={() => setHoveredRating(0)}
                      >
                        <FaStar className="text-2xl" />
                      </button>
                    ))}
                    {(rating > 0 || hoveredRating > 0) && (
                      <span className="ml-2 text-sm font-medium text-gray-700">
                        {hoveredRating || rating} star{hoveredRating !== 1 || rating !== 1 ? 's' : ''}
                      </span>
                    )}
                  </div>
                </div>

                {/* Review Input */}
                <div className="space-y-2">
                  <label htmlFor="review" className="block text-sm font-medium text-gray-700 mb-2">
                    Your review <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    id="review"
                    value={review}
                    onChange={(e) => setReview(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 resize-none text-sm"
                    placeholder="Share your experience with this course. What did you like? What could be improved?"
                    rows={5}
                    maxLength={500}
                  />
                  <div className="text-right text-xs text-gray-500">
                    {review.length}/500 characters
                  </div>
                </div>

                {/* Modal Actions */}
                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-md transition-colors duration-200"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting || rating === 0 || !review.trim()}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Submitting...</span>
                      </>
                    ) : (
                      <>
                        <FaStar className="text-yellow-300" />
                        <span>Submit review</span>
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Add custom styles for modal animations */}
      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out;
        }

        .animate-slideUp {
          animation: slideUp 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default CourseRating;