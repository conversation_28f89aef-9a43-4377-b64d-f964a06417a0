import React, { useState } from "react";
import { Link, useLocation, useParams, useNavigate } from "react-router-dom";
import useFetch from "@/hooks/useFetch";
import { Icon } from "@iconify/react";
import EditorData from "../../components/EditorData";
import Accordion from "../../components/ui/Accordion";
import { ASSET_URL } from "@/config";
import avatar from "@/assets/images/avatar/av-1.svg";
import { useSelector } from "react-redux";
import Rating from "../../components/ui/Rating";
import banner from "@/assets/images/all-img/video-poster.png";
import ModuleAccordion from "../../components/ui/ModuleAccordion";
import shape from "@/assets/images/svg/shape5.svg";
import Mentors from "./Mentors";
import curveShape from "@/assets/images/svg/curveShape.svg";
import curveShape2 from "@/assets/images/svg/curveShpae2.svg";
import RoutineAccordion from "../../components/ui/RoutineAccordion";
import ReactPlayer from "react-player";
import RelatedCoures from "./RelatedCourses";
import certificateLoadingImg from "@/assets/images/all-img/certificateLoading.png";
import paymentProcessImg from "@/assets/images/all-img/paymentProcess.png";
import AssignmentList from "../assignment/list";
import SubjectAccordion from "../../components/ui/SubjectAccordion";
import api from "@/server/api";
import { useQueryClient } from "@tanstack/react-query";

import CourseRating from "./CourseRating";
const CourseDetails = () => {
  const { id, slug } = useParams();
  const [loading, setLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [subjectOpen, setSubjectOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const detailsUrl = id ? `course-details?id=${id}` : `course/${slug}`;
  const {
    data: coursedetails = [],
    isLoading,
    isError,
  } = useFetch({
    queryKey: detailsUrl,
    endPoint: detailsUrl,
    dependencies: [id, slug],
  });



  const queryClient = useQueryClient();

  if (isError) return <div>Error fetching data</div>;

  const { isAuth } = useSelector((state) => state.auth);

  const course = coursedetails?.data;
  console.log(course);

  const chartOptions = {
    chart: {
      type: "radialBar",
    },
    plotOptions: {
      radialBar: {
        hollow: {
          size: "70%",
        },
        dataLabels: {
          show: true,
          name: {
            show: false,
          },
          value: {
            show: true,
            fontSize: "25px",
          },
        },
      },
    },
  };

  const handleFreeEnroll = async () => {
    if (isAuth) {
      try {
        setLoading(true);
        console.log(id);
        const response = await api.post("purchase-course", {
          course_id: course?.id,
          payment_method: "Free",
        });
        if (response?.data.status) {
          queryClient.invalidateQueries(`course-details?id=${id}`);
        }
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    } else {
      navigate("/login", { state: { from: location }, replace: true }); // Use navigate here
    }
  };

  const chartSeries = [75];

  return isLoading ? (
    <div className="min-h-[450px] flex flex-col items-center justify-center">
      <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-purple-500"></div>
      <p>Loading...</p>
    </div>
  ) : (
    <div className=" py-8">
      <section className="space-y-8 container">
        {/* <GoBack title={"Course Details"} /> */}
        <div className="grid lg:grid-cols-2 gap-8 items-start">
          {/* Video/Image Section */}
          <div className="w-full">
            {coursedetails?.data?.trailer_video ? (
              <div className="w-full aspect-video rounded-2xl overflow-hidden shadow-lg">
                <ReactPlayer
                  url={`https://www.youtube.com/watch?v=${coursedetails?.data?.trailer_video}`}
                  className="rounded-2xl overflow-hidden"
                  width="100%"
                  height="100%"
                  controls={true}
                />
              </div>
            ) : (
              <div className="w-full aspect-video rounded-2xl overflow-hidden shadow-lg">
                <img
                  src={course?.thumbnail ? ASSET_URL + course?.thumbnail : banner}
                  className="w-full h-full object-cover"
                  alt={course?.title || "Course thumbnail"}
                />
              </div>
            )}
          </div>

          {/* Course Info Section */}
          <div className="space-y-6">
            {/* Header */}
            <div className="space-y-4">
              <div className="flex justify-between items-start gap-4">
                <span className="inline-flex items-center bg-gradient-to-r from-sky-50 to-blue-50 text-sky-700 px-4 py-2 text-sm font-medium rounded-full border border-sky-200">
                  Course Details
                </span>
                <Rating rating={course?.rating} />
              </div>

              <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 leading-tight">
                {course?.title}
              </h1>

              {/* Course Duration Information */}
              {(course?.course_duration > 0 || course?.duration_per_day > 0) && (
                <div className="flex flex-wrap gap-4 mt-4">
                  {course?.course_duration > 0 && (
                    <div className="inline-flex items-center gap-2 bg-sky-50 text-sky-700 px-4 py-2 rounded-full border border-sky-200">
                      <Icon icon="mdi:calendar-clock" className="w-4 h-4" />
                      <span className="text-sm font-medium">{course.course_duration} days total</span>
                    </div>
                  )}

                  {course?.duration_per_day > 0 && (
                    <div className="inline-flex items-center gap-2 bg-emerald-50 text-emerald-700 px-4 py-2 rounded-full border border-emerald-200">
                      <Icon icon="mdi:clock-time-four" className="w-4 h-4" />
                      <span className="text-sm font-medium">{course.duration_per_day} hours/day</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Description Section */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-800">About This Course</h3>
              <div className="relative">
                <div
                  className={`prose prose-sm max-w-none text-gray-600 leading-relaxed transition-all duration-300 ${
                    !isExpanded ? 'line-clamp-4' : 'line-clamp-none'
                  }`}
                >
                  <EditorData
                    htmlData={course?.description || "No description available."}
                  />
                </div>

                {course?.description && course.description.length > 150 && (
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="mt-2 inline-flex items-center gap-1 text-sky-600 hover:text-sky-700 font-medium text-sm transition-colors duration-200"
                  >
                    {isExpanded ? (
                      <>
                        <span>Show less</span>
                        <Icon icon="mdi:chevron-up" className="w-4 h-4" />
                      </>
                    ) : (
                      <>
                        <span>Read more</span>
                        <Icon icon="mdi:chevron-down" className="w-4 h-4" />
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>

            {/* Enrollment Button */}
            <div className="pt-4">
              {course?.is_enrolled !== true ? (
                course?.is_free ? (
                  <button
                    onClick={handleFreeEnroll}
                    disabled={loading}
                    className="w-full sm:w-auto inline-flex items-center justify-center gap-3 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {loading ? (
                      <>
                        <Icon icon="eos-icons:loading" width="20" height="20" />
                        <span>Enrolling...</span>
                      </>
                    ) : (
                      <>
                        <span>Enroll Now - Free</span>
                        <Icon icon="mdi:arrow-right" className="w-5 h-5" />
                      </>
                    )}
                  </button>
                ) : (
                  <Link
                    to={`/course-checkout/${course?.id}`}
                    className="w-full sm:w-auto inline-flex items-center justify-center gap-3 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5"
                  >
                    <span>Enroll Now</span>
                    <Icon icon="mdi:arrow-right" className="w-5 h-5" />
                  </Link>
                )
              ) : (
                <div className="inline-flex items-center gap-2 bg-green-50 text-green-700 px-6 py-3 rounded-xl border border-green-200">
                  <Icon icon="mdi:check-circle" className="w-5 h-5" />
                  <span className="font-medium">Already Enrolled</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* certification progress */}
      <section className="my-10 container">
        <h2 className="text-xl font-semibold text-sky-600 mb-4 flex items-center gap-2">
          <Icon icon="mdi:certificate-outline" className="text-2xl" />
          Certificate
        </h2>
        <div className="shadow-md border rounded-lg p-4 bg-gradient-to-r from-[#E8F0F7] to-[#F0F7FF] overflow-hidden relative">
          <div className="absolute top-0 right-0 w-24 h-24 bg-sky-100 rounded-full -mr-8 -mt-8 opacity-40"></div>

          {!isAuth ? (
            <div className="flex flex-col md:flex-row items-center justify-between gap-5 py-2 relative z-10">
              <div className="flex items-start md:items-center gap-4">
                <div className="bg-blue-100 p-3 rounded-full flex-shrink-0">
                  <Icon icon="mdi:certificate" className="text-3xl text-sky-600" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-sky-700 mb-1">Earn Your Professional Certificate</h3>
                  <p className="text-sm text-gray-600 mb-2">
                    Showcase your achievement with a verified certificate upon course completion
                  </p>
                  <ul className="text-sm space-y-1">
                    <li className="flex items-center gap-2">
                      <Icon icon="mdi:check-circle" className="text-green-500" />
                      <span>Add to your professional profile</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <Icon icon="mdi:check-circle" className="text-green-500" />
                      <span>Share on LinkedIn and social media</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <Icon icon="mdi:check-circle" className="text-green-500" />
                      <span>Boost your career opportunities</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="flex-shrink-0 md:self-center">
                <Link
                  to="/login"
                  state={{ from: location }}
                  className="bg-sky-600 hover:bg-sky-700 text-white px-5 py-2 rounded-lg text-sm flex items-center gap-2 transition-all duration-300 shadow-md whitespace-nowrap"
                >
                  <Icon icon="mdi:login" className="text-lg" />
                  Sign In to Track Progress
                </Link>
              </div>
            </div>
          ) : (
            <div className="flex flex-col md:flex-row gap-4 justify-between items-center relative z-10">
              <div className="flex flex-col gap-2 w-full md:w-3/4">
                <div className="flex items-center gap-2">
                  <span className="bg-white text-sky-600 w-auto text-center py-1 px-3 text-sm rounded-full shadow-sm flex items-center gap-1">
                    <Icon icon="mdi:certificate" className="text-lg" />
                    {course?.title}
                  </span>
                  {course?.is_enrolled && course?.progress >= 100 && (
                    <span className="bg-green-100 text-green-600 py-0.5 px-2 text-xs rounded-full flex items-center gap-1">
                      <Icon icon="mdi:check-circle" className="text-sm" />
                      Completed
                    </span>
                  )}
                </div>

                {course?.is_enrolled && (
                  <div className="w-full mt-1">
                    <div className="flex items-center gap-2 mb-1.5">
                      <span className="text-sm font-medium text-sky-700">Progress</span>
                      <span className="text-xs bg-sky-100 text-sky-700 px-2 py-0.5 rounded-full">{course?.progress.toFixed(1)}%</span>
                    </div>

                    <div className="w-full bg-gray-200 rounded-full h-2.5 overflow-hidden shadow-inner">
                      <div
                        className="bg-sky-500 h-2.5 rounded-full transition-all duration-500 ease-out"
                        style={{ width: `${course?.progress}%` }}
                      ></div>
                    </div>

                    <p className="text-sm text-sky-700 mt-2 font-medium">
                      {course?.progress < 100
                        ? "Complete the course to get your certificate"
                        : "Congratulations! You've completed the course"}
                    </p>
                  </div>
                )}
              </div>

              <div className="flex-shrink-0">
                {course?.is_enrolled && course?.progress >= 100 ? (
                  <button className="bg-sky-600 hover:bg-sky-700 text-white px-4 py-1.5 rounded-lg text-sm flex items-center gap-1 transition-all duration-300 shadow-sm whitespace-nowrap">
                    <Icon icon="mdi:download" className="text-lg" />
                    View Certificate
                  </button>
                ) : course?.is_enrolled ? (
                  <div className="w-20 h-20 relative">
                    <svg className="w-full h-full" viewBox="0 0 100 100">
                      <circle
                        cx="50" cy="50" r="45"
                        fill="none"
                        stroke="#E2E8F0"
                        strokeWidth="8"
                      />
                      <circle
                        cx="50" cy="50" r="45"
                        fill="none"
                        stroke="#0284C7"
                        strokeWidth="8"
                        strokeDasharray="283"
                        strokeDashoffset={283 - (283 * course?.progress / 100)}
                        transform="rotate(-90 50 50)"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center flex-col">
                      <span className="text-lg font-bold text-sky-700">{course?.progress.toFixed(0)}%</span>
                    </div>
                  </div>
                ) : (
                  <div className="w-20 h-20 flex items-center justify-center">
                    {/* Certificate image commented out as requested */}
                    {/* <img
                      src={certificateLoadingImg}
                      alt="Certificate"
                      className="w-16 h-16 object-contain"
                    /> */}
                    <Icon icon="mdi:certificate-outline" className="w-16 h-16 text-sky-300" />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </section>

      {course?.latest_activities ? (
        <section className="my-10 container bg-[#E2F6F0] rounded-lg p-5 py-2 md:flex justify-between items-center">
          <div className="flex gap-4">
            <img
              className="w-10 object-contain rounded-lg"
              src={avatar}
              alt=""
            />
            <div className="space-y-2">
              <span className="bg-white text-sky-600 w-32 text-center p-2 px-4 text-sm rounded-full">
                Last Watched
              </span>
              <h2 className="text-xl text-black">
                {course?.latest_activities?.title}
              </h2>
              <p className="text-black text-sm">
                {course?.latest_activities?.module_name}
              </p>
            </div>
          </div>

          <div className="flex gap-4 items-center">
            {course?.latest_activities?.type === "video" ||
              course?.latest_activities?.type === "script" ? (
              <Link
                to={`/content-details/${course?.latest_activities?.id}`}
                className="bg-sky-700 text-white px-5 py-2 rounded-lg hover:bg-sky-600"
              >
                Resume
              </Link>
            ) : (
              <Link
                to={`/quiz/${course?.latest_activities?.element_id}/${course?.id}/${course?.latest_activities?.id}`}
                className="bg-sky-700 text-white px-5 py-2 rounded-lg hover:bg-sky-600"
              >
                Resume
              </Link>
            )}
          </div>
        </section>
      ) : (
        ""
      )}
      {/* course outline section  */}
      <section className="space-y-5 my-14 max-sm:my-10 container ">
        <h2 className="text-xl max-sm:text-2xl text-sky-700">Course Outline</h2>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-5">
          <div className="col-span-2">
          {course?.subjects?.length > 0 && (
            <div className="">
              {course?.subjects?.map((subject, idx) => (
                <SubjectAccordion key={idx} subject={subject} course={course}  />
              ))}
            </div>
          )}
          {course?.outlines?.length > 0 && (
            <div className="">
              {course?.outlines?.map((module, idx) => (
                <ModuleAccordion key={idx} module={module} course={course} />
              ))}
            </div>
          )}
          </div>

          <div className="bg-transparent col-span-1">
            <div className="bg-white shadow-lg border rounded-lg p-5">
              {/* Price and discount section */}

              {course?.is_enrolled != true ? (
                <div className="bg-red-200 rounded-lg shadow-md px-6 py-5 text-center space-y-3">


                
                  <p className="text-xl text-center text-sky-700 font-semibold">
                    Course Fee:{" "}
                    {course?.is_free ? (
                      "Free"
                    ) : course?.monthly_amount > 0 &&
                      course?.installment_type === "Monthly" ? (
                      <span> {course.monthly_amount?.toLocaleString()}/Month {course?.currency}</span>
                    ) : course?.sale_price && course?.regular_price ? (
                      <p className="flex justify-center items-center font-semibold text-lg gap-3 text-red-500">
                        {course.sale_price?.toLocaleString()} {course?.currency}
                        {course.regular_price > course.sale_price && 
                        <span className="text-gray-500 line-through">
                          {course.regular_price?.toLocaleString()} {course?.currency}
                        </span>
                        }
                      </p>
                    ) : course?.minimum_enroll_amount?.toLocaleString() + ' ' + course?.currency} 
                  </p>
                  {course?.discount_percentage > 0 && 
                  <h2 className="text-xl font-semibold text-[#FF0000]">
                    {course?.discount_percentage}% Discount
                  </h2>
                  }
                  <div className="w-full">
                    {course?.is_enrolled != true ? (
                      course?.is_free ? (
                        <button
                          // to={`/course-checkout/${course?.id}`}
                          onClick={handleFreeEnroll}
                          className="flex items-center justify-center gap-2 bg-sky-600 text-white px-6 py-2 rounded-full border border-transparent text-lg transition-color duration-300 max-w-60 mx-auto"
                        >
                          Start Course Now{" "}
                          <Icon
                            icon="line-md:arrow-right"
                            className="text-lg transition-all duration-300 transform"
                          />
                        </button>
                      ) : (
                        <Link
                          to={`/course-checkout/${course?.id}`}
                          className="flex items-center justify-center gap-2 bg-sky-600 text-white px-6 py-2 rounded-full border border-transparent hover:bg-white hover:text-sky-600 hover:border-sky-600 text-lg transition-color duration-300 max-w-60 mx-auto"
                        >
                          Start Course Now{" "}
                          <Icon
                            icon="line-md:arrow-right"
                            className="text-lg transition-all duration-300 transform"
                          />
                        </Link>
                      )
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              ) : (
                ""
              )}

              {/* Course features section */}
              <div className="space-y-3">
                <h2 className="text-xl max-sm:text-2xl font-semibold text-sky-700 my-6">
                  You’re getting in this course
                </h2>

                {/* Course Duration Information */}
                {course?.course_duration > 0 && (
                  <div className="space-y-3">
                    <span className="flex items-center gap-2 text-gray-500 text-md">
                      <Icon icon="mdi:calendar-clock" className="w-7 h-7 text-sky-600" />
                      <p className="text-sm">{course.course_duration} hours total duration</p>
                    </span>
                  </div>
                )}

                {course?.duration_per_day > 0 && (
                  <div className="space-y-3">
                    <span className="flex items-center gap-2 text-gray-500 text-md">
                      <Icon icon="mdi:clock-time-four" className="w-7 h-7 text-sky-600" />
                      <p className="text-sm">{course.duration_per_day} hours per day</p>
                    </span>
                  </div>
                )}

                {coursedetails?.data?.features?.map((feature, idx) => (
                  <div key={idx} className="space-y-3">
                    <span className="flex items-center gap-2 text-gray-500 text-md">
                      <img
                        src={ASSET_URL + feature?.icon}
                        alt="Youtube Icon"
                        className="h-7"
                      />
                      <p className="text-sm">{feature?.title}</p>
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
      {coursedetails?.data?.assignments?.length > 0 && (
        <section className="container space-y-5">
          <h2 className="text-xl text-sky-700">Assignments</h2>
          <AssignmentList assignments={coursedetails?.data?.assignments} />
        </section>
      )}
      {/* External Links  */}
      {coursedetails?.data?.external_libraries?.length > 0 && (
        <section className="my-16 max-sm:my-12 space-y-5 container ">
          <h2 className="text-xl text-sky-700">
            External Libraries for this course
          </h2>

          {coursedetails?.data?.external_libraries?.map((extLibrary, idx) => (
            <div key={idx} className="flex items-start gap-3">

              <a className="text-sm text-sky-700 underline hover:text-sky-600" href={extLibrary?.url} target="_blank"> {idx + 1}. {extLibrary?.title}</a>
            </div>
          ))}
        </section>
      )}

      {/* what will learn section  */}
      {coursedetails?.data?.learning_outcomes?.length > 0 && (
        <section className="my-16 max-sm:my-12 space-y-5 container ">
          <h2 className="text-xl text-sky-700">
            What you will learn by doing this course
          </h2>

          {coursedetails?.data?.learning_outcomes?.map((feature, idx) => (
            <div key={idx} className="flex items-start gap-3">
              <img className="h-8" src={ASSET_URL + feature?.icon} alt="" />
              <p className="text-sm">{feature?.title}</p>
            </div>
          ))}
        </section>)}

      {/* routine section  */}
      <section className="container my-16">
        <RoutineAccordion
          course={coursedetails?.data}
          classSchedule={coursedetails?.data?.class_schedules}
          isOpen={false}
        />
      </section>

      {/* course instructor section  */}
      <section className="h-[500px] max-sm:h-[350px] bg-blue-50 relative overflow-hidden">
        <img
          src={shape}
          className="absolute left-0 top-0 opacity-10 z-0"
          alt=""
        />
        <img
          src={shape}
          className="absolute -right-5 -bottom-10 rotate-180 opacity-10 z-0"
          alt=""
        />
        <Mentors
          mentors={coursedetails?.data?.mentors}
          sectionTitle="Course Instructor"
        />
      </section>

      {/* payment progress section  */}
      {coursedetails?.data?.is_enrolled != true && (
        <section className="container my-16 space-y-10">
          <div className="relative border">
            <img
              src={curveShape}
              className="absolute left-0 top-0 z-0"
              alt=""
            />
            <img
              src={curveShape2}
              className="absolute right-0 bottom-0 z-0"
              alt=""
            />

            {/* <div className="relative z-10 flex items-center justify-center h-full">
              <div className="flex max-sm:flex-col items-center justify-center max-sm:gap-5 max-sm:p-5 gap-10">
                <div className="space-y-4">
                  <h2 className="text-4xl text-sky-600 flex items-center gap-3">
                    <Icon
                      icon="fluent:payment-48-regular"
                      className="text-2xl text-sky-600"
                    />{" "}
                    Payment Process
                  </h2>
                  <p className="text-gray-500 text-md font-semibold">
                    To know how to make payments, please{" "}
                  </p>
                </div>

                <img
                  src={paymentProcessImg}
                  alt=""
                  style={{ maxHeight: "250px", width: "auto" }}
                  className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl"
                />
              </div>
            </div> */}
          </div>
        </section>
      )}
      {/* what you need  */}
      {coursedetails?.data?.prerequisites?.length > 0 && (
        <section className="space-y-5 container my-14">
          <h2 className="text-xl text-sky-700">
            What you need to do this course
          </h2>

          {coursedetails?.data?.prerequisites?.map((item, idx) => (
            <div className="flex gap-2 items-center text-lg" key={idx}>
              <img className="h-8" src={ASSET_URL + item?.icon} alt="" />
              <p className="text-sm">{item?.title}</p>
            </div>
          ))}
        </section>)}

      {/* Discussion Forum Section */}
      {course?.is_enrolled === true && (
        <section className="container my-14">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <div className="bg-primary-100 p-2 rounded-full mr-3">
                <Icon icon="mdi:forum" className="h-6 w-6 text-primary-600" />
              </div>
              <h2 className="text-xl font-semibold text-sky-700">Discussion Forum</h2>
            </div>
            <Link
              to={`/course-discussion/${coursedetails?.data?.id}`}
              className="flex items-center gap-2 text-primary-600 hover:text-primary-700 transition-colors group bg-primary-50 px-4 py-2 rounded-full"
            >
              <span>View All Discussions</span>
              <Icon icon="mdi:arrow-right" className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>

          <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl shadow-md p-8 border border-gray-200">
            <div className="flex flex-col md:flex-row items-center">
              <div className="mb-6 md:mb-0 md:mr-8 bg-white p-4 rounded-full shadow-md">
                <Icon icon="mdi:forum-outline" className="h-16 w-16 text-primary-500" />
              </div>
              <div className="text-center md:text-left flex-1">
                <h3 className="text-2xl font-semibold text-gray-800 mb-3">Join the Course Discussion</h3>
                <p className="text-gray-600 max-w-2xl mx-auto md:mx-0 mb-6">
                  Connect with fellow students, ask questions, and share insights about the course material.
                </p>
                <Link
                  to={`/course-discussion/${coursedetails?.data?.id}`}
                  className="px-6 py-3 bg-gradient-to-r from-primary-600 to-primary-500 text-white rounded-full shadow-md hover:shadow-lg hover:from-primary-700 hover:to-primary-600 transition-all inline-flex items-center gap-2"
                >
                  <Icon icon="mdi:plus-circle" className="h-5 w-5" />
                  <span>Start Discussion</span>
                </Link>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* faq section  */}
      {coursedetails?.data?.faqs?.length > 0 && (
        <section className="container my-14 space-y-5">
          <h2 className="text-xl text-sky-700 ml-5">
            Frequently Asking Question (FAQ){" "}
          </h2>
          {coursedetails?.data?.faqs?.map((item, idx) => (
            <Accordion key={idx} title={item?.title} content={item?.answer} />
          ))}
        </section>
      )}

      {course?.is_enrolled == true && (
        <section className="container py-10">
          <CourseRating
            ratings={coursedetails?.data?.rating_list}
            course={coursedetails?.data}
          />
        </section>
      )}
      {/* recommended course section  */}
      <section className="container py-10">
        <RelatedCoures
          courses={coursedetails?.data?.related_courses}
          sectionTitle={"Recommended Courses"}
        />
      </section>
    </div>
  );
};

export default CourseDetails;
