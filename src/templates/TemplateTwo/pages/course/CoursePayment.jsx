import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import useFetch from "@/hooks/useFetch";
import { Icon } from "@iconify/react";
import { Formik, Field, Form } from "formik";
import paymentImg from "@/assets/images/all-img/payment_step.jpg";
import GoBack from "@/components/ui/GoBack";
import Modal from "@/components/ui/Modal";
import { ASSET_URL } from "@/config";
import api from "@/server/api";
import * as Yup from "yup";
import StripePayment from "./StripePayment";
import CyberSourcePayment from "./CyberSourcePayment";
import { useSelector } from "react-redux";
const CoursePayment = () => {
  const [clientSecret, setClientSecret] = useState(null);
  const { id } = useParams();
  const navigate = useNavigate();
  const [selectedPaymentType, setSelectedPaymentType] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [paymentInfo, setPaymentInfo] = useState({ trx_id: "" });
  const [paymentId, setPaymentId] = useState('');
  const [isPaying, setIsPaying] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedGateway, setSelectedGateway] = useState(null);
  const [isProcessingGateway, setIsProcessingGateway] = useState(false);
  const [error, setError] = useState(null);
  const [couponCode, setCouponCode] = useState('');
  const [couponData, setCouponData] = useState(null);
  const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [isCouponLoading, setIsCouponLoading] = useState(false);

  const { data: coursedetails = [], isLoading, isError } = useFetch({
    queryKey: `get-course-payment-details/${id}`,
    endPoint: `get-course-payment-details?id=${id}`,
    dependencies: [id],
  });


  const { organization } = useSelector((state) => state.commonSlice);
  const { data: paymentTypes = [], isLoading: paymentTypesIsLoading } = useFetch({
    queryKey: `organization-payment-typelist`,
    endPoint: `organization-payment-typelist`,
  });

  useEffect(() => {
    if (paymentTypes?.data?.length > 0 && !selectedPaymentType) {
      setSelectedPaymentType(paymentTypes.data[0]);
    }
  }, [paymentTypes]);

  console.log(paymentTypes?.data);
  const course = coursedetails?.data;

  const handlePaymentTypeSelect = (paymentType) => {
    setSelectedPaymentType(paymentType);
    setSelectedGateway(null);
    setClientSecret(null);
  };

  const handleGatewaySelect = (gateway) => {
    console.log(gateway);
    setSelectedGateway(gateway);
    setSelectedPaymentType(null);
    setClientSecret(null);
  };

  const handlePayment = async (values) => {
    setIsPaying(true);
    const formdata = new FormData();
    formdata.append("course_id", id);
    formdata.append("payment_method_id", selectedPaymentType?.id);
    formdata.append("payment_method", selectedPaymentType?.name);
    formdata.append("trx_id", values.trx_id);
    formdata.append("coupon", couponCode || ""); // Use the couponCode state variable instead of form values
    if (values.image) {
      formdata.append("image", values.image);
    }

    console.log("Submitting with coupon:", couponCode); // Debug log

    try {
      const response = await api.filepost("enroll-course", formdata);
      if (response?.data.status === true) {
        navigate("/my-payments");
      }
    } catch (error) {
      console.error("Payment error:", error);
    } finally {
      setIsPaying(false);
    }
  };

  const processGatewayPayment = async () => {
    if (!selectedGateway || !course) return;
    setIsProcessingGateway(true);
    try {
      console.log("Processing gateway payment for:", selectedGateway?.payment_gateway?.type);
      const response = await api.post("process-payment", {
        gateway: selectedGateway?.payment_gateway?.type,
        amount: course.installment_type === "Monthly" ? course.monthly_amount : course.sale_price,
        currency: course.currency,
        course_id: id,
        coupon: couponCode || ""
      });
      console.log("Gateway payment response:", response);
      if (selectedGateway?.payment_gateway?.type === "Stripe") {
        setPaymentId(response?.data?.data?.id);
        if (response.data.redirect_url) {
          window.location.href = response.data.redirect_url;
        } else if (response.data.data?.client_secret) {
          setClientSecret(response.data.data.client_secret);
        } else {
          console.error('Unexpected Stripe response:', response);
          setError('Failed to initialize payment. Please try again.');
        }
      } else if (selectedGateway?.payment_gateway?.type?.toLowerCase() === "cybersource") {
        console.log("Processing CyberSource payment");
        setPaymentId(response?.data?.data?.transaction_id);
        if (response.data.data?.client_secret) {
          console.log("Setting client secret:", response.data.data.client_secret);
          setClientSecret(response.data.data.client_secret);
        } else {
          console.error('Unexpected CyberSource response:', response);
          setError('Failed to initialize payment. Please try again.');
        }
      }
    } catch (error) {
      console.error("Gateway payment error:", error);
      setError(error.message || 'Payment processing failed');
    } finally {
      setIsProcessingGateway(false);
    }
  };

  const openImageModal = (image) => {
    setSelectedImage(image);
    setIsModalOpen(true);
  };

  const closeImageModal = () => {
    setIsModalOpen(false);
    setSelectedImage(null);
  };

  // Function to check coupon validity
  const checkCoupon = async (code) => {
    if (!code) return;

    setIsCouponLoading(true);
    try {
      const formData = new FormData();
      formData.append('organization_id', organization?.id); 
      formData.append('coupon', code);

      const response = await api.post('coupon-check', formData);

      if (response?.data?.status === true) {
        setCouponData(response.data.data);
        setCouponCode(code);
        return response.data.data;
      } else {
        setCouponData(null);
        return null;
      }
    } catch (error) {
      console.error('Coupon check error:', error);
      setCouponData(null);
      return null;
    } finally {
      setIsCouponLoading(false);
    }
  };


  // Function to calculate discounted price
  const calculateDiscountedPrice = () => {
    if ( !course) return null;

    if (!couponData ) {
      return course.installment_type === "Monthly"
      ? course.minimum_enroll_amount
      : course.sale_price;
    }


    const basePrice = course.installment_type === "Monthly"
      ? course.minimum_enroll_amount
      : course.sale_price;

    console.log(basePrice);

    if (couponData.discount_type === "percentage") {
      const discountAmount = (basePrice * couponData.discount) / 100;
      return basePrice - discountAmount;
    } else {
      // Fixed amount discount
      return basePrice - couponData.discount;
    }
  };

  if (isLoading || paymentTypesIsLoading) {
    return (
      <div className="min-h-[450px] flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-blue-500"></div>
        <p className="mt-4 text-gray-600">Loading payment details...</p>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="min-h-[450px] flex flex-col items-center justify-center">
        <Icon icon="heroicons:exclamation-triangle" className="text-red-500 text-5xl mb-4" />
        <p className="text-red-500 text-lg">Error loading payment details</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="py-8 bg-gray-50 min-h-screen">
      <section className="space-y-6 container mx-auto px-4 max-w-6xl">
        <GoBack title={course?.title} />
        <Formik
          initialValues={{
            trx_id: '',
            image: null,
            selectedItem: "",
            coupon: ""
          }}
          validationSchema={Yup.object({
            trx_id: Yup.string().test({
              name: 'conditionalRequired',
              test: function() {
                // Only require trx_id when selectedPaymentType is selected and selectedGateway is not
                if (selectedPaymentType && !selectedGateway) {
                  if (!this.parent.trx_id) {
                    return this.createError({
                      message: 'Transaction ID is required',
                      path: 'trx_id'
                    });
                  }
                }
                return true;
              }
            })
          })}
          onSubmit={handlePayment}
        >
          {({ handleChange, handleSubmit, setFieldValue, values, touched, errors }) => (

            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="md:flex items-start gap-6">
                {/* Left Section: Payment Options */}
                <div className="flex-1 bg-white shadow-md p-6 rounded-lg">
                  <h2 className="text-xl font-bold text-gray-800 mb-6">Payment Options</h2>
                  {/* Gateway Payment Options */}
                  {course?.gateways?.length > 0 && (
                    <div className="space-y-4 mb-8">
                      <label className="block text-sm font-medium text-gray-700">
                        Pay with Payment Gateway
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {course.gateways.map((gatewayItem) => (
                          <div
                            key={gatewayItem.id}
                            className={`border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                              selectedGateway?.payment_gateway?.id === gatewayItem.payment_gateway.id
                                ? 'border-blue-500 bg-blue-50 shadow-md'
                                : 'border-gray-200 hover:border-blue-300'
                            }`}
                            onClick={() => handleGatewaySelect(gatewayItem)}
                          >
                            <div className="flex flex-col items-center">
                              {gatewayItem.payment_gateway.logo ? (
                                <img
                                  src={`${ASSET_URL}${gatewayItem.payment_gateway.logo}`}
                                  alt={gatewayItem.payment_gateway.name}
                                  className="h-12 w-12 object-contain mb-2"
                                />
                              ) : (
                                <div className="h-12 w-12 flex items-center justify-center bg-gray-100 rounded-full mb-2">
                                  <Icon icon="heroicons:credit-card" className="text-2xl text-gray-500" />
                                </div>
                              )}
                              <span className="text-sm font-medium text-center">
                                {gatewayItem.payment_gateway.name}
                              </span>
                              {gatewayItem.payment_gateway.short_description && (
                                <p className="text-xs text-gray-500 mt-1 text-center">
                                  {gatewayItem.payment_gateway.short_description}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}


                  {paymentTypes?.data?.length > 0 && (
                    <div className="space-y-4 mb-8">
                      <label className="block text-sm font-medium text-gray-700">
                        Pay with Payment Method
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {paymentTypes?.data?.map((paymentType) => (
                          <div
                            key={paymentType.id}
                            className={`border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                              selectedPaymentType?.id === paymentType.id
                                ? 'border-blue-500 bg-blue-50 shadow-md'
                                : 'border-gray-200 hover:border-blue-300'
                            }`}
                            onClick={() => handlePaymentTypeSelect(paymentType)}
                          >
                            <div className="flex flex-col items-center">
                              {paymentType.icon ? (
                                <img
                                  src={`${ASSET_URL}${paymentType.icon}`}
                                  alt={paymentType.name}
                                  className="h-12 w-12 object-contain mb-2"
                                />
                              ) : (
                                <div className="h-12 w-12 flex items-center justify-center bg-gray-100 rounded-full mb-2">
                                  <Icon icon="heroicons:credit-card" className="text-2xl text-gray-500" />
                                </div>
                              )}
                              <span className="text-sm font-medium text-center">
                                {paymentType.name}
                              </span>
                              {paymentType.short_description && (
                                <p className="text-xs text-gray-500 mt-1 text-center">
                                  {paymentType.short_description}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Payment Method Details */}
                  {selectedPaymentType && (
                    <div className="mt-6 space-y-4">
                      <h3 className="text-lg font-medium text-gray-800">{selectedPaymentType.name} Details</h3>
                      {selectedPaymentType.items.map((item) => (
                        <div key={item.id} className="space-y-2">
                          <label className="block text-sm text-gray-700">{item.field_name}</label>
                          {item.field_value !== null && (
                            <Field
                              type="text"
                              name={`field_${item.id}`}
                              value={item.field_value || ""}
                              disabled
                              className="w-full p-2 border rounded-lg bg-gray-100 focus:outline-none"
                            />
                          )}
                          {item.image !== null && (
                            <div className="mt-2">
                              <img
                                src={ASSET_URL + item.image}
                                className="w-full h-48 object-contain rounded-lg cursor-pointer border hover:shadow-md transition"
                                alt={item.field_name}
                                onClick={() => openImageModal(item.image)}
                              />
                              <p className="text-xs text-gray-500 mt-1">Click to enlarge</p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                {/* Right Section: Order Summary and Payment Form */}
                <div className="flex-1">
                  <div className="bg-white shadow-md p-6 rounded-lg sticky top-6">
                    <h2 className="text-xl font-bold text-gray-800 mb-6">Payment Summary</h2>
                    <div className="space-y-4 mb-6">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Course:</span>
                        <span className="font-medium">{course?.title}</span>
                      </div>
                      {course?.installment_type !== "Monthly" ? (
                        <>
                         { course?.regular_price > course?.sale_price && 
                          <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                            <span className="text-gray-600">Regular Price:</span>
                            <span className="text-gray-800 line-through">{course?.regular_price?.toLocaleString()} {course?.currency}</span>
                          </div>
                          }
                         { course?.discount_percentage > 0 && 
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Discount:</span>
                            <span className="text-red-500 font-medium">{course?.discount_percentage}%</span>
                          </div>
                          }
                          <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                            <span className="text-gray-600">Price:</span>
                            <span className="text-green-600 font-bold">{course?.sale_price?.toLocaleString()} {course?.currency}</span>
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                            <span className="text-gray-600">Monthly Fee:</span>
                            <span className="text-green-600 font-bold">{course?.monthly_amount?.toLocaleString()} {course?.currency}</span>
                          </div>
                          <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                            <span className="text-gray-600">Initial Payment:</span>
                            <span className="text-green-600 font-bold">{course?.minimum_enroll_amount?.toLocaleString()} {course?.currency}</span>
                          </div>
                        </>
                      )}
                      {/* Coupon Discount */}
                      {couponData && (
                        <>
                          <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                            <span className="text-gray-600">Coupon Applied:</span>
                            <span className="text-blue-600 font-medium">{couponData.code}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Coupon Discount:</span>
                            <span className="text-red-500 font-medium">
                              {couponData.discount_type === "percentage"
                                ? `${couponData.discount}%`
                                : `${couponData.discount} ${course?.currency}`}
                            </span>
                          </div>
                        </>
                      )}

                      <div className="flex justify-between items-center pt-4">
                        <span className="text-lg font-bold text-gray-800">Total:</span>
                        <span className="text-xl font-bold text-blue-600">
                          {couponData
                            ? `${calculateDiscountedPrice()?.toLocaleString()} ${course?.currency}`
                            : `${course?.installment_type === "Monthly"
                                ? course?.minimum_enroll_amount?.toLocaleString()
                                : course?.sale_price?.toLocaleString()} ${course?.currency}`
                          }
                        </span>
                      </div>
                    </div>

                    {/* Coupon Button */}
                    <div className="mb-6">


                      {couponData ?  (
                        <div className="mt-2 text-sm text-green-600 flex items-center">
                          <Icon icon="heroicons:check-circle" className="mr-1" />
                          Coupon "{couponData.code}" applied successfully!
                        </div>
                      ):

                      <button
                        type="button"
                        onClick={() => setIsCouponModalOpen(true)}
                        className="flex items-center text-blue-600 hover:text-blue-800 font-medium"
                      >
                        <Icon icon="heroicons:ticket" className="mr-2" />
                        {'Use Coupon Code'}
                      </button>
                      }
                    </div>
                    {/* Payment Forms */}
                    {(() => {
                      console.log("Rendering payment form - Gateway type:", selectedGateway?.payment_gateway?.type);
                      console.log("Client secret:", clientSecret);
                      console.log("Payment ID:", paymentId);

                      if (selectedGateway?.payment_gateway?.type === "Stripe" && clientSecret) {
                        return (
                          <StripePayment
                            paymentId={paymentId}
                            clientSecret={clientSecret}
                            credentials={selectedGateway?.credentials}
                            onSuccess={() => navigate("/my-payments")}
                            courseId={id}
                            currency={course?.currency}
                            amount={calculateDiscountedPrice()}
                            coupon={couponCode}
                          />
                        );
                      } else if (selectedGateway?.payment_gateway?.type?.toLowerCase() === "cybersource" && clientSecret) {
                        console.log("Rendering CyberSource payment form");
                        return (
                          <CyberSourcePayment
                            paymentId={paymentId}
                            clientSecret={clientSecret}
                            credentials={selectedGateway?.credentials}
                            onSuccess={() => navigate("/my-payments")}
                            courseId={id}
                            currency={course?.currency}
                            amount={calculateDiscountedPrice()}
                            coupon={couponCode}
                          />
                        );
                      } else if (selectedGateway) {
                        return (
                          <button
                            type="button"
                            onClick={processGatewayPayment}
                            className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 disabled:opacity-70 disabled:cursor-not-allowed"
                            disabled={isProcessingGateway}
                          >
                            {isProcessingGateway ? (
                              <span className="flex items-center justify-center">
                                <Icon icon="eos-icons:loading" className="mr-2 text-xl" />
                                Processing...
                              </span>
                            ) : (
                              `Pay with ${selectedGateway?.payment_gateway?.name}`
                            )}
                          </button>
                        );
                      } else {
                        return (
                          <>
                            {/* Manual Payment Fields */}
                            {selectedPaymentType && (
                              <div className="space-y-4">
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Transaction ID <span className="text-red-500">*</span>
                                  </label>
                                  <Field
                                    type="text"
                                    name="trx_id"
                                    onChange={(e) => {
                                      handleChange(e);
                                      setPaymentInfo({ ...paymentInfo, trx_id: e.target.value });
                                    }}
                                    className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter transaction ID"
                                  />
                                  {touched.trx_id && errors.trx_id && (
                                    <div className="text-red-500 text-sm mt-1">{errors.trx_id}</div>
                                  )}
                                </div>

                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Payment Screenshot (Optional)
                                  </label>
                                  <input
                                    type="file"
                                    name="image"
                                    accept="image/*"
                                    onChange={(e) => {
                                      const file = e.target.files[0];
                                      setFieldValue("image", file);
                                      setImagePreview(URL.createObjectURL(file));
                                    }}
                                    className="w-full p-2 border rounded-lg focus:outline-none"
                                  />
                                  {imagePreview && (
                                    <div className="mt-2">
                                      <img
                                        src={imagePreview}
                                        alt="Preview"
                                        className="h-32 object-contain border rounded-lg"
                                      />
                                    </div>
                                  )}
                                </div>
                                <button
                                  type="submit"
                                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 disabled:opacity-70 disabled:cursor-not-allowed mt-4"
                                  disabled={isPaying}
                                >
                                  {isPaying ? (
                                    <span className="flex items-center justify-center">
                                      <Icon icon="eos-icons:loading" className="mr-2 text-xl" />
                                      Processing...
                                    </span>
                                  ) : (
                                    'Complete Payment'
                                  )}
                                </button>
                              </div>
                            )}
                          </>
                        );
                      }
                    })()}
                  </div>
                </div>
              </div>
            </form>
          )}
        </Formik>
      </section>
      {/* Image Preview Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-lg font-medium">Payment Reference</h3>
              <button
                onClick={closeImageModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <Icon icon="heroicons:x-mark" className="w-6 h-6" />
              </button>
            </div>
            <div className="p-4">
              <img
                src={ASSET_URL + selectedImage}
                alt="Payment Reference"
                className="w-full h-auto object-contain"
              />
            </div>
            <div className="p-4 border-t flex justify-end">
              <button
                onClick={closeImageModal}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Coupon Modal */}
      <Modal
        title="Apply Coupon"
        activeModal={isCouponModalOpen}
        onClose={() => setIsCouponModalOpen(false)}
        centered
        size="md"
      >
        <Formik
          initialValues={{ couponCode: couponCode || '' }}
          onSubmit={async (values, { setSubmitting, setErrors }) => {
            const result = await checkCoupon(values.couponCode);
            setSubmitting(false);
            if (!result) {
              setErrors({ couponCode: 'Invalid coupon code' });
            }
          }}
        >
          {({ handleChange, handleSubmit, values, isSubmitting, errors }) => (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="couponCode" className="block text-sm font-medium text-gray-700 mb-1">
                  Enter Coupon Code
                </label>
                <input
                  type="text"
                  id="couponCode"
                  name="couponCode"
                  value={values.couponCode}
                  onChange={handleChange}
                  className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g. SUMMER2023"
                />
                {errors.couponCode && (
                  <div className="text-red-500 text-sm mt-1">{errors.couponCode}</div>
                )}
              </div>

              {couponData && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mt-4">
                  <div className="flex items-center">
                    <Icon icon="heroicons:check-circle" className="text-green-500 text-xl mr-2" />
                    <h4 className="font-medium text-green-800">Coupon Applied!</h4>
                  </div>
                  <div className="mt-2 text-sm text-green-700">
                    <p>Code: <span className="font-semibold">{couponData.code}</span></p>
                    <p>Discount: <span className="font-semibold">
                      {couponData.discount_type === 'percentage'
                        ? `${couponData.discount}%`
                        : `${couponData.discount} ${course?.currency}`}
                    </span></p>
                    <p className="mt-1">New Total: <span className="font-semibold">
                      {calculateDiscountedPrice()} {course?.currency}
                    </span></p>
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-3 mt-4">
                <button
                  type="button"
                  onClick={() => setIsCouponModalOpen(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                >
                  Close
                </button>
                {!couponData && (
                  <button
                    type="submit"
                    disabled={isSubmitting || isCouponLoading}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-70"
                  >
                    {isSubmitting || isCouponLoading ? (
                      <span className="flex items-center">
                        <Icon icon="eos-icons:loading" className="mr-2 animate-spin" />
                        Checking...
                      </span>
                    ) : (
                      'Apply Coupon'
                    )}
                  </button>
                )}

              </div>
            </form>
          )}
        </Formik>
      </Modal>
    </div>
  );
};

export default CoursePayment;
