import { Icon } from "@iconify/react/dist/iconify.js";
import React, { useEffect, useRef, useState } from "react";
import { Link } from "react-router-dom";
import Course from "../home/<USER>";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import SwiperCore, { Autoplay, Navigation } from "swiper";
import "@/pages/home/<USER>";
import { ASSET_URL } from "@/config";
import schoolIcon from "@/assets/images/svg/school.svg";

SwiperCore.use([Autoplay, Navigation]);

const Courses = ({ category, bought_items = [] }) => {
  const swiperRef = useRef(null);
  const [isPrevDisabled, setPrevDisabled] = useState(true);
  const [isNextDisabled, setNextDisabled] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(
    category?.sub_categories && category.sub_categories.length > 0
      ? category.sub_categories[0].id
      : null
  );


  // console.log(filteredItem[0]?.courses);

  // Update button states based on current index and total slides
  const updateButtonStates = () => {
    if (!swiperRef.current) return; // Check if swiperRef is initialized

    const swiper = swiperRef.current;
    const currentIndex = swiper.activeIndex;
    const totalSlides = swiper.slides.length;
    const slidesPerView = swiper.params.slidesPerView;

    // Disable the previous button if at the start
    setPrevDisabled(currentIndex === 0);

    // Disable the next button if we're at or beyond the last viewable slide
    setNextDisabled(currentIndex >= totalSlides - slidesPerView);
  };

  // useEffect(() => {
  //   if (!swiperRef.current) return;

  //   const swiper = swiperRef.current;
  //   // Initial update
  //   updateButtonStates();

  //   // Add event listener
  //   swiper.on("slideChange", updateButtonStates);

  //   // Cleanup on unmount
  //   return () => {
  //     if (swiper) swiper.off("slideChange", updateButtonStates);
  //   };
  // }, [coursesCategory?.length]);
  // // console.log(courses);

  return (
 
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 pt-6">
    {category?.courses?.slice(0, 3)?.map((course, idx) => (
      <Course key={idx} course={course} boughtCourses={bought_items} />
    ))}
  </div>
  );
};

export default Courses;
