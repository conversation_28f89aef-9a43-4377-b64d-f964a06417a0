import React, { useState } from "react";
import useFetch from "@/hooks/useFetch";
import { useParams } from "react-router-dom";
import GoBack from "@/components/ui/GoBack";
import Course from "../home/<USER>";
import Pagination from "../../components/ui/Pagination";

const CourseList = () => {
  const { menuId, subMenuId } = useParams();
  const [page, setPage] = useState(0);
  const perPage = 12;

  const {
    data: response,
    isLoading,
  } = useFetch({
    queryKey: `course-list-web${menuId}-${subMenuId}-${page}`,
    endPoint: `course-list-web?items_per_page=${perPage}&current_page=${page}${menuId ? `&category_id=${menuId}` : ''}${subMenuId ? `&sub_category_id=${subMenuId}` : ''}`,
  });

  const result = response?.data;
  const courses = result?.data || [];
  const boughtCourses = result?.bought_items || [];

  // Extract pagination info from API response
  const totalPages = result?.last_page ? result.last_page + 1 : 1; // Convert zero-based to 1-based
  const currentPage = result?.current_page !== undefined ? result.current_page + 1 : 1; // Convert zero-based to 1-based

  const handlePageChange = (newPage) => {
    // Convert 1-based page back to 0-based for API
    setPage(newPage - 1);
  };

  return isLoading ? (
    <div className="min-h-[450px] flex flex-col items-center justify-center">
      <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-purple-500"></div>
      <p>Loading...</p>
    </div>
  ) : (
    <div className="container items-center justify-center py-5 xl:py-10">
      <GoBack title={"All Courses"} />
      <div className="flex items-center justify-between">
        <h2 className="text-xl text-sky-600 font-semibold">Courses</h2>
        <p className="text-sky-600 flex items-center gap-1 font-semibold cursor-pointer text-sm">
        {/* <Icon icon="mage:filter" className="text-md mr-1" /> Filter */}
        </p>
      </div>

          
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-10 mt-6 text-sm mb-8">
        {courses?.length > 0 ? (
          courses.map((course, index) => (
            <Course key={course.id || index} course={course} index={index} boughtCourses={boughtCourses} />
          ))
        ) : (
          <div className="col-span-full text-center py-8">
            <p className="text-gray-500">No courses found</p>
          </div>
        )}
      </div>

      {totalPages > 1 && (
        <Pagination
          totalPages={totalPages}
          currentPage={currentPage}
          handlePageChange={handlePageChange}
        />
      )}

    </div>
  );
};

export default CourseList;
