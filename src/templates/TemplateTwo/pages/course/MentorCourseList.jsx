import React from "react";
import useFetch from "@/hooks/useFetch";
import { ASSET_URL } from "@/config";
import { useNavigate } from "react-router-dom";
import GoBack from "@/components/ui/GoBack";
import Icon from "@/components/ui/Icon";

const CourseCard = ({ course }) => {
  const navigate = useNavigate();

  return (
    <div className="flex items-center p-4 bg-white border rounded-lg shadow-md gap-4">
      {/* Thumbnail */}
      <img
        src={`${ASSET_URL}${course.thumbnail}`}
        alt={course.title}
        className="w-30 h-24 object-cover rounded-md"
      />
      {/* Course Info */}
      <div className="flex-grow">
        <h3 className="text-xl font-serif pb-2 font-semibold text-gray-800">{course.title}</h3>
        <p>{course.description.length > 220 ? course.description.slice(0, 220) + "..." : course.description}</p>
      </div>
      {/* Start Now Button */}
      <button
        className="flex bg-blue-500 w-72 text-white px-4 py-2 rounded-md shadow hover:bg-blue-600 items-center justify-center gap-2"
        onClick={() => navigate(`/course-details/${course.id}`)}
      >
        <span className="sm:hidden">Go</span>
        <span className="hidden sm:block">See Details</span>
        <Icon icon="mdi:arrow-right"></Icon>
      </button>
    </div>
  );
};

const MentorCourseList = () => {
  const {
    data: response,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `mentor-course-list`,
    endPoint: `mentor-course-list`,
  });
  const courses = response?.data;
  console.log(courses)
  if (isLoading) {
    return (
      <div className="min-h-[450px] flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-purple-500"></div>
        <p>Loading...</p>
      </div>
    );
  }

  if (isError || !courses) {
    return (
      <div className="min-h-[450px] flex flex-col items-center justify-center">
        <p className="text-red-500">You do not have enrolled any course</p>
      </div>
    );
  }

  return (
    <div className="container py-5 xl:py-10">
      <GoBack title={"My Courses"} />
      <div className="flex flex-col gap-6 mt-6">
        {courses.map((course) => (
          <CourseCard key={course.id} course={course} />
        ))}
      </div>
    
    </div>
  );
};

export default MentorCourseList;
