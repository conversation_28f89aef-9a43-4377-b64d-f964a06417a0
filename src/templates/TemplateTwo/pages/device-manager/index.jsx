import React, { useState } from "react";
import GoBack from "@/components/ui/GoBack";
import pcIcon from "@/assets/images/svg/mobile.svg";
import mobileIcon from "@/assets/images/svg/pc.svg";
import { Icon } from "@iconify/react/dist/iconify.js";
import Modal from "@/components/ui/Modal";
import useFetch from "@/hooks/useFetch";
import api from "@/server/api";
import { useDispatch } from "react-redux"; // Import dispatch
import { handleLogout } from "@/pages/auth/common/store";
import { useNavigate } from "react-router-dom";

export const LoggedInDevice = ({
  deviceInfo,
  handleSelectDevice,
  handleSingleDevice,
  isSelected,
  logout
}) => {
  const [deviceLogout, setDeviceLogout] = useState(false);

  return (
    <div className="relative w-full">
      <div className="flex justify-between items-center border shadow-md rounded-xl p-7">
        <div className="flex items-center gap-4">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => handleSelectDevice(deviceInfo.id)}
          />
          <img
            src={deviceInfo.device === "Android" ? mobileIcon : pcIcon}
            alt=""
          />
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <h3 className="text-xl text-black">{deviceInfo?.device}</h3>
              {deviceInfo?.is_current_device && (
                <div
                  title="Current Device"
                  className="bg-green-500 h-3 w-3 rounded-full"
                ></div>
              )}
            </div>
            <p className="font-semibold">{deviceInfo?.browser}</p>
          </div>
        </div>
      </div>
      <button
        onClick={() => {
          handleSingleDevice(deviceInfo?.id);
        }}
        className="flex items-center gap-2 border-2 rounded-xl px-4 h-14 py-1 absolute right-6 top-1/2 -translate-y-1/2"
      >
        <Icon icon="line-md:logout" className="text-3xl text-red-500 inline" />
        Log Out from this device
      </button>

      {deviceLogout && <Modal label="Delete Item" />}
    </div>
  );
};

const DeviceManager = () => {
  const [selectAll, setSelectAll] = useState(false);
  const [selectedDevices, setSelectedDevices] = useState([]);
  const dispatch = useDispatch(); // Initialize dispatch
  const navigate = useNavigate();

  const {
    data: response,
    isLoading,
    isError,
    refetch, 
  } = useFetch({
    endPoint: `get-login-devices`,
  });
  const allDevices = response?.data;

  const handleSelectDevice = (deviceId) => {
    if (selectedDevices.includes(deviceId)) {
      setSelectedDevices(selectedDevices.filter((id) => id !== deviceId));
    } else {
      setSelectedDevices([...selectedDevices, deviceId]);
    }
  };
  const handleSingleDevice = (deviceId) => {
    
    logout([deviceId]);

  };


  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    if (!selectAll) {
      setSelectedDevices(allDevices?.map((device) => device.id) || []);
    } else {
      setSelectedDevices([]);
    }
  };

  const logout = async (ids = []) => {
    if (selectedDevices.length > 0 || ids.length > 0) {
    try {
      const formData = new FormData();
      if (ids.length > 0) {
        formData.append("ids", JSON.stringify(ids));
      } else {
        formData.append("ids", JSON.stringify(selectedDevices));
      }

      // Send the request with FormData
      const response = await api.post("logout-by-device", formData);

        // Check if current device is in the selected list
        const isCurrentDevice = allDevices?.some(
          (device) => device.is_current_device && selectedDevices.includes(device.id)
        );
        if (isCurrentDevice) {
          api.removeTokenHeader();
          dispatch(handleLogout());
          navigate("/");
        }

        await refetch();

        setSelectedDevices([]);
        setSelectAll(false);

    } catch (error) {
      console.error("Error logging out devices:", error);
    }
  }
  };

  return (
    <div className=" py-8 container">
      <GoBack title={"Device Manager"} />

      <div className="flex flex-col items-start gap-5">
        <span
          onClick={handleSelectAll}
          className="flex gap-2 cursor-pointer"
        >
          <input
            type="checkbox"
            checked={selectAll}
            onChange={handleSelectAll}
          />
          Select All
        </span>
        {allDevices?.map((device, idx) => (
          <LoggedInDevice
            key={idx}
            deviceInfo={device}
            handleSelectDevice={handleSelectDevice}
            handleSingleDevice={handleSingleDevice}
            isSelected={selectedDevices.includes(device.id)}
            logout={logout}
          />
        ))}
      </div>

      <button
        onClick={logout}
        className={`mt-8 px-6 py-2 rounded-md ${selectedDevices.length === 0 ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-red-500 text-white'}`}
        disabled={selectedDevices.length === 0}
      >
        Log Out Selected Devices
      </button>

      <div className="flex itesm-center px-10 gap-2 mt-8">
        <Icon
          icon="icon-park-solid:attention"
          className="text-2xl text-sky-600"
        />
        <p className="text-gray-700">
          You have been logged in to {allDevices?.length} devices. You are
          allowed to log in from 6 devices at a time. Please log out from a
          device to log in to a new one.
        </p>
      </div>
    </div>
  );
};

export default DeviceManager;
