import React from "react";
import { useParams } from "react-router-dom";
import useFetch from "@/hooks/useFetch";
import EditorData from "../components/EditorData";
const CustomDynamicPage = () => {
  const { slug } = useParams();

  const {
    data: response,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `page${slug}`,
    endPoint: `page/${slug}`,
  });

  const page = response?.data;
  return (
    <div className="container mx-auto my-16">
      <h1 className="text-3xl font-bold mb-4"> {page?.name}</h1>
      
        {page?.category_items?.map((item, index) => (
        <div key={index}>
            <EditorData htmlData={item.description} />
        </div>
        ))}

    </div>
  );
};

export default CustomDynamicPage;

