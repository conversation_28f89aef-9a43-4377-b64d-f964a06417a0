import React, { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import Loading from "@/components/Loading";
import useFetch from "@/hooks/useFetch";
import QuizForm from "./QuizForm";
import Timer from "./Timer";
import GoBack from "@/components/ui/GoBack";
import examIcon from "@/assets/images/svg/exam.svg";
import { Icon } from "@iconify/react/dist/iconify.js";
import TrueFalseQuestion from "./trueFalseQuestion";
import FillBlank from "./FillBlank";
import Matching from "./Matching";
import Modal from "@/components/ui/Modal";
import api from "@/server/api";

const Quiz = () => {
  const navigate = useNavigate();
  const { courseId, quizId, resultId } = useParams();
  const [showWrittenExamModal, setShowWrittenExamModal] = useState(false);
  const [answers, setAnswers] = useState({
    mcq: {},
    true_false: {},
    fill_in_blank: {},
    matching: {}
  });

  const {
    data: quizdetails,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `quizdetails`,
    endPoint: `quiz-details/${quizId}`,
    params: {
      item_id: courseId,
      item_type: "Course",
    },
  });

  if (quizdetails) {
    if (!localStorage.getItem('startTime')) {
      localStorage.setItem('startTime', new Date().getTime());
      localStorage.setItem(
        'endTime',
        new Date().getTime() + quizdetails?.data?.duration * 60000
      );
    }
  }

  const quizQuestion = quizdetails?.data;

  // Handle answer changes for different question types
  const handleMcqChange = (questionId, optionNum, value) => {
    setAnswers(prev => ({
      ...prev,
      mcq: {
        ...prev.mcq,
        [questionId]: {
          ...prev.mcq[questionId],
          [`answer${optionNum}`]: value
        }
      }
    }));
  };

  const handleTrueFalseChange = (questionId, value) => {
    setAnswers(prev => ({
      ...prev,
      true_false: {
        ...prev.true_false,
        [questionId]: value
      }
    }));
  };

  const handleFillBlankChange = (questionId, index, value) => {
    setAnswers(prev => ({
      ...prev,
      fill_in_blank: {
        ...prev.fill_in_blank,
        [questionId]: {
          ...prev.fill_in_blank[questionId],
          [index]: value
        }
      }
    }));
  };

  const handleMatchingChange = (questionId, questionIndex, answerText) => {
    setAnswers(prev => {
      // Create a copy of the current matching answers for this question
      const currentMatches = { ...(prev.matching[questionId] || {}) };

      // Update the answer for this question index
      if (answerText === undefined) {
        // If answerText is undefined, remove this answer
        delete currentMatches[questionIndex];
      } else {
        // Otherwise, set the answer
        currentMatches[questionIndex] = answerText;
      }

      return {
        ...prev,
        matching: {
          ...prev.matching,
          [questionId]: currentMatches
        }
      };
    });
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    const formattedAnswers = [];

    // Format MCQ answers according to the required API format
    Object.keys(answers?.mcq || {}).forEach(questionId => {
      // Get the current MCQ answer
      const mcqAnswer = answers?.mcq?.[questionId];

      // Find the question item from the API data to get the top-level id
      const questionItem = quizdetails?.data?.items?.find(
        item => item.type === 'mcq' && item.chapter_quiz_question?.id === parseInt(questionId)
      );

      const itemId = questionItem?.id; // This is the top-level id from the API

      // Only add to formatted answers if at least one option is selected
      if (mcqAnswer && Object.values(mcqAnswer).some(val => val === true) && itemId) {
        formattedAnswers.push({
          type: 'mcq',
          id: itemId, // Using the top-level id from the API
          question_id: parseInt(questionId),
          answer1: mcqAnswer.answer1 || false,
          answer2: mcqAnswer.answer2 || false,
          answer3: mcqAnswer.answer3 || false,
          answer4: mcqAnswer.answer4 || false
        });
      }
    });

    // Format True/False answers according to the required API format
    Object.keys(answers?.true_false || {}).forEach(questionId => {
      // Find the question item from the API data to get the top-level id
      const questionItem = quizdetails?.data?.items?.find(
        item => item.type === 'true_false' && item.true_false?.id === parseInt(questionId)
      );

      const itemId = questionItem?.id; // This is the top-level id from the API

      // Only add if the answer is not undefined (user has selected an option)
      if (answers?.true_false?.[questionId] !== undefined && itemId) {
        formattedAnswers.push({
          type: 'true_false',
          id: itemId, // Using the top-level id from the API
          true_false_id: parseInt(questionId),
          answer: answers?.true_false?.[questionId]
        });
      }
    });

    // Format Fill in the Blank answers according to the required API format
    Object.keys(answers?.fill_in_blank || {}).forEach(questionId => {
      const blankAnswers = answers?.fill_in_blank?.[questionId];

      // Find the question item from the API data
      const questionItem = quizdetails?.data?.items?.find(
        item => item.type === 'fill_in_blank' && item.fill_in_the_blank?.id === parseInt(questionId)
      );

      if (blankAnswers && Object.keys(blankAnswers).length > 0 && questionItem) {
        const itemId = questionItem?.id;
        const blankAnswersArray = [];

        // Format each blank answer
        Object.entries(blankAnswers).forEach(([blankIndex, userAnswer]) => {
          // Only add non-empty answers
          if (userAnswer && userAnswer.trim() !== '') {
            const blankAnswer = questionItem?.fill_in_the_blank?.blank_answers?.[parseInt(blankIndex)];

            if (blankAnswer) {
              blankAnswersArray.push({
                blank_answer_id: blankAnswer.id,
                user_answer: userAnswer.trim()
              });
            }
          }
        });

        // Only add to formattedAnswers if we have at least one valid blank answer
        if (blankAnswersArray.length > 0) {
          formattedAnswers.push({
            type: 'fill_in_blank',
            id: itemId,
            fill_in_blank_id: parseInt(questionId),
            blank_answers: blankAnswersArray
          });
        }
      }
    });

    // Format Matching answers according to the required API format
    Object.keys(answers?.matching || {}).forEach(questionId => {
      const matchingAnswers = answers?.matching?.[questionId];

      // Process each matching answer
      if (matchingAnswers && Object.keys(matchingAnswers).length > 0) {
        // Get the matching question from the API data
        const matchingQuestion = quizdetails?.data?.items?.find(
          item => item.type === 'matching' && item.matching?.id === parseInt(questionId)
        );

        // Create an array to hold all matches for this question
        const matches = [];

        // For each question-answer pair
        Object.entries(matchingAnswers).forEach(([questionIndex, selectedAnswer]) => {
          // Get the matching_answer_id from the matching_answers array
          const matchingAnswerId = matchingQuestion?.matching?.matching_answers?.[parseInt(questionIndex)]?.id;

          // Only add if we have a valid answer and matching_answer_id
          if (selectedAnswer && matchingAnswerId) {
            matches.push({
              matching_answer_id: matchingAnswerId,
              selected_right_item: selectedAnswer
            });
          }
        });

        // Only add to formattedAnswers if we have at least one match
        if (matches.length > 0) {
          const matchingData = {
            type: 'matching',
            id: matchingQuestion?.id, // Using the top-level id from the API
            matching_id: parseInt(questionId),
            matches: matches
          };

          formattedAnswers.push(matchingData);
        }
      }
    });

    const submitData = {
      chapter_quiz_id: quizQuestion?.id,
      result_id: resultId,
      answers: formattedAnswers
    };

    // Log the full submission data in a formatted way
    console.log("Submitting data:", submitData);
    console.log("Formatted JSON for backend:", JSON.stringify(submitData, null, 2));

    api.post(`submit-quiz`, submitData)
      .then((response) => {
        console.log(response);
        if (quizQuestion?.written_questions) {
          setShowWrittenExamModal(true);
        } else {
          navigate(`/exam-details/${resultId}`, { replace: true });
        }
      })
      .catch(error => {
        console.error("Error submitting quiz:", error);
      });
  };

  if (isLoading) return <Loading />;
  if (isError) return <div>Error fetching data</div>;

  return (
    <div className="relative">
      <div className="container">
        <GoBack title={quizQuestion?.title} />
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl shadow-md container border border-blue-100">
          <div className="md:flex items-center justify-between w-full mb-3">
            <div className="flex items-center gap-2">
              <div className="bg-white p-2 rounded-full shadow-md">
                <img src={examIcon} alt="" className="h-8 w-8" />
              </div>
              <div>
                <div className="inline-block px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full text-xs font-medium mb-0.5">
                  Quiz
                </div>
                <h4 className="text-lg font-bold text-gray-800">{quizdetails?.data?.title}</h4>
              </div>
            </div>
            <div className="mt-2 md:mt-0 flex items-center gap-2 bg-white px-3 py-2 rounded-lg shadow-md border border-blue-100">
              <Icon icon="mdi:timer-outline" className="text-primary-600 text-xl" />
              <div>
                <p className="text-xs font-medium text-gray-500">Remaining Time</p>
                <Timer duration={quizQuestion?.duration} />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-3 border border-gray-100">
            <h3 className="text-base font-semibold text-primary-600 mb-2 flex items-center">
              <Icon icon="mdi:information-outline" className="mr-1.5 h-5 w-5" />
              Quiz Summary
            </h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
              <div className="bg-blue-50 rounded-lg p-2.5 border border-blue-100 flex items-start">
                <div className="bg-blue-100 p-1.5 rounded-full mr-2">
                  <Icon
                    icon="mdi:help-circle-outline"
                    className="text-blue-600 text-lg"
                  />
                </div>
                <div>
                  <p className="font-medium text-gray-500 text-xs">
                    No. of Questions
                  </p>
                  <span className="font-bold text-base text-gray-800">
                    {quizQuestion?.number_of_question}
                  </span>
                </div>
              </div>

              <div className="bg-green-50 rounded-lg p-2.5 border border-green-100 flex items-start">
                <div className="bg-green-100 p-1.5 rounded-full mr-2">
                  <Icon
                    icon="mdi:plus-circle-outline"
                    className="text-green-600 text-lg"
                  />
                </div>
                <div>
                  <p className="font-medium text-gray-500 text-xs">
                    Positive Mark
                  </p>
                  <span className="font-bold text-base text-gray-800">
                    {quizQuestion?.positive_mark}
                  </span>
                </div>
              </div>

              <div className="bg-yellow-50 rounded-lg p-2.5 border border-yellow-100 flex items-start">
                <div className="bg-yellow-100 p-1.5 rounded-full mr-2">
                  <Icon
                    icon="mdi:star-circle"
                    className="text-yellow-600 text-lg"
                  />
                </div>
                <div>
                  <p className="font-medium text-gray-500 text-xs">
                    Total Marks
                  </p>
                  <span className="font-bold text-base text-gray-800">
                    {quizQuestion?.total_mark}
                  </span>
                </div>
              </div>

              <div className="bg-purple-50 rounded-lg p-2.5 border border-purple-100 flex items-start">
                <div className="bg-purple-100 p-1.5 rounded-full mr-2">
                  <Icon
                    icon="mdi:clock-outline"
                    className="text-purple-600 text-lg"
                  />
                </div>
                <div>
                  <p className="font-medium text-gray-500 text-xs">
                    Total Time
                  </p>
                  <span className="font-bold text-base text-gray-800">
                    {quizQuestion?.duration} Minutes
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="pt-4 border border-gray-200 shadow-lg rounded-xl my-4 bg-white">
          <form onSubmit={handleSubmit} className="space-y-3">
            <div className="space-y-3">
              {quizdetails?.data?.items?.map((item, index) => {
                switch (item?.type) {
                  case "mcq":
                    return (
                      <QuizForm
                        key={index}
                        question={item}
                        onChange={handleMcqChange}
                        answers={answers?.mcq?.[item?.chapter_quiz_question?.id] || {}}
                      />
                    );
                  case "true_false":
                    return (
                      <TrueFalseQuestion
                        key={index}
                        question={item?.true_false}
                        onChange={handleTrueFalseChange}
                        answer={answers?.true_false?.[item?.true_false?.id]}
                      />
                    );
                  case "fill_in_blank":
                    return (
                      <FillBlank
                        key={index}
                        question={item?.fill_in_the_blank}
                        onChange={handleFillBlankChange}
                        answers={answers?.fill_in_blank?.[item?.fill_in_the_blank?.id] || {}}
                      />
                    );
                  case "matching":
                    return (
                      <Matching
                        key={index}
                        question={item?.matching}
                        onChange={handleMatchingChange}
                        matches={answers?.matching?.[item?.matching?.id] || {}}
                      />
                    );
                  default:
                    return null;
                }
              })}
            </div>

            <div className="w-full text-center py-6">
              <button
                type="submit"
                className="px-5 py-2.5 bg-primary-600 text-white rounded-lg shadow-md hover:bg-primary-700 transition-colors duration-300 font-medium flex items-center mx-auto"
              >
                <Icon icon="mdi:check-circle" className="mr-1.5 h-4 w-4" />
                Submit Quiz
              </button>
            </div>
          </form>

          {showWrittenExamModal && (
            <Modal
              activeModal={showWrittenExamModal}
              onClose={() => setShowWrittenExamModal(false)}
            >
              <div>
                <h4 className="text-lg mb-4">
                  Do you want to start written exam? The MCQ Answer will be
                  submitted automatically before start!
                </h4>
                <button
                  onClick={() =>
                    navigate(
                      `/quiz/written-exam/${resultId}/${quizQuestion?.id}/${courseId}`
                    )
                  }
                  className="px-4 py-2 bg-blue-500 text-white rounded-md mr-4"
                >
                  Start Written Exam{" "}
                </button>
                <button
                  onClick={() => (setShowWrittenExamModal(false), navigate('/exam-result'))}
                  className="px-4 py-2 bg-red-500 text-white rounded-md"
                >
                  No
                </button>
              </div>
            </Modal>
          )}
        </div>
      </div>
    </div>
  );
};

export default Quiz;
