import React from "react";
import { ASSET_URL } from "@/config";
import { Icon } from "@iconify/react";

const QuizForm = ({ question, onChange, answers }) => {
  // Get the chapter_quiz_question from the item
  const mcqQuestion = question?.chapter_quiz_question;

  if (!mcqQuestion) {
    return null;
  }

  // Handle checkbox change for MCQ options
  const handleChange = (optionNum) => {
    // Toggle the current answer value
    onChange(mcqQuestion.id, optionNum, !answers[`answer${optionNum}`]);
  };

  return (
    <div className="p-4 mx-3 rounded-xl border border-gray-200 bg-white shadow-md hover:shadow-lg transition-shadow duration-300 mb-4">
      <div className="mb-3 flex items-start">
        <div className="bg-primary-100 p-1.5 rounded-full mr-2 mt-0.5">
          <Icon icon="mdi:help-circle-outline" className="h-4 w-4 text-primary-600" />
        </div>
        <div>
          <div className="inline-block px-2 py-0.5 bg-primary-50 text-primary-700 rounded-full text-xs font-medium mb-1">
            Multiple Choice Question
          </div>
          <h3 className="text-base font-semibold text-gray-800">{mcqQuestion.question_text}</h3>
        </div>
      </div>

      {/* Display question image if available */}
      {mcqQuestion.question_image && (
        <div className="mb-3 rounded-lg overflow-hidden border border-gray-200 shadow-sm">
          <img
            src={ASSET_URL + mcqQuestion.question_image}
            alt="Question"
            className="w-auto h-56 object-contain"
          />
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
        {[1, 2, 3, 4].map((opt, idx) => {
          const isSelected = answers[`answer${opt}`] || false;
          return (
            <div
              key={idx}
              className={`border rounded-lg transition-all duration-200 overflow-hidden ${
                isSelected
                  ? 'border-primary-500 bg-primary-50 shadow-md'
                  : 'border-gray-200 hover:border-primary-300 hover:bg-gray-50'
              }`}
            >
              <label className="flex items-start p-2.5 cursor-pointer">
                <div className={`flex-shrink-0 w-4 h-4 rounded ${
                  isSelected ? 'bg-primary-500' : 'border-2 border-gray-300'
                } mr-2 mt-0.5 flex items-center justify-center transition-colors`}>
                  {isSelected && <Icon icon="mdi:check" className="text-white text-xs" />}
                </div>
                <div className="flex-1">
                  {mcqQuestion?.[`option${opt}_image`] ? (
                    <div className="space-y-2">
                      <div className="rounded-md overflow-hidden border border-gray-200">
                        <img
                          src={ASSET_URL + mcqQuestion?.[`option${opt}_image`]}
                          className="w-auto h-32"
                          alt={`Option ${opt}`}
                        />
                      </div>
                      <span className="text-gray-800 block text-sm">{mcqQuestion[`option${opt}`]}</span>
                    </div>
                  ) : (
                    <span className="text-gray-800 text-sm">{mcqQuestion[`option${opt}`]}</span>
                  )}
                </div>
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={() => handleChange(opt)}
                  className="sr-only" // Hide the actual checkbox but keep it functional
                />
              </label>
            </div>
          );
        })}
      </div>

      <div className="mt-2 text-xs text-gray-500 flex items-center">
        <Icon icon="mdi:information-outline" className="mr-1.5 text-gray-400" />
        <p>Select all correct options. Multiple answers may be correct.</p>
      </div>
    </div>
  );
};

export default QuizForm;
