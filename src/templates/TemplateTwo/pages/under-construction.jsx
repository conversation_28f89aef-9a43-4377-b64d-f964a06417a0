import { Icon } from "@iconify/react/dist/iconify.js";
import React from "react";
import { useNavigate } from "react-router-dom";
import shape from "@/assets/images/svg/shape5.svg";
import icon from "@/assets/images/all-img/construction.png";
import icon2 from "@/assets/images/all-img/construction2.png";

const UnderConstruction = () => {
  const navigate = useNavigate();
  return (
    <div className="flex justify-center min-h-[94vh] xl:min-h-[92vh] bg-indigo-50 text-white relative overflow-hidden">
      <img
        src={shape}
        className="hidden lg:block absolute -left-10 -top-12 opacity-25"
        alt=""
      />
      <img
        src={shape}
        className="hidden lg:block absolute -right-10 -bottom-12 opacity-10 rotate-180"
        alt=""
      />
      <div className="flex items-center justify-center">
        <div className="text-center space-y-4 text-gray-600 p-5">
          {/* <Icon icon="mdi:worker" className="text-gray-400 text-6xl mx-auto" /> */}
          <img className="max-h-52" src={icon2} alt="" />
          <p className="text-xl">This page is</p>
          <h2 className="text-4xl max-sm:text-3xl font-serif font-bold mt-0">
            Under Construction
          </h2>
          <p className="text-lg">
            Stay tuned with EduPack for something amazing!
          </p>
          <div className="mt-6">
            <button
              onClick={() => navigate(-1)}
              className="px-6 py-2 rounded-full border-orange-500 flex items-center gap-1 hover:gap-3 justify-center mx-auto border text-orange-500 hover:bg-orange-500 hover:text-white transition-all duration-200"
            >
              <Icon icon="eva:arrow-back-outline" /> Go Back
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnderConstruction;
