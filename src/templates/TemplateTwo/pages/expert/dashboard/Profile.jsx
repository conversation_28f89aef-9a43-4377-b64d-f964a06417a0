import React from "react";
import { Icon } from "@iconify/react";
import useFetch from "@/hooks/useFetch";
import moment from "moment";
import { BASE_URL } from "@/config";

const Profile = () => {
  const {
    data: mentorProfile,
    isLoading,
    isError,
  } = useFetch({
    queryKey: "mentorProfile",
    endPoint: `${BASE_URL}/profile`,
  });
  console.log(mentorProfile);
  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error fetching data</div>;
  return (
    <>
      <div className="rounded p-3">
        <div className="border-l-4 border-primary-500 rounded ps-2">
          <h6 className="mt-2">Profile Details</h6>
        </div>
        <div className="mt-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Name : {mentorProfile?.data?.user?.name}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                User Type : {mentorProfile?.data?.user?.user_type}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Profession : {mentorProfile?.data?.profession}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Contact Number : {mentorProfile?.data?.contact_no}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Alternative Contact Number :{" "}
                {mentorProfile?.data?.alternative_contact_no}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Mentor Code : {mentorProfile?.data?.mentor_code}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Host Rank Number : {mentorProfile?.data?.host_rank_number}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Batch Certificate Number : {mentorProfile?.data?.birth_certificate_no}
              </p>
            </div>
          </div>
          <div className="grid md:grid-cols-1 gap-4 mt-3">
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">BIO : {mentorProfile?.data?.bio}</p>
            </div>
          </div>
        </div>
      </div>

      {/* //education details  */}

      <div className="bg-zinc- rounded p-3 my-3">
        <div className="border-l-4 border-primary-500 rounded ps-2">
          <h6 className="mt-2">Education Details</h6>
        </div>
        <div className="mt-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Education : {mentorProfile?.data?.education}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Institute : {mentorProfile?.data?.institute}</p>
            </div>
          </div>
        </div>
      </div>

      {/* //account overview */}

      <div className="rounded p-3 my-3">
        <div className="border-l-4 border-primary-500 rounded ps-2">
          <h6 className="mt-2">Account Overview</h6>
        </div>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>User Name : {mentorProfile?.data?.username}</p>
          </div>
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>Email Address : {mentorProfile?.data?.email}</p>
          </div>
          <div className="border p-2 rounded-lg flex justify-start items-start">
            <p className="inline-block my-1">
              Status:{" "}
              <span
                className={`p-2 rounded ${
                  mentorProfile?.data?.is_active
                    ? "bg-green-500 text-white"
                    : "bg-red-500 text-white"
                }`}
              >
                {mentorProfile?.data?.is_active ? "Active" : "Inactive"}
              </span>
            </p>
          </div>
          <div className="border p-2 rounded-lg flex justify-start items-start">
            <p className="inline-block my-1">
              Foreigner:{" "}
              <span
                className={`p-2 rounded ${
                  mentorProfile?.data?.is_foreigner
                    ? "bg-green-500 text-white"
                    : "bg-red-500 text-white"
                }`}
              >
                {mentorProfile?.data?.is_foreigner ? "Yes" : "No"}
              </span>
            </p>
          </div>
          <div className="border p-2 rounded-lg flex justify-start items-start">
            <p className="inline-block my-1">
              Featured:{" "}
              <span
                className={`p-2 rounded ${
                  mentorProfile?.data?.is_featured
                    ? "bg-green-500 text-white"
                    : "bg-red-500 text-white"
                }`}
              >
                {mentorProfile?.data?.is_featured ? "Yes" : "No"}
              </span>
            </p>
          </div>
          <div className="border p-2 rounded-lg flex justify-start items-start">
            <p className="inline-block my-1">
              Host Certificate:{" "}
              <span
                className={`p-2 rounded ${
                  mentorProfile?.data?.is_host_certified
                    ? "bg-green-500 text-white"
                    : "bg-red-500 text-white"
                }`}
              >
                {mentorProfile?.data?.is_host_certified ? "Yes" : "No"}
              </span>
            </p>
          </div>
          <div className="border p-2 rounded-lg flex justify-start items-start">
            <p className="inline-block my-1">
              Host Staff:{" "}
              <span
                className={`p-2 rounded ${
                  mentorProfile?.data?.is_host_staff
                    ? "bg-green-500 text-white"
                    : "bg-red-500 text-white"
                }`}
              >
                {mentorProfile?.data?.is_host_staff ? "Yes" : "No"}
              </span>
            </p>
          </div>
          <div className="border p-2 rounded-lg flex justify-start items-start">
            <p className="inline-block my-1">
              Life Couch:{" "}
              <span
                className={`p-2 rounded ${
                  mentorProfile?.data?.is_life_couch
                    ? "bg-green-500 text-white"
                    : "bg-red-500 text-white"
                }`}
              >
                {mentorProfile?.data?.is_life_couch ? "Yes" : "No"}
              </span>
            </p>
          </div>
        </div>
      </div>

      {/* //personal details */}

      <div className="bg-zinc- rounded p-3 my-3">
        <div className="border-l-4 border-primary-500 rounded ps-2">
          <h6 className="mt-2">Personal Details</h6>
        </div>
        <div className="mt-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Father Name : {mentorProfile?.data?.father_name}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Mother Name : {mentorProfile?.data?.mother_name}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">NID Number : {mentorProfile?.data?.nid_no}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Birth Certification Number :{" "}
                {mentorProfile?.data?.birth_certificate_no}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Passport Number : {mentorProfile?.data?.passport_no}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Date of Birthday : {mentorProfile?.data?.date_of_birth}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Blood Group : {mentorProfile?.data?.blood_group}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Gender : {mentorProfile?.data?.gender}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Religion : {mentorProfile?.data?.religion}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Marital Status : {mentorProfile?.data?.marital_status}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* //address details */}

      <div className="rounded p-3 my-3">
        <div className="border-l-4 border-primary-500 rounded ps-2">
          <h6 className="mt-2">Address Details</h6>
        </div>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>Current Address : {mentorProfile?.data?.current_address}</p>
          </div>
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>Permanent Address : {mentorProfile?.data?.permanent_address}</p>
          </div>
        </div>
      </div>

      {/* //other details */}

      <div className="rounded p-3 my-3">
        <div className="border-l-4 border-primary-500 rounded ps-2">
          <h6 className="mt-2">Other Information</h6>
        </div>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>Device ID : {mentorProfile?.data?.device_id}</p>
          </div>
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>Rating : {mentorProfile?.data?.rating}</p>
          </div>
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>Referral Code : {mentorProfile?.data?.referral_code}</p>
          </div>
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>Referred Code : {mentorProfile?.data?.referred_code}</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Profile;
