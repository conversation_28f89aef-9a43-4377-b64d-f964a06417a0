import React , { useState } from 'react';
import { Icon } from "@iconify/react";
import useFetch from "@/hooks/useFetch";
import Modal from "@/components/ui/Modal";
import moment from 'moment';
import CreateAssignment from './CreateAssignment';
import api from "@/server/api";
const AssignmentList = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const { data: assignmentList, isAssignemtLoading, isError } = useFetch({ queryKey: 'assignmentList', endPoint: 'assignments' });

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error fetching data</div>;


  const publishAssignment = async (id) => {
    try {
        setLoading(true);
        const response = await api.put(`publish-assignment/${id}`, {assignment_id: id});
        
    } catch (error) {
        console.error(error);
    } finally {
        setLoading(false);
    }
  };
  return (
    <div className="overflow-x-auto"> 
      <div className="flex justify-between mb-4">
        <h2 className="text-xl font-bold">Assignment List</h2>
        <button
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          onClick={() => {
            setIsOpen(true);
          }}
        >
          Create Assignment
        </button>
      </div>
      {isOpen && (
        <Modal activeModal={isOpen} onClose={() => setIsOpen(false)} className='max-w-5xl' title='Create Assignment'>
          
          <CreateAssignment setIsOpen={setIsOpen} />
        </Modal>
      )}
      <table className="min-w-full bg-white border">
        <thead>
          <tr>
            <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">Assignment</th>
            <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">Course</th>
            <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">Deadline</th>
            <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">Publish Date</th>
            <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">Status</th>
            <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">Action</th>
          </tr>
        </thead>
        <tbody>
          {assignmentList?.data?.map(item => (
            <tr key={item.id}>
              <td className="py-4 px-4 border-b border-gray-200">{item.title}</td>
              <td className="py-4 px-4 border-b border-gray-200">{item.course_title}</td>
              <td className="py-4 px-4 border-b border-gray-200">{moment(item.deadline).format('MMMM Do YYYY')}</td>
              <td className="py-4 px-4 border-b border-gray-200">{moment(item.publish_date).format('MMMM Do YYYY')}</td>
              <td className="py-4 px-4 border-b border-gray-200">
                {item.status == 'Unpublished' ? 
                <button onClick={() => {publishAssignment(item.id)}} className="btn btn-primary">Publish</button>
                // <span className="text-red-500">Unpublished</span>
                : 
                <span className="text-green-500">Published</span>
                }
              </td>
              <td className="py-4 px-4 border-b border-gray-200">
                <button className="text-blue-500">Details</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default AssignmentList;      