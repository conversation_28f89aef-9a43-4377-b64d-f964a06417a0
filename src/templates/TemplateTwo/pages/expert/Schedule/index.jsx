import React, { useState } from 'react';
import { Icon } from "@iconify/react";
import useFetch from '@/hooks/useFetch';
import { useParams } from "react-router-dom";
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import moment from 'moment';
import Modal from "@/components/ui/Modal";
import DateTimePicker from '@/components/form/DateTimePicker';
import api from "@/server/api";

const Schedule = () => {
    const [loading, setLoading] = useState(false);
    const [selectedMenuItem, setSelectedMenuItem] = useState('MyCourseList');
    const [isOpen, setIsOpen] = useState(false);
    const {id} = useParams();
    const { data: expertProfile, isLoading, isError } = useFetch({ queryKey: 'expertProfile', endPoint: 'profile' });
    const { data: scheduleList, isSheduleLoading, isScheduleError } = useFetch({ queryKey: 'scheduleList', endPoint: 'mentor-schedule-list/' + id });
   
    const { data: studentInfo, isStudentLoading, isStudentError } = useFetch({ queryKey: 'studentInfo', endPoint: 'student-details-by-mapping-id/' + id });

    if (isLoading) return <div>Loading...</div>;
    if (isError) return <div>Error fetching data</div>;

    const validationSchema = Yup.object().shape({
        schedule_date: Yup.date().required('Publish Date is required'),
    });
    const handleSubmit = async (values, { setSubmitting }) => {
        // Handle form submission here
        setLoading(true);

        console.log(values);
        try {
   
            const response = await api.post('add-new-schedule', values);
            setIsOpen(false);
        } catch (error) {
            console.error(error);
        } finally {
            setSubmitting(false);
            setLoading(false);
        }
    };
    return (
        <div className='grid grid-cols-3 container gap-10'>
            <div>
                <div className="flex flex-col items-center p-4 border bg-white py-10">
                    <img
                        src={expertProfile?.data?.image ? expertProfile?.data?.image : 'https://picsum.photos/200'}
                        alt="Profile"
                        className="rounded-full border-4 border-gray-300 mb-4 w-32 h-32"
                    />
                    <div className="flex items-center mb-2">
                        <Icon icon="mdi:account" className="h-5 w-5 text-gray-700 mr-2" />
                        <span className="text-base font-semibold">{expertProfile?.data?.name}</span>
                    </div>
                    <div className="flex items-center mb-2">
                        <Icon icon="mdi:phone" className="h-5 w-5 text-gray-700 mr-2" />
                        <span className="text-base">{expertProfile?.data?.contact_no}</span>
                    </div>
                    <div className="flex items-center">
                        <Icon icon="mdi:briefcase" className="h-5 w-5 text-gray-700 mr-2" />
                        <span className="text-base">{expertProfile?.data?.user_type}</span>
                    </div>
                </div>

                <div className="mt-4 bg-white">
                    <ul>
                        <li> <b> Student Name:</b> {studentInfo?.data.name} </li>
                        <li className='mt-2'> <b> Contact No:</b> {studentInfo?.data.contact_no} </li>
                        <li className='mt-2'> <b> Email:</b> {studentInfo?.data.email} </li>
                    </ul>
                </div>
            </div>
            <div className="col-span-2">
            <button
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          onClick={() => {
            setIsOpen(true);
          }}
        >
          Create Schedule
        </button>
        {isOpen && (
        <Modal activeModal={isOpen} onClose={() => setIsOpen(false)} className='max-w-xl' title='Create Scedule'>


            <Formik
                initialValues={
                    {
                        mapping_id: id,
                        schedule_date: ''
                    }
                }
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
            >
            {({ setFieldValue, values }) => (
                <Form>
                    
                    <div className="grid grid-cols-1 gap-4">
                    <DateTimePicker
                                label="Schedule Date"
                                name="schedule_date"
                                required
                            />
                    </div>
                        <div className="mt-6">
                            <button
                                type="submit"
                                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                            >
                                Create Schedule
                            </button>
                        </div>
                </Form>
            )}
            </Formik>
        </Modal>
        )}
                <div className="overflow-x-auto"> 
                <table className="min-w-full bg-white border">
                    <thead>
                    <tr>
                        <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">Course</th>
                        <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">Date</th>
                        <th className="py-4 px-4 border-b border-gray-200 bg-gray-100 text-left text-sm font-semibold text-gray-700">Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    {scheduleList?.data.map(item => (
                        <tr key={item.id}>
                        <td className="py-4 px-4 border-b border-gray-200">{item.course_title}</td>
                        <td className="py-4 px-4 border-b border-gray-200">{moment(item.schedule_datetime).format('MMMM Do YYYY, h:mm a')}</td>
                        <td className="py-4 px-4 border-b border-gray-200">
                            {item.join_link && 
                            <a href={item.join_link} target="_blank" className="text-blue-500" rel="noopener noreferrer">Join Class</a>
                            }
                        </td>
                        
                        </tr>
                    ))}
                    </tbody>
                </table>
                </div>
            </div>
        </div>
    );
}

export default Schedule;