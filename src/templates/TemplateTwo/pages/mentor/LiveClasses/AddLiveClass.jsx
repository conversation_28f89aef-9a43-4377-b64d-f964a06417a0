import React, { useEffect, useState } from "react";
import Modal from "@/components/ui/Modal";
import { Formik, Form, ErrorMessage } from "formik";
import Button from "@/components/ui/Button";
import Select from "react-select";
import InputField from "@/components/ui/InputField";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import MultiSelectComponent from "@/components/ui/MultiSelectComponent";
import useFetch from "@/hooks/useFetch";
import api from "@/server/api";
import { useDispatch } from "react-redux";
import * as Yup from "yup";
import DateTimePicker from "@/components/form/DateTimePicker";
import { useQueryClient } from "@tanstack/react-query";

const AddLiveClass = ({ showModal, setShowModal }) => {
  const dispatch = useDispatch();
  const [courseId, setCourseId] = useState(null);
  const [batchList, setBatchList] = useState([]);

  const queryClient = useQueryClient();

  // Fetch course list
  const { data: courseList } = useFetch({
    queryKey: `mentor-course-list-for-filter`,
    endPoint: `mentor-course-list-for-filter`,
  });

  // Fetch students based on courseId
  // const { data: students, isStudentLoading } = useFetch(
  //   courseId
  //     ? {
  //         queryKey: `mentor/student-list?course_id=${courseId}`,
  //         endPoint: `mentor/student-list?course_id=${courseId}`,
  //       }
  //     : {
  //         queryKey: ``,
  //         endPoint: ``,
  //       }
  // );

  // useEffect(() => {
  //   if (students?.data) {
  //     setStudentList(students.data);
  //   }
  // }, [students]);

  const { data: batches, isLoading: isBatchLoading } = useFetch(
    courseId
      ? {
          queryKey: `/mentor/batch-list?course_id=${courseId}`,
          endPoint: `/mentor/batch-list?course_id=${courseId}`,
        }
      : {
          queryKey: ``,
          endPoint: ``,
        }
  );

  useEffect(() => {
    if (batches?.data) {
      setBatchList(batches.data);
    }
  }, [batches]);

  // console.log(studentList);
  // console.log(
  //   studentList?.map((student) => ({
  //     label: student.name,
  //     value: student.id,
  //   }))
  // );
  const validationSchema = Yup.object({
    title: Yup.string()
      .required("Assignment title is required")
      .max(100, "Title cannot exceed 100 characters"),
    course_id: Yup.string().required("Please select a course"),
    schedule_datetime: Yup.date().required("Class date & time is required"), // No need to validate format here since we handle it in `handleSubmit`
    duration: Yup.string().required("Duration is required"),
    class_url: Yup.string().required("Class URL is required"),
    // student_ids: Yup.array()
    //   .min(1, "Select at least one student")
    //   .required("Students selection is required"),
    batch_id: Yup.string().required("Batch are required"),
  });

  const handleSubmit = async (values) => {
    try {
      // Make API call
      const response = await api.post(
        "mentor/create-live-class-schedule",
        values
      );
      if (response.data.status) {
        queryClient.invalidateQueries("live-class-list");
        dispatch(setShowModal(false));
      }
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  return (
    <div>
      <Modal
        activeModal={showModal}
        onClose={() => dispatch(setShowModal(false))}
        title="Add Live Class"
        className="max-w-4xl"
        footer={
          <Button
            text="Close"
            btnClass="btn-primary"
            onClick={() => dispatch(setShowModal(false))}
          />
        }
      >
        <Formik
          initialValues={{
            title: "",
            course_id: "",
            schedule_datetime: "",
            duration: "",
            class_url: "",
            // student_ids: [], // Updated to track selected student IDs
            batch_id: "",
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, setFieldValue }) => (
            <Form>
              {/* {console.log(values)} */}
              <div className="space-y-3">
                <div className="lg:flex justify-between gap-4 w-full">
                  <div className="w-full">
                    <InputField
                      label="Class Title"
                      name="title"
                      type="text"
                      placeholder="Live Class Name"
                      required
                    />
                  </div>
                  <div className="w-full">
                    <label className="block text-gray-600 text-sm font-medium mb-2">
                      Course
                      <span className="text-red-500"> *</span>
                    </label>
                    <Select
                      placeholder="Select Course"
                      options={courseList?.data?.map((course) => ({
                        value: course.id,
                        label: course.title,
                      }))}
                      name="course_id"
                      onChange={(e) => {
                        console.log(e.value);
                        setCourseId(e.value);
                        setFieldValue("course_id", e.value);
                      }}
                    />

                    <ErrorMessage name="course_id">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>

                  <div className="w-full">
                    <label className="block text-gray-600 text-sm font-medium mb-2">
                      Select Batch
                      <span className="text-red-500">*</span>
                    </label>
                    <Select
                      placeholder="Select Batch"
                      options={batchList?.map((batch) => ({
                        value: batch.id,
                        label: batch.name,
                      }))}
                      name="batch_id"
                      onChange={(e) => {
                        console.log(e.value);
                        // setCourseId(e.value);
                        setFieldValue("batch_id", e.value);
                      }}
                    />
                    <ErrorMessage name="batch_id">
                      {(msg) => (
                        <div className="text-red-500 text-sm mt-1">{msg}</div>
                      )}
                    </ErrorMessage>
                  </div>
                </div>
                {/* <div className="mt-3">
                  <label className="block text-gray-600 text-sm font-medium mb-2">
                    Select Students
                    <span className="text-red-500"> *</span>
                  </label>
                  <MultiSelectComponent
                    name="student_ids"
                    placeholder="Select Students"
                    options={studentList?.map((student) => ({
                      label: student.name,
                      value: student.id,
                    }))}
                    valueKey="value"
                    labelKey="label"
                  />
                </div> */}

                <div className="lg:flex justify-between gap-4">
                  <div className="w-full">
                    <InputField
                      label="Duration"
                      name="duration"
                      type="text"
                      placeholder="Class Duration"
                      required
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label="Class URL"
                      name="class_url"
                      type="text"
                      placeholder="Class URL"
                      required
                    />
                  </div>
                  <div className="w-full">
                    <DateTimePicker
                      time={true}
                      label="Class Date & Time"
                      placeholder="YYYY-MM-DD"
                      format="YYYY/MM/DD"
                      name="schedule_datetime"
                      onChange={(e) => {
                        setFieldValue("schedule_datetime", e[0]);
                      }}
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="flex justify-end mt-4">
                <Button text="Submit" type="submit" btnClass="btn-primary" />
              </div>
            </Form>
          )}
        </Formik>
      </Modal>
    </div>
  );
};

export default AddLiveClass;
