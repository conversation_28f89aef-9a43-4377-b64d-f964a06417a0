import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { Icon } from "@iconify/react";
import useFetch from "@/hooks/useFetch";
import Loading from "@/components/Loading";
import { Formik, Form, Field } from "formik";
import api from "@/server/api";
import { ASSET_URL } from "@/config";
import * as Yup from "yup";
// import InputField from "@/components/ui/InputField";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import ImageModal from "@/components/ui/ImageModal";
import InputField from "@/components/ui/InputField";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

const AnswerDetails = () => {
  const { id } = useParams(); // Get assignment ID from route parameters
  const [submitting, setSubmitting] = useState(false);
  const [modalImage, setModalImage] = useState(null); // State for modal image
  const [isOpenModal, setIsOpenModal] = useState(false);
  const [showSubmit, setShowSubmit] = useState(false);
  const navigate = useNavigate();
  // Fetch assignment details
  const {
    data: assignment,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `submission-details/${id}`,
    endPoint: `mentor/submission-details?id=${id}`,
  });

  const attachmentFiles = assignment?.data?.assignment_attachments;
  const assignmentData = assignment?.data?.assignment;

  // Handle modal opening
  const openModal = (imagePath) => {
    setIsOpenModal(true);
    setModalImage(imagePath); // Set the image path in state to display in modal
  };

  // Handle modal closing
  const closeModal = () => {
    setIsOpenModal(false);
    setModalImage(null); // Close the modal by clearing the state
  };

  // Handle assignment submission
  const handleSubmit = async (values) => {
    setSubmitting(true);

    const formData = new FormData();
    formData.append("assignment_id", assignment?.data.id);
    formData.append("course_id", assignment?.data.course_id);
    formData.append("answer", values.answer);

    // Append files to formData
    values.attachment_files.forEach((file) => {
      formData.append("attachment_files[]", file); // Use the same key for multiple files
    });

    try {
      const response = await api.filepost("submit-assignment", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      if (response.status === 200) {
        console.log(response.data);
        window.location.reload();
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleMarkSubmit = async (values) => {
    try {
      const response = await api.post("mentor/mark-assignment", values);

      if (response.status === 200) {
        // toast.success("Mark added successfully!");
        setShowSubmit(false);
        // navigate(-1); // Navigate to the previous page
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to submit marks. Please try again.");
    }
  };

  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomStyle, setZoomStyle] = useState({
    transform: "scale(1)", // Initial scale is 1 (no zoom)
    transformOrigin: "center center", // Initial zoom origin is the center
    cursor: "zoom-in", // Cursor is zoom-in initially
  });
  const [lastClickPoint, setLastClickPoint] = useState({ x: 50, y: 50 }); // Store last click point as percentages

  const handleClick = (e) => {
    const { offsetX, offsetY, target } = e.nativeEvent;
    const { width, height } = target.getBoundingClientRect();

    const x = (offsetX / width) * 100;
    const y = (offsetY / height) * 100;

    if (!isZoomed) {
      setIsZoomed(true);
      setLastClickPoint({ x, y }); // Store click position

      setZoomStyle({
        transform: "scale(2)", // Zoom in to 2x
        transformOrigin: `${x}% ${y}%`, // Zoom to the clicked point
        cursor: "zoom-out", // Change cursor to zoom-out
      });
    } else {
      setIsZoomed(false);
      setZoomStyle({
        transform: "scale(1)", // Reset zoom
        transformOrigin: `${lastClickPoint.x}% ${lastClickPoint.y}%`, // Zoom out from the last clicked point
        cursor: "zoom-in", // Reset cursor to zoom-in
      });
    }
  };

  return (
    <section className="space-y-5 container mt-12">
      {isLoading ? (
        <Loading />
      ) : isError ? (
        <p className="text-red-500">Failed to load assignment details.</p>
      ) : (
        <div className="flex flex-col gap-2">
          <h1 className="text-xl font-bold text-sky-700">
            <b>Title : </b> {assignmentData?.title}
          </h1>
          <p className="text-md text-gray-700">
            {assignmentData?.description ||
              `
              Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum
            `}
          </p>

          {assignmentData?.supporting_doc && (
            <iframe
              src={ASSET_URL + assignmentData?.supporting_doc}
              className="w-full h-96"
              title="Supporting Document"
            />
          )}

          <p className="text-sm text-gray-600">
            <strong>Publish Date:</strong>{" "}
            {new Date(assignmentData.publish_date).toLocaleDateString()}
          </p>
          <p className="text-sm text-gray-600">
            <strong>Deadline:</strong>{" "}
            {new Date(assignmentData.deadline).toLocaleDateString()}
          </p>

          {assignment?.data.assignment_attachments === null ? (
            <Formik
              validationSchema={Yup.object().shape({
                answer: Yup.string(),
                attachment_files: Yup.array().min(
                  1,
                  "At least one file is required"
                ),
              })}
              initialValues={{
                answer: "",
                attachment_files: [],
              }}
              onSubmit={handleSubmit}
            >
              {({ setFieldValue, values, errors, touched }) => (
                <Form className="bg-white p-6 rounded-lg shadow-md flex flex-col gap-6">
                  {/* Answer Field */}
                  <Field
                    as="textarea"
                    name="answer"
                    className="w-full p-2 border rounded-lg shadow-sm resize-none"
                    rows={10}
                    placeholder="Type your answer here..."
                  />
                  {errors.answer && touched.answer && (
                    <div className="text-red-500">{errors.answer}</div>
                  )}

                  {/* Preview Section */}
                  {values.attachment_files.length > 0 && (
                    <div className="grid grid-cols-6 md:grid-cols-8 gap-4 mt-4">
                      {values.attachment_files.map((file, index) => (
                        <div
                          key={index}
                          className="relative border rounded-lg p-2 shadow-sm"
                        >
                          {file.type.startsWith("image/") ? (
                            <img
                              src={URL.createObjectURL(file)}
                              alt={`Preview ${index}`}
                              className="max-w-full h-auto rounded"
                            />
                          ) : (
                            <div className="flex flex-col items-center">
                              <Icon
                                icon="mdi:file-document-outline"
                                className="text-gray-500 text-4xl"
                              />
                              <p className="text-sm text-gray-600 mt-2">
                                {file.name}
                              </p>
                            </div>
                          )}
                          <button
                            type="button"
                            onClick={() =>
                              setFieldValue(
                                "attachment_files",
                                values.attachment_files.filter(
                                  (_, fileIndex) => fileIndex !== index
                                )
                              )
                            }
                            className="absolute top-1 right-1 bg-red-600 text-white rounded-full p-1"
                          >
                            <Icon icon="mdi:close" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="flex flex-col gap-2">
                    <label
                      className="text-gray-700 font-semibold text-lg"
                      htmlFor="files"
                    >
                      Attach Assignment Files
                    </label>
                    <div className="border border-dashed border-gray-400 rounded-lg p-4">
                      <input
                        id="files"
                        type="file"
                        name="attachment_files"
                        multiple
                        accept=".jpg,.jpeg,.png,.pdf"
                        onChange={(e) =>
                          setFieldValue(
                            "attachment_files",
                            Array.from(e.target.files)
                          )
                        }
                        className="hidden"
                      />
                      <label
                        htmlFor="files"
                        className="cursor-pointer px-4 py-2 bg-sky-700 text-white rounded-md hover:bg-sky-600 transition-colors flex justify-center items-center"
                      >
                        <Icon icon="mdi:upload" className="text-xl mr-2" />
                        Choose Files
                      </label>
                    </div>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-sky-700 text-white py-2 px-4 rounded hover:bg-sky-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
                    disabled={
                      submitting || values.attachment_files.length === 0
                    }
                  >
                    {submitting ? "Submitting..." : "Submit Assignment"}
                  </button>
                </Form>
              )}
            </Formik>
          ) : (
            <div>
              <h3 className="my-2">Answer: </h3>

              {assignment?.data.assignment_attachments?.answer && (
                <p className="text-sm text-gray-600">
                  {assignment?.data.assignment_attachments.answer}
                </p>
              )}

              {attachmentFiles?.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold">Submitted Images:</h3>
                  <div className="flex flex-wrap gap-5 mt-4">
                    {attachmentFiles?.map((attachment, index) => (
                      <div key={attachment.id} className="relative">
                        <img
                          src={
                            attachment?.file ? ASSET_URL + attachment.file : ""
                          }
                          alt={`Submitted ${index}`}
                          className="w-36 h-32 object-cover rounded-md cursor-pointer"
                          onClick={() => openModal(ASSET_URL + attachment.file)}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Modal to display the image */}
              {modalImage && (
                <ImageModal
                  activeModal={isOpenModal}
                  onClose={() => closeModal()}
                  className="max-w-4xl"
                >
                  <div className="rounded-md">
                    <button
                      onClick={closeModal}
                      className="absolute -top-7 -right-7 text-white bg-red-600 p-2 rounded-full m-8 z-10"
                    >
                      <Icon icon="mdi:close" className="text-xl" />
                    </button>
                    <img
                      src={modalImage}
                      alt="Modal Image"
                      className=" max-w-full max-h-screen object-contain transition-transform duration-500 ease-in-out"
                      //   className="transition-transform duration-500 ease-in-out max-w-full rounded-md opacity-100"
                      onClick={handleClick} // Trigger zoom on click
                      style={{
                        width: "100%",
                        maxHeight: "800px",
                        ...zoomStyle,
                        opacity: 1, // Ensure opacity is always 1
                        transition:
                          "opacity 0.5s ease-in-out, transform 0.5s ease-in-out", // Smooth transition for opacity and transform
                      }}
                    />
                  </div>
                </ImageModal>
              )}
            </div>
          )}

          <div className="mt-5">
            <Formik
              initialValues={{
                submission_id: id,
                marks: assignment?.data?.marks || "", // Set empty string if marks is null/undefined
                remarks: assignment?.data?.remarks || "", // Set empty string if remarks is null/undefined
              }}
              onSubmit={handleMarkSubmit}
            >
              {({ setFieldValue }) => (
                <Form className="flex flex-col gap-4 justify-end max-w-sm">
                  <InputField
                    name="marks"
                    label="Marks"
                    type="number"
                    placeholder="Enter Mark"
                    disabled={!showSubmit}
                  />
                  <div className="flex flex-col gap-2">
                    <label htmlFor="remarks" className="text-sm font-semibold">
                      Remarks
                    </label>
                    <textarea
                      name="remarks"
                      label="Re-Mark"
                      type="text"
                      className="border border-gray-300 p-3 h-20 rounded-lg"
                      placeholder="Re-Mark"
                      disabled={!showSubmit}
                      defaultValue={assignment?.data?.remarks || ""}
                      onChange={(e) => setFieldValue("remarks", e.target.value)}
                    />
                  </div>
                  {!showSubmit ? (
                    <button
                      onClick={() => setShowSubmit(true)}
                      className="font-semibold border text-md py-2 rounded-lg ring:border-blue-300 focus:border-blue-300"
                    >
                      Update
                    </button>
                  ) : (
                    <Button type="submit">Submit</Button>
                  )}
                </Form>
              )}
            </Formik>
          </div>
        </div>
      )}
    </section>
  );
};

export default AnswerDetails;
