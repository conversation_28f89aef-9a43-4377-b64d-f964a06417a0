import React, { useEffect, useState } from "react";
import Modal from "@/components/ui/Modal";
import { useDispatch } from "react-redux";
import Button from "@/components/ui/Button";
import { Form, Formik } from "formik";
import Textinput from "@/components/ui/Textinput";
import InputSelect from "@/components/form/InputSelect";
import Fileinput from "@/components/ui/Fileinput";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import useFetch from "@/hooks/useFetch";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import { useSelector } from "react-redux";
import { data } from "autoprefixer";
import api from '@/server/api';
import MultiSelectComponent from "@/components/ui/MultiSelectComponent";

const EditAssignment = ({ editData, showEditModal, setShowEditModal }) => {
  const dispatch = useDispatch();

  const { data: courseList } = useFetch({
    queryKey: `mentor-course-list-for-filter`,
    endPoint: `mentor-course-list-for-filter`,
  });
  
  const { data: studentList, isStudentLoading } = useFetch({
    queryKey: `mentor-student-list-for-filter`,
    endPoint: `/mentor/student-list?course_id=${editData?.course_id}`,
  });

  // const initialValues = {
  //   title: editData?.title || "",
  //   course_id: editData?.course_id || "",
  //   deadline: editData?.deadline || "",
  //   assignment_mark: editData?.assignment_mark || "",
  //   supporting_doc: editData?.supporting_doc || null,
  //   student: editData?.student || "",
  // };


  const handleSubmit = async (values) => {    
    const formData = new FormData();
    formData.append("title", values.title);
    formData.append("course_id", values.course_id);
    formData.append("deadline", values.deadline);
    formData.append("mark", values.mark);
    formData.append("supporting_doc", values.supporting_doc);

    console.log(values)
    const response = await api.fileput(
      import.meta.env.VITE_BASE_URL + "mentor/update-assignment/" + editData.id,
      formData
    );
    console.log(response)
  };

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setShowEditModal(false))}
      title="Edit Assignment"
      className="max-w-4xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowEditModal(false))}
        />
      }
    >
      <Formik
        initialValues={editData}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ values, setFieldValue }) => (
          <Form className="space-y-3">
            <div className="lg:flex justify-between w-full gap-4">
              <div className="w-full">
                <Textinput
                  defaultValue={values.title}
                  label="Assignment Title"
                  name="title"
                  type="text"
                  placeholder="Enter assignment title"
                  onChange={(e) => setFieldValue("title", e.target.value)}
                  required
                />
              </div>

              <div className="w-full">
                <InputSelect
                  label="Select Course"
                  placeholder="Select Course"
                  options={courseList?.data?.map((course) => ({
                    value: course.id,
                    label: course.title,
                  }))}
                  name="course_id"
                  defaultValue={values.course_id}
                  onChange={(e) => setFieldValue("course_id", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="lg:flex justify-between w-full gap-4">
              <div className="w-full">
                <DatePicker
                  label="Deadline"
                  placeholder="YYYY-MM-DD"
                  format="YYYY/MM/DD"
                  name="deadline"
                  defaultValue={values.deadline}
                  onChange={(date) => setFieldValue("deadline", date)}
                  required
                />
              </div>

              <div className="w-full">
                <Textinput
                  label="Total Mark"
                  name="mark"
                  type="text"
                  placeholder="Enter assignment mark"
                  defaultValue={values.mark}
                  onChange={(e) => setFieldValue("mark", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="w-full">
              <Fileinput
                name="supporting_doc"
                accept=".pdf"
                type="file"
                placeholder="Select Document"
                title="Question (Document)"
                selectedFile={values.supporting_doc}
                showLink = "link"
                onChange={(e) => {
                  setFieldValue("supporting_doc", e.target.files[0]);
                }}
                setFieldValue={setFieldValue}
                required
              />
            </div>

            <div className="w-full">
              <MultiSelectComponent
                label="Student"
                name="student"
                placeholder="Select Student"
                options={studentList?.data?.map((student) => ({
                  label: student.name,
                  value: student.id,
                }))}
                valueKey="value"
                labelKey="label"
                onChange={(e) => console.log(e.target)}
                required
              />
            </div>

            <div className="flex justify-end">
              <Button type="submit" className="btn text-center btn-primary">
                Submit
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditAssignment;
