import React , { useState } from "react";
import { ASSET_URL } from "@/config";
import avatar from "@/assets/images/avatar/av-1.svg";
import EditAssignment from "./EditAssignment";
import Badge from "@/components/ui/Badge";
import useFetch from "@/hooks/useFetch";
import Loading from "@/components/Loading";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Breadcrumbs from "@/components/ui/Breadcrumbs";
import bgShape from "@/assets/images/svg/shape2.svg";
import bgShape2 from "@/assets/images/svg/shape3.svg";
import { useDispatch } from "react-redux";
import DeleteAssignment from "./DeleteAssignment";
import CreateAssignment from "./CreateAssignment";
import { useSelector } from "react-redux";
import {
  setDeleteData,
  setDeleteModalShow,
  setEditData,
  setShowEditModal,
} from "@/store/assignmentStore";
import { useNavigate } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";

const AssignmentsList = () => {
  const { showDeleteModal, editData, deleteData, showEditModal } = useSelector(
    (state) => state.assignmentSlice
  );
  const dispatch = useDispatch();
    const [showModal, setShowModal] = useState(false);
  //   const [apiParam, setApiParam] = useState("");
  const navigate = useNavigate();

  //   console.log(showDeleteModal);
  const {
    data: assignmentList,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `assignment-list`,
    endPoint: `/mentor/assignment-list`,
  });
 const queryClient = useQueryClient();
  //   const res = useGetMentorListQuery(apiParam);
  //   const changePage = (val) => {
  //     setApiParam(val);
  //   };

  const data = assignmentList?.data;
  const columns = [
    {
      label: "#",
      field: "serial",
    },
    {
      label: "Assignment Title",
      field: "title",
    },
    {
      label: "Course Title",
      field: "course_title",
    },
    {
      label: "Student no",
      field: "student_assignments_count",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Deadline",
      field: "deadline",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const tableData = data?.map((item, index) => {
    return {
      //   name: (
      //     <button
      //       type="button"
      //       onClick={() => console.log(item.id)}
      //       className="flex items-center bg-transparent border-none cursor-pointer p-0"
      //     >
      //       <img
      //         src={item.image ? ASSET_URL + item.image : avatar}
      //         className="rounded-full w-8 h-8 mr-2"
      //         alt="avatar"
      //       />
      //       <span className="hover:text-primary-500 hover:underline">
      //         {item.name}
      //       </span>
      //     </button>
      //   ),
      serial: <p className="font-semibold">{index + 1}</p>,
      title: <p>{item.title}</p>,
      course_title: <p>{item.course_title}</p>,
      student_assignments_count: <p>{item.student_assignments_count}</p>,
      status: <div>{item.status}</div>,
      deadline: (
        <div>
          {new Date(item.deadline).toLocaleString("en-US", {
            day: "numeric",
            month: "short",
            year: "numeric",
          })}
        </div>
      ),
      //   status: (
      //     <Badge
      //       className={
      //         !item.is_active
      //           ? `bg-danger-500 text-white`
      //           : `bg-success-500 text-white`
      //       }
      //     >
      //       {item.is_active ? "Active" : "Pending"}
      //     </Badge>
      //   ),
    };
  });

  const actions = [
    {
      name: "Details",
      icon: "lsicon:view-outline",
      onClick: (val) => {
        navigate(`/assignment-details/${data[val].id}`)
        console.log(val);
        // dispatch(setEditData(data[val]));
        // dispatch(setShowEditModal(true));
      },
    },
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        console.log(val);
        dispatch(setEditData(data[val]));
        dispatch(setShowEditModal(true));
      },
    },
    {
      name: "delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        console.log(data[val]);
        dispatch(setDeleteData(data[val]));
        dispatch(setDeleteModalShow(true));
      },
    },
  ];

  const handleSubmit = () => {
    setShowModal(false);
  };

  const createPage = <CreateAssignment />;
  const editPage = <EditAssignment />;

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div
      className="container mt-5"
      style={{ minHeight: "calc(70vh - 5rem)" }}
    >
      <img className="absolute left-0 z-0" src={bgShape} alt="" />
      <img className="absolute right-0 z-0" src={bgShape2} alt="" />
      {/* <Breadcrumbs /> */}
      <div className="relative">
        <BasicTablePage
          title="Assignment List"
          createButton="Create New Assignment"
          editPage={editPage}
          actions={actions}
          columns={columns}
          data={tableData}
          openCreateModal={() => setShowModal(true)}
          // changePage={changePage}
          currentPage={data?.current_page}
          submitForm={handleSubmit}
          totalPages={Math.ceil(data?.total / data?.per_page)}
          // filter={filter}
          // setFilter={setApiParam}
        />

        <DeleteAssignment
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setDeleteModalShow}
          data={deleteData}
        />
        {showModal && 
          <CreateAssignment showModal={showModal} setShowModal={setShowModal} />
        }
        {showEditModal && 
          <EditAssignment editData={editData} showEditModal={showEditModal} setShowEditModal={setShowEditModal} />
         }
      </div>
    </div>
  );
};

export default AssignmentsList;
