import React, { useState } from "react";
import { Link, useParams } from "react-router-dom";
import { Icon } from "@iconify/react";
import useFetch from "@/hooks/useFetch";
import Loading from "@/components/Loading";
import { Formik, Form, Field } from "formik";
import api from "@/server/api";
import { ASSET_URL } from "@/config";
import * as Yup from "yup";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import avatar from "@/assets/images/avatar/av-1.svg";

const AssignmentDetails = () => {
  const { id } = useParams(); // Get assignment ID from route parameters
  const [submitting, setSubmitting] = useState(false);
  const [modalImage, setModalImage] = useState(null); // State for modal image

  // Fetch assignment details
  const {
    data: assignment,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `assignments/${id}`,
    endPoint: `mentor/assignments-details?id=${id}`,
  });

  // Handle modal opening
  const openModal = (imagePath) => {
    setModalImage(imagePath); // Set the image path in state to display in modal
  };

  // Handle modal closing
  const closeModal = () => {
    setModalImage(null); // Close the modal by clearing the state
  };

  // Handle assignment submission
  const handleSubmit = async (values) => {
    setSubmitting(true);

    const formData = new FormData();
    formData.append("assignment_id", assignment?.data.id);
    formData.append("course_id", assignment?.data.course_id);
    formData.append("answer", values?.answer);

    // Append files to formData
    values.attachment_files.forEach((file) => {
      formData.append("attachment_files[]", file); // Use the same key for multiple files
    });

    try {
      const response = await api.filepost("submit-assignment", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      if (response.status === 200) {
        console.log(response.data);
        window.location.reload();
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSubmitting(false);
    }
  };

  console.log(assignment);

  const columns = [
    {
      label: "#",
      field: "serial",
    },
    {
      label: "Student Name",
      field: "name",
    },
    {
      label: "Status",
      field: "status",
    },
    {
      label: "Obtained Marks",
      field: "mark",
    },
    // {
    //   label: "Submission Date",
    //   field: "submission_date",
    // },
    {
      label: "Answer",
      field: "answer",
    },
  ];

  const tableData = assignment?.data?.submissions?.map((item, index) => {
    return {
      serial: <p className="font-semibold">{index + 1}</p>,
      name: (
        <div className="flex items-center gap-2">
          <img
            className="h-10 w-10 rounded-full"
            src={item?.image ? ASSET_URL + item?.image : avatar}
            alt="Avatar"
          />
          <p>{item.name}</p>
        </div>
      ),
      status: (
        <span
          className={`font-semibold text-white px-3 py-1.5 rounded ${
            item.mark
              ? "bg-green-500"
              : item.submission_status
              ? "bg-orange-500"
              : "bg-gray-700"
          }`}
        >
          {item.mark
            ? "Marked"
            : item.submission_status
            ? "Submitted"
            : "Not Submitted"}
        </span>
      ),
      mark: <p>{item.mark || "-"}</p>,
      // Conditional rendering for View Answer button
      answer: item.submission_id ? (
        <Link
          to={`/answer-details/${item.submission_id}`}
          className="text-blue-700 font-semibold"
        >
          View Answer
        </Link>
      ) : (
        <p className="bg-gray-700 text-white py-2 rounded font-semibold">No Answer</p>
      ),
    };
  });

  return (
    <section className="space-y-5 container mt-12">
      {isLoading ? (
        <Loading />
      ) : isError ? (
        <p className="text-red-500">Failed to load assignment details.</p>
      ) : (
        <div className="flex flex-col gap-4">
          <h1 className="text-xl font-bold text-sky-700">
            <b>Title : </b> {assignment?.data.title}
          </h1>
          <p className="text-md text-gray-700">
            {assignment?.data.description ||
              `
              Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum
            `}
          </p>

          {assignment?.data.supporting_doc && (
            <iframe
              src={ASSET_URL + assignment?.data.supporting_doc}
              className="w-full h-96"
              title="Supporting Document"
            />
          )}

          <p className="text-sm text-gray-600">
            <strong>Publish Date:</strong>{" "}
            {new Date(assignment?.data.publish_date).toLocaleDateString()}
          </p>
          <p className="text-sm text-gray-600">
            <strong>Deadline:</strong>{" "}
            {new Date(assignment?.data.deadline).toLocaleDateString()}
          </p>

          {assignment?.data.assignment_submission === null ? (
            <Formik
              validationSchema={Yup.object().shape({
                answer: Yup.string(),
                attachment_files: Yup.array().min(
                  1,
                  "At least one file is required"
                ),
              })}
              initialValues={{
                answer: "",
                attachment_files: [],
              }}
              onSubmit={handleSubmit}
            >
              {({ setFieldValue, values, errors, touched }) => (
                <Form className="bg-white p-6 rounded-lg shadow-md flex flex-col gap-6">
                  {/* Answer Field */}
                  <Field
                    as="textarea"
                    name="answer"
                    className="w-full p-2 border rounded-lg shadow-sm resize-none"
                    rows={10}
                    placeholder="Type your answer here..."
                  />
                  {errors.answer && touched.answer && (
                    <div className="text-red-500">{errors.answer}</div>
                  )}

                  {/* Preview Section */}
                  {values.attachment_files.length > 0 && (
                    <div className="grid grid-cols-6 md:grid-cols-8 gap-4 mt-4">
                      {values.attachment_files.map((file, index) => (
                        <div
                          key={index}
                          className="relative border rounded-lg p-2 shadow-sm"
                        >
                          {file.type.startsWith("image/") ? (
                            <img
                              src={URL.createObjectURL(file)}
                              alt={`Preview ${index}`}
                              className="max-w-full h-auto rounded"
                            />
                          ) : (
                            <div className="flex flex-col items-center">
                              <Icon
                                icon="mdi:file-document-outline"
                                className="text-gray-500 text-4xl"
                              />
                              <p className="text-sm text-gray-600 mt-2">
                                {file.name}
                              </p>
                            </div>
                          )}
                          <button
                            type="button"
                            onClick={() =>
                              setFieldValue(
                                "attachment_files",
                                values.attachment_files.filter(
                                  (_, fileIndex) => fileIndex !== index
                                )
                              )
                            }
                            className="absolute top-1 right-1 bg-red-600 text-white rounded-full p-1"
                          >
                            <Icon icon="mdi:close" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* File Input Section */}
                  <div className="flex flex-col gap-2">
                    <label
                      className="text-gray-700 font-semibold text-lg"
                      htmlFor="files"
                    >
                      Attach Assignment Files
                    </label>
                    <div className="border border-dashed border-gray-400 rounded-lg p-4">
                      <input
                        id="files"
                        type="file"
                        name="attachment_files"
                        multiple
                        accept=".jpg,.jpeg,.png,.pdf"
                        onChange={(e) =>
                          setFieldValue(
                            "attachment_files",
                            Array.from(e.target.files)
                          )
                        }
                        className="hidden"
                      />
                      <label
                        htmlFor="files"
                        className="cursor-pointer px-4 py-2 bg-sky-700 text-white rounded-md hover:bg-sky-600 transition-colors flex justify-center items-center"
                      >
                        <Icon icon="mdi:upload" className="text-xl mr-2" />
                        Choose Files
                      </label>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    className="w-full bg-sky-700 text-white py-2 px-4 rounded hover:bg-sky-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
                    disabled={
                      submitting || values.attachment_files.length === 0
                    }
                  >
                    {submitting ? "Submitting..." : "Submit Assignment"}
                  </button>
                </Form>
              )}
            </Formik>
          ) : (
            <div>
              {assignment?.data.assignment_submission?.answer && (
                <p className="text-sm text-gray-600">
                  {assignment?.data.assignment_submission?.answer}
                </p>
              )}
              {/* Show Images if available */}
              {assignment?.data.assignment_submission?.assignment_attachments
                ?.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold">Submitted Images:</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                    {assignment?.data.assignment_submission.assignment_attachments.map(
                      (attachment, index) => (
                        <div key={attachment.id} className="relative">
                          <img
                            src={ASSET_URL + attachment.file}
                            alt={`Submitted ${index}`}
                            className="w-full h-32 object-cover rounded-md cursor-pointer"
                            onClick={() =>
                              openModal(ASSET_URL + attachment.file)
                            }
                          />
                        </div>
                      )
                    )}
                  </div>
                </div>
              )}

              {/* Modal to display the image */}
              {modalImage && (
                <div
                  className="fixed inset-1 bg-black bg-opacity-50 flex justify-center items-center z-50"
                  onClick={closeModal}
                >
                  <div className="relative bg-white p-4 rounded-md">
                    <button
                      onClick={closeModal}
                      className="absolute top-2 right-2 text-white bg-red-600 p-2 rounded-full m-8"
                    >
                      <Icon icon="mdi:close" className="text-xl" />
                    </button>
                    <img
                      src={modalImage}
                      alt="Modal Image"
                      className="p-10 max-w-full max-h-screen object-contain"
                    />
                  </div>
                </div>
              )}
            </div>
          )}

          <div className="relative text-center">
            <BasicTablePage
              title={assignment?.data?.title}
              //   editPage={editPage}
              //   actions={actions}
              columns={columns}
              data={tableData}
              //   openCreateModal={() => setShowModal(true)}
              // changePage={changePage}
              //   currentPage={data?.current_page}
              //   submitForm={handleSubmit}
              //   totalPages={Math.ceil(data?.total / data?.per_page)}
              // filter={filter}
              // setFilter={setApiParam}
            />
          </div>
        </div>
      )}
    </section>
  );
};

export default AssignmentDetails;
