import React, { useState } from "react";
import Card from "@/components/ui/Card";
import MentorInformation from "./leftSide/mentorInformation";
import CourseInformation from "./leftSide/courseInformation";
import ActivityOverview from "./leftSide/activityoverView";
import TopCardInformation from "./center/topInformation";
import MyCoursesInformation from "./center/myCourseInformation";
import UpComingLiveClass from "./rightSide/upcomingClass";
import ClassRoutine from "./rightSide/classRoutine";
import JoinDate from "./rightSide/joinDate";
import useFetch from "@/hooks/useFetch";
import Assignments from "./rightSide/assignments";
import { useSelector } from "react-redux";

const index = () => {
  const [isLoading, setLoading] = useState(false);
  const {isAuth} = useSelector(state => state.auth);
  const { data: mentorDashboard, isError } = useFetch({
    queryKey: "mentorDashboard",
    endPoint: "mentor-dashboard",
  });

  console.log("mentor", mentorDashboard?.data);
  const upcommingClasses = mentorDashboard?.data?.upcoming_class_routine;

  const assignments = mentorDashboard?.data?.assignments;

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error fetching data</div>;
  return (
    <>
      <div className="my-5"></div>
      <div className="grid grid-cols-12 gap-5 mb-5 mx-5">
        <div className="lg:col-span-3 col-span-12 space-y-4">
          <Card className="shadow-lg border border-slate-200">
            <MentorInformation />
          </Card>
          <div className="shadow-lg border border-slate-200 rounded-lg">
            {/* <CourseInformation /> */}
          </div>
          <div className="shadow-lg border border-slate-200 rounded-lg">
            <ActivityOverview data={mentorDashboard?.data} />
          </div>
        </div>
        <div className="lg:col-span-6 col-span-12 space-y-4">
          <Card className="shadow-lg border border-slate-200 rounded-lg">
            <TopCardInformation />
          </Card>
          <div className="shadow-lg border border-slate-200 rounded-lg">
            <MyCoursesInformation />
          </div>
        </div>
        <div className="lg:col-span-3 col-span-12 space-y-4">
          <Card className="shadow-lg border border-slate-200 rounded-lg">
            <UpComingLiveClass liveClasses={upcommingClasses} />
          </Card>
          <Card className="shadow-lg border border-slate-200 rounded-lg">
            <Assignments assignments={assignments} />
          </Card>
          {/* <Card className="shadow-lg border border-slate-200 rounded-lg">
            <ClassRoutine />
          </Card> */}
          {/* <Card className="shadow-lg border border-slate-200 rounded-lg">
            <JoinDate />
          </Card> */}
        </div>
      </div>
    </>
  );
};

export default index;
