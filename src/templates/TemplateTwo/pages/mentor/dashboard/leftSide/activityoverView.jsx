import React, { useState } from "react";
import GraphImage from "@/assets/MentorDashboard/timeSpending.svg";
import ReactApex<PERSON><PERSON> from "react-apexcharts";
import useFetch from "@/hooks/useFetch";

// Graph set in image

const activityoverView = ({ info }) => {
  const [selectedDays, setSelectedDays] = useState(7);

  const { data, isLoading, isError, refetch } = useFetch({
    queryKey: ["graph-data", selectedDays], // Include selectedDays in query key
    endPoint: `mentor-activities?days=${selectedDays}`, // Use selectedDays to filter data
  });

  const mentorData = data?.data;

  // const data = [44, 55, 41, 67, 22, 43];
  const categoryDays = mentorData?.map((data) => {
    return new Date(data?.date).toLocaleDateString("en-US", {weekday: "short"});
  });
  const assignmentCount = mentorData?.map((data) => data?.assignment_count);
  const classCount = mentorData?.map((data) => data?.class_count);


  const chartOptions = {
    chart: {
      type: "bar",
      height: 350,
      toolbar: {
        show: false,
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "50%",
        endingShape: "rounded",
      },
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      show: true,
      width: 2,
      colors: ["transparent"],
    },
    xaxis: {
      categories: categoryDays,
    },
    yaxis: {
      title: {
        text: "Values",
      },
    },
    fill: {
      opacity: 1,
    },
    tooltip: {
      y: {
        formatter: (val) => `${val} units`,
      },
    },
    title: {
      text: "Activity Overview",
      align: "center",
      fontSize: "30px",
    },
    colors: ["#EB8317", "#10375C"],
  };

  const dataSeries = [
    {
      name: "Live Class",
      data: classCount,
    },
    {
      name: "Assignment",
      data: assignmentCount,
    },
  ];

  return (
    <div className="w-full h-full p-2">
      <div className="flex items-center justify-between p-3">
        <p className="text-base"></p>
        <p className="text-base">Last 7 days</p>
      </div>
      {/* <img src={GraphImage} alt="" /> */}
      <div className="column-chart p-5">
        <ReactApexChart
          options={chartOptions}
          series={dataSeries}
          type="bar"
          height={350}
        />
      </div>
    </div>
  );
};

export default activityoverView;
