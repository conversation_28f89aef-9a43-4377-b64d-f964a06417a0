import React from "react";
import Icon from "@/components/ui/Icon";
import Chart from "react-apexcharts";
import { colors } from "@/constant/data";
import SplitDropdown from "@/components/ui/Split-dropdown";

const CourseList = () => {
  // Sample Data for courses with individual datasets for graphs
  const courses = [
    {
      title: "BCS prilli quiz module",
      percentage: "60%",
      trend: "down",
      series: [{ data: [800, 600, 1000, 800] }],
    },
    {
      title: "GK Exam",
      percentage: "30%",
      trend: "up",
      series: [{ data: [400, 300, 500, 400] }],
    },
    {
      title: "BCS prilli quiz module",
      percentage: "30%",
      trend: "up",
      series: [{ data: [700, 500, 600, 700] }],
    },
    {
      title: "IELTS Preparation",
      percentage: "60%",
      trend: "down",
      series: [{ data: [900, 800, 700, 600] }],
    },
  ];

  // Common chart options
  const options = {
    chart: {
      toolbar: {
        autoSelected: "pan",
        show: false,
      },
      offsetX: 0,
      offsetY: 0,
      zoom: {
        enabled: false,
      },
      sparkline: {
        enabled: true,
      },
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      curve: "smooth",
      width: 2,
    },
    colors: [colors.primary],
    tooltip: {
      theme: "light",
    },
    grid: {
      show: false,
      padding: {
        left: 0,
        right: 0,
      },
    },
    yaxis: {
      show: false,
    },
    fill: {
      type: "solid",
      opacity: [0.1],
    },
    legend: {
      show: false,
    },
    xaxis: {
      low: 0,
      offsetX: 0,
      offsetY: 0,
      show: false,
      labels: {
        show: false,
      },
      axisBorder: {
        show: false,
      },
    },
  };

  return (
    <div className="shadow-lg p-3">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-4">
        <span className="text-base font-semibold text-shark-950">
          Course List
        </span>
        <SplitDropdown
          classMenuItems="left-0  w-[220px] top-[110%]"
          label="All Course Overview"
          labelClass="btn-outline-warning"
        />
      </div>

      {/* Subheader Section */}
      <p className="text-sm text-cello-900 mb-4">Average</p>

      {/* Courses List */}
      <div className="space-y-4">
        {courses.map((course, index) => (
          <div
            key={index}
            className="flex items-center justify-between border-b border-gray-200 pb-3 gap-2"
          >
            {/* Course Title Section */}
            <div className="flex flex-col">
              <p className="text-sm font-medium text-shark-950">
                {course.title}
              </p>
            </div>

            {/* Graph Section */}
            <div className="flex items-center">
              <Chart
                options={options}
                series={course.series}
                type="area"
                height={40}
                width={130}
              />
            </div>

            {/* Percentage and Trend Section */}
            <div className="flex items-center space-x-1 text-sm font-semibold">
              <p className="text-sm font-medium text-shark-950">
                {course.percentage}
              </p>
              {course.trend === "up" ? (
                <Icon
                  icon="mdi:arrow-up-bold"
                  className="w-4 h-4 text-green-500"
                />
              ) : (
                <Icon
                  icon="mdi:arrow-down-bold"
                  className="w-4 h-4 text-red-500"
                />
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CourseList;
