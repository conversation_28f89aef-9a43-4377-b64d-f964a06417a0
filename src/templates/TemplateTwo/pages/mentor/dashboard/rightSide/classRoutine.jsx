// src/components/ClassRoutine.jsx
import React from "react";
import Icon from "@/components/ui/Icon";

const ClassRoutine = ({ routineData }) => {
  // Sample Data for class routine
  const routine = [
    {
      day: "Saturday",
      time: "12.00 am - 12.30 am",
      course: "Course name Module Title",
    },
    { day: "Sunday", time: "12.00 am", course: "Course name Module Title" },
    { day: "Monday", time: "12.00 am", course: "Course name Module Title" },
    { day: "Tuesday", time: "12.00 am", course: "Course name Module Title" },
    { day: "Wednesday", time: "12.00 am", course: "Course name Module Title" },
    { day: "Thursday", time: "12.00 am", course: "Course name Module Title" },
    { day: "Friday", time: "12.00 am", course: "Course name Module Title" },
  ];

  return (
    <div className="">
      {/* Header Section */}
      <div className="flex items-center gap-2 mb-4">
        <Icon icon="mdi:calendar-clock" className="w-6 h-6 text-blue-500" />
        <h3 className="text-base font-semibold text-downriver-900">
          Class Routine
        </h3>
      </div>

      {/* Routine Table */}
      <div className="overflow-x-auto">
        <table className="w-full table-auto text-left border-collapse">
          <thead>
            <tr className="text-center">
              <th className="py-2 px-1 text-sm font-semibold text-gray-700 border-e border-b border-slate-400">
                Day
              </th>
              <th className="py-2 px-1 text-sm font-semibold text-gray-700 border-e border-b border-slate-400">
                Time
              </th>
              <th className="py-2 px-1 text-sm font-semibold text-gray-700  border-b border-slate-400">
                Course & Module name
              </th>
            </tr>
          </thead>
          <tbody>
            {routine.map((entry, index) => (
              <tr key={index} className="text-center">
                <td className="py-1 px-1 text-sm text-gray-700 border-e border-b border-slate-400">
                  {entry.day}
                </td>
                <td className="py-1 px-1 text-sm text-gray-700 border-e border-b border-slate-400">
                  {entry.time}
                </td>
                <td className="py-1 px-1 text-sm text-gray-700 border-b border-slate-400">
                  {entry.course}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ClassRoutine;
