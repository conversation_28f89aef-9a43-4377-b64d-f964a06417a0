import React, { useState, useEffect } from "react";
import api from "@/server/api";
import useFetch from "@/hooks/useFetch";
import { ASSET_URL } from "@/config";
const Attendance = ({ eventData, setIsAttendancePage }) => {
  const [attendance, setAttendance] = useState({});
  const [isLoading, setLoading] = useState(false);
  const { data: students, isLoading: studentsLoading } = useFetch({
    queryKey: `student-list-for-attendance?class_schedule_id=${eventData.id}${eventData?.batch_id ? "&batch_id=" + eventData.batch_id : ''}`,
    endPoint: `mentor/student-list-for-attendance?class_schedule_id=${eventData.id}${eventData?.batch_id ? "&batch_id=" + eventData.batch_id : ''}`,
  });

  console.log(students);
  useEffect(() => {
    if (students) {
      const initialAttendance = students?.data.reduce((acc, student) => {
        if (student.attendance_id !== null && student.is_present) {
          acc[student.id] = true;
        }
        return acc;
      }, {});
      setAttendance(initialAttendance);
    }
  }, [students]);

  const handleAttendanceChange = (studentId) => {
    setAttendance((prevAttendance) => ({
      ...prevAttendance,
      [studentId]: !prevAttendance[studentId],
    }));
  };

  const handleSubmit = async () => {
    setLoading(true);
    const attendanceData = {
      course_id: eventData?.course_id,
      batch_id: eventData?.batch_id,
      class_schedule_id: eventData?.id,
      attendance_date: new Date(eventData?.schedule_datetime)
        .toISOString()
        .split("T")[0],
      students: students?.data.map((student) => ({
        id: student.id,
        is_present: !!attendance[student.id],
      })),
    };

    const response = await api.post("mentor/save-attendance", attendanceData);

    if (response?.error) {
      alert("Error submitting attendance.");
      console.error(response.error);
    } else {
      setIsAttendancePage(false);
    }

    setLoading(false);
  };

  return (
    <div className="px-6 py-4">
      {/* Header Section */}
      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <button
          className="text-blue-600 hover:text-blue-800 font-semibold mb-4"
          onClick={() => setIsAttendancePage(false)}
        >
          &larr; Back
        </button>
        <div className="flex flex-col items-start space-y-2">
          <h2 className="text-xl font-semibold text-gray-700">
            {eventData?.title}
          </h2>
          <h1 className="text-sm font-bold text-gray-800">
            {eventData?.course}
          </h1>
          <h3 className="text-sm font-medium text-gray-600">
            {eventData?.batch_name}
          </h3>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white shadow-md rounded-lg p-6">
        {students?.data.length > 0 && (
          <div>
            <h2 className="text-xl font-bold mb-4">Student Attendance</h2>

            {studentsLoading ? (
              <p>Loading students...</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

            
                {students?.data.map((student) => (
                  <div
                    key={student.id}
                    className="border rounded-lg p-4 bg-gray-50 shadow-sm hover:shadow-md transition duration-150 cursor-pointer"
                    onClick={() => handleAttendanceChange(student.id)}
                  >
                    <div className="flex items-center space-x-4">
                      {student.image ? (
                        <img
                          src={`${ASSET_URL + student.image}`}
                          alt={student.name}
                          className="w-12 h-12 rounded-full border object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-gray-500 font-bold">
                          {student.name[0]}
                        </div>
                      )}
                      <div className="flex-1">
                        <span className="text-md font-medium text-gray-800">
                          {student.name}
                        </span>
                        <p className="text-sm text-gray-500">
                          {student.student_id || "No ID Available"}
                        </p>
                      </div>
                      <div>
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            aria-label={`Mark ${student.name} as present`}
                            className="form-checkbox bg-blue-500 rounded-full h-6 w-6 border-2 border-transparent focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            checked={!!attendance[student.id]}
                            readOnly
                          />
                          <span className="text-md font-medium text-blue-500">
                            Present
                          </span>
                        </label>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <button
              onClick={handleSubmit}
              className="mt-6 bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition duration-150"
              disabled={isLoading}
            >
              {isLoading ? "Submitting..." : "Submit"}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Attendance;
