import React, { useEffect, useState } from "react";
import useFetch from "@/hooks/useFetch";
import Loading from "@/components/Loading";
import { useParams } from "react-router-dom";
import GoBack from "@/components/ui/GoBack";
import examIcon from "@/assets/images/svg/exam.svg";
import SummaryCard from "./components/SummaryCard";
import TabsNav from "./components/TabsNav";
import QuestionCard from "./components/QuestionCard";
import TrueFalseOptions from "./components/TrueFalseOptions";
import MCQOptions from "./components/MCQOptions";
import FillInBlankOptions from "./components/FillInBlankOptions";
import MatchingOptions from "./components/MatchingOptions";
import WrittenAnswers from "./components/WrittenAnswers";
import ImageModal from "./components/ImageModal";

const ExamDetails = () => {
  const { id } = useParams();
  const { data, isLoading, isError } = useFetch({
    queryKey: "examDetails",
    endPoint: `student-quiz-result-details-by-id/${id}`,
  });
  const [activeTab, setActiveTab] = useState('mcq');
  const [modalImage, setModalImage] = useState(null);

  useEffect(() => {
    if (data) {
      // Set the active tab based on available question types
      if (data.data.mcq_questions?.length > 0) {
        setActiveTab('mcq');
      } else if (data.data.true_false_questions?.length > 0) {
        setActiveTab('true_false');
      } else if (data.data.fill_in_blank_questions?.length > 0) {
        setActiveTab('fill_in_blank');
      } else if (data.data.matching_questions?.length > 0) {
        setActiveTab('matching');
      }
    }
  }, [data]);

  // Prevent back navigation after quiz submission
  useEffect(() => {
    const preventBack = () => {
      window.history.pushState(null, "", window.location.href);
    };

    // Push a new state to prevent back navigation
    preventBack();

    // Listen for popstate event (back button)
    const handlePopState = () => {
      preventBack();
    };

    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, []);

  const examResultInfo = data?.data;
  const negativeMark = examResultInfo?.negative_mark;
  const positiveMark = examResultInfo?.positive_mark;

  // Handle modal opening
  const openModal = (imagePath) => {
    setModalImage(imagePath);
  };

  // Handle modal closing
  const closeModal = () => {
    setModalImage(null);
  };

  // Format date function
  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      };
      return date.toLocaleDateString('en-US', options);
    } catch (error) {
      return dateString;
    }
  };

  // Calculate total questions and correct answers
  const getTotalQuestions = () => {
    let total = 0;
    if (examResultInfo?.mcq_questions) total += examResultInfo.mcq_questions.length;
    if (examResultInfo?.true_false_questions) total += examResultInfo.true_false_questions.length;
    if (examResultInfo?.fill_in_blank_questions) total += examResultInfo.fill_in_blank_questions.length;
    if (examResultInfo?.matching_questions) total += examResultInfo.matching_questions.length;
    return total;
  };

  const getCorrectAnswers = () => {
    let correct = 0;
    if (examResultInfo?.mcq_questions) {
      correct += examResultInfo.mcq_questions.filter(q => q.is_correct).length;
    }
    if (examResultInfo?.true_false_questions) {
      correct += examResultInfo.true_false_questions.filter(q => q.is_correct).length;
    }
    if (examResultInfo?.fill_in_blank_questions) {
      correct += examResultInfo.fill_in_blank_questions.filter(q => q.is_correct).length;
    }
    if (examResultInfo?.matching_questions) {
      correct += examResultInfo.matching_questions.filter(q => q.is_correct).length;
    }
    return correct;
  };

  if (isLoading) return <Loading />;
  if (isError) return <div>Error fetching data</div>;

  return (
    <div className="container my-6">

      {/* Summary Card with gradient background */}
      <SummaryCard
        examResultInfo={examResultInfo}
        formatDate={formatDate}
        getTotalQuestions={getTotalQuestions}
        getCorrectAnswers={getCorrectAnswers}
        examIcon={examIcon}
      />

      {/* Tabs for different question types */}
      <TabsNav
        examResultInfo={examResultInfo}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
      />

      {/* Question Content Sections */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* MCQ Questions */}
        {activeTab === 'mcq' && examResultInfo?.mcq_questions?.length > 0 && (
          <div className="p-2">
            {examResultInfo.mcq_questions.map((question, index) => (
              <QuestionCard
                key={index}
                question={question}
                index={index}
                positiveMark={positiveMark}
                negativeMark={negativeMark}
                openModal={openModal}
              >
                <MCQOptions
                  question={question}
                  openModal={openModal}
                />
              </QuestionCard>
            ))}
          </div>
        )}

        {/* True/False Questions */}
        {activeTab === 'true_false' && examResultInfo?.true_false_questions?.length > 0 && (
          <div className="p-2">
            {examResultInfo.true_false_questions.map((question, index) => (
              <QuestionCard
                key={index}
                question={question}
                index={index}
                positiveMark={positiveMark}
                negativeMark={negativeMark}
                openModal={openModal}
              >
                <TrueFalseOptions question={question} />
              </QuestionCard>
            ))}
          </div>
        )}

        {/* Fill in Blank Questions */}
        {activeTab === 'fill_in_blank' && examResultInfo?.fill_in_blank_questions?.length > 0 && (
          <div className="p-2">
            {examResultInfo.fill_in_blank_questions.map((question, index) => (
              <QuestionCard
                key={index}
                question={question}
                index={index}
                positiveMark={positiveMark}
                negativeMark={negativeMark}
                openModal={openModal}
              >
                <FillInBlankOptions question={question} />
              </QuestionCard>
            ))}
          </div>
        )}

        {/* Matching Questions */}
        {activeTab === 'matching' && examResultInfo?.matching_questions?.length > 0 && (
          <div className="p-2">
            {examResultInfo.matching_questions.map((question, index) => (
              <QuestionCard
                key={index}
                question={question}
                index={index}
                positiveMark={positiveMark}
                negativeMark={negativeMark}
                openModal={openModal}
              >
                <MatchingOptions question={question} />
              </QuestionCard>
            ))}
          </div>
        )}
      </div>

      <WrittenAnswers
        examResultInfo={examResultInfo}
        openModal={openModal}
      />

      {/* Modal to display the image */}
      <ImageModal
        modalImage={modalImage}
        closeModal={closeModal}
      />


    </div>
  );
};

export default ExamDetails;
