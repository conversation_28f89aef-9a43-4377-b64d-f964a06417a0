import React from 'react';
import { ASSET_URL } from "@/config";

const MCQOptions = ({ question, openModal }) => {
  const notAnswered = !question.answer1 && !question.answer2 && !question.answer3 && !question.answer4;
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
      <div className={`p-3 rounded-md flex items-center gap-3 ${
        question.answer1
          ? question.correct_answer1
            ? "bg-green-100 border border-green-200"
            : "bg-red-100 border border-red-200"
          : question.correct_answer1
            ? "bg-blue-50 border border-blue-200"
            : "bg-gray-50 border border-gray-200"
      }`}>
        <input
          type="checkbox"
          checked={question.answer1}
          disabled
          className="w-5 h-5"
        />
        <div className="flex items-center gap-2">
          {question.option1_image ? (
            <img
              src={ASSET_URL + question.option1_image}
              className="max-w-[100px] h-auto rounded"
              alt=""
              onClick={() => openModal(ASSET_URL + question.option1_image)}
            />
          ) : null}
          <span className={`${
            question.answer1
              ? question.correct_answer1
                ? "text-green-800 font-medium"
                : "text-red-800 font-medium"
              : question.correct_answer1
                ? "text-blue-800 font-medium"
                : "text-gray-700"
          }`}>
            {question.option1}
          </span>
        </div>
      </div>

      <div className={`p-3 rounded-md flex items-center gap-3 ${
        question.answer2
          ? question.correct_answer2
            ? "bg-green-100 border border-green-200"
            : "bg-red-100 border border-red-200"
          : question.correct_answer2
            ? "bg-blue-50 border border-blue-200"
            : "bg-gray-50 border border-gray-200"
      }`}>
        <input
          type="checkbox"
          checked={question.answer2}
          disabled
          className="w-5 h-5"
        />
        <div className="flex items-center gap-2">
          {question.option2_image ? (
            <img
              src={ASSET_URL + question.option2_image}
              className="max-w-[100px] h-auto rounded"
              alt=""
              onClick={() => openModal(ASSET_URL + question.option2_image)}
            />
          ) : null}
          <span className={`${
            question.answer2
              ? question.correct_answer2
                ? "text-green-800 font-medium"
                : "text-red-800 font-medium"
              : question.correct_answer2
                ? "text-blue-800 font-medium"
                : "text-gray-700"
          }`}>
            {question.option2}
          </span>
        </div>
      </div>

      <div className={`p-3 rounded-md flex items-center gap-3 ${
        question.answer3
          ? question.correct_answer3
            ? "bg-green-100 border border-green-200"
            : "bg-red-100 border border-red-200"
          : question.correct_answer3
            ? "bg-blue-50 border border-blue-200"
            : "bg-gray-50 border border-gray-200"
      }`}>
        <input
          type="checkbox"
          checked={question.answer3}
          disabled
          className="w-5 h-5"
        />
        <div className="flex items-center gap-2">
          {question.option3_image ? (
            <img
              src={ASSET_URL + question.option3_image}
              className="max-w-[100px] h-auto rounded"
              alt=""
              onClick={() => openModal(ASSET_URL + question.option3_image)}
            />
          ) : null}
          <span className={`${
            question.answer3
              ? question.correct_answer3
                ? "text-green-800 font-medium"
                : "text-red-800 font-medium"
              : question.correct_answer3
                ? "text-blue-800 font-medium"
                : "text-gray-700"
          }`}>
            {question.option3}
          </span>
        </div>
      </div>

      <div className={`p-3 rounded-md flex items-center gap-3 ${
        question.answer4
          ? question.correct_answer4
            ? "bg-green-100 border border-green-200"
            : "bg-red-100 border border-red-200"
          : question.correct_answer4
            ? "bg-blue-50 border border-blue-200"
            : "bg-gray-50 border border-gray-200"
      }`}>
        <input
          type="checkbox"
          checked={question.answer4}
          disabled
          className="w-5 h-5"
        />
        <div className="flex items-center gap-2">
          {question.option4_image ? (
            <img
              src={ASSET_URL + question.option4_image}
              className="max-w-[100px] h-auto rounded"
              alt=""
              onClick={() => openModal(ASSET_URL + question.option4_image)}
            />
          ) : null}
          <span className={`${
            question.answer4
              ? question.correct_answer4
                ? "text-green-800 font-medium"
                : "text-red-800 font-medium"
              : question.correct_answer4
                ? "text-blue-800 font-medium"
                : "text-gray-700"
          }`}>
            {question.option4}
          </span>
        </div>
      </div>
    </div>
  );
};

export default MCQOptions;
