import React from 'react';

const FillInBlankOptions = ({ question }) => {
  return (
    <>
      {/* Render the question text with blanks */}
      <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
        {question.question_text.split('_______').map((part, i, arr) => (
          <React.Fragment key={i}>
            <span>{part}</span>
            {i < arr.length - 1 && (
              <span className="inline-block mx-1 px-3 py-1 rounded border min-w-[100px] text-center">
                {question.answers[i] && (
                  <span className={`font-medium ${
                    question.answers[i].is_correct
                      ? "text-green-600"
                      : "text-red-600"
                  }`}>
                    {question.answers[i].user_answer || "(No answer)"}
                  </span>
                )}
              </span>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Answers Table */}
      <div className="mt-4 overflow-x-auto">
        <table className="min-w-full bg-white rounded-lg overflow-hidden border border-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Blank #</th>
              <th className="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Your Answer</th>
              <th className="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Correct Answer</th>
              <th className="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {question.answers.map((answer, i) => (
              <tr key={i} className={answer.is_correct ? "bg-green-50" : "bg-red-50"}>
                <td className="py-2 px-4 text-sm text-gray-700">{i + 1}</td>
                <td className="py-2 px-4 text-sm font-medium">
                  <span className={answer.is_correct ? "text-green-600" : "text-red-600"}>
                    {answer.user_answer || "(No answer)"}
                  </span>
                </td>
                <td className="py-2 px-4 text-sm font-medium text-blue-600">
                  {answer.correct_answer}
                </td>
                <td className="py-2 px-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    answer.is_correct
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}>
                    {answer.is_correct ? "Correct" : "Incorrect"}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </>
  );
};

export default FillInBlankOptions;
