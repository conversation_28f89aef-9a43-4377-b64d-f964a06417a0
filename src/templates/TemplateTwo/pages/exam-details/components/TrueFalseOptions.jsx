import React from 'react';
import { Icon } from "@iconify/react";

const TrueFalseOptions = ({ question }) => {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
        <div className={`p-2 rounded-md flex items-center gap-2 ${
          question.user_answer === true
            ? question.correct_answer === true
              ? "bg-green-100 border border-green-200"
              : "bg-red-100 border border-red-200"
            : question.correct_answer === true
              ? "bg-blue-50 border border-blue-200"
              : "bg-gray-50 border border-gray-200"
        }`}>
          <input
            type="radio"
            checked={question.user_answer === true}
            disabled
            className="w-4 h-4"
          />
          <span className={`text-sm ${
            question.user_answer === true
              ? question.correct_answer === true
                ? "text-green-800 font-medium"
                : "text-red-800 font-medium"
              : question.correct_answer === true
                ? "text-blue-800 font-medium"
                : "text-gray-700"
          }`}>
            True
          </span>
        </div>
        
        <div className={`p-2 rounded-md flex items-center gap-2 ${
          question.user_answer === false
            ? question.correct_answer === false
              ? "bg-green-100 border border-green-200"
              : "bg-red-100 border border-red-200"
            : question.correct_answer === false
              ? "bg-blue-50 border border-blue-200"
              : "bg-gray-50 border border-gray-200"
        }`}>
          <input
            type="radio"
            checked={question.user_answer === false}
            disabled
            className="w-4 h-4"
          />
          <span className={`text-sm ${
            question.user_answer === false
              ? question.correct_answer === false
                ? "text-green-800 font-medium"
                : "text-red-800 font-medium"
              : question.correct_answer === false
                ? "text-blue-800 font-medium"
                : "text-gray-700"
          }`}>
            False
          </span>
        </div>
      </div>
      
      <div className="mt-3 p-2 bg-blue-50 rounded-md border border-blue-100">
        <p className="text-blue-800 text-sm flex items-center gap-1">
          <Icon icon="mdi:check-circle" className="text-base" />
          Correct Answer: <span className="ml-1 font-medium">{question.correct_answer ? "True" : "False"}</span>
        </p>
      </div>
    </>
  );
};

export default TrueFalseOptions;
