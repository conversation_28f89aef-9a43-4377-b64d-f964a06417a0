import React from 'react';
import { Icon } from "@iconify/react";

const SummaryCard = ({ 
  examResultInfo, 
  formatDate, 
  getTotalQuestions, 
  getCorrectAnswers,
  examIcon 
}) => {
  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-lg shadow-sm mb-4 border border-blue-100">
      <div className="md:flex items-center justify-between w-full">
        <h2 className="text-lg font-bold text-blue-800 flex items-center gap-2 mb-2 md:mb-0">
          <img src={examIcon} alt="" className="w-6 h-6" />
          {examResultInfo?.title}
        </h2>
        <div className="flex items-center gap-2 bg-white px-3 py-1 rounded-full shadow-sm border border-blue-100">
          <Icon icon="material-symbols:timer" className="text-blue-600 text-sm" />
          <p className="text-sm font-semibold text-blue-800">
            {examResultInfo?.duration} Minutes
          </p>
        </div>
      </div>

      {/* Score Card */}
      <div className="mt-3 bg-white p-2 rounded-lg shadow-sm border border-blue-100">
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-semibold text-gray-700">Exam Result Summary</h3>
          <div className="flex items-center gap-1">
            <span className="text-xs text-gray-500">Submitted:</span>
            <span className="text-xs font-medium text-gray-700">
              {formatDate(examResultInfo?.submitted_at)}
            </span>
          </div>
        </div>
        
        <div className="mt-2 grid grid-cols-2 md:grid-cols-5 gap-2">
          <div className="bg-blue-50 p-2 rounded-lg flex items-center gap-2">
            <div className="bg-blue-100 p-1 rounded-full">
              <Icon
                icon="bi:question-circle-fill"
                className="text-blue-600 text-base"
              />
            </div>
            <div>
              <p className="text-gray-500 text-xs">Questions</p>
              <p className="font-bold text-blue-800 text-sm">
                {getTotalQuestions()}
              </p>
            </div>
          </div>

          <div className="bg-green-50 p-2 rounded-lg flex items-center gap-2">
            <div className="bg-green-100 p-1 rounded-full">
              <Icon
                icon="icon-park-outline:positive-dynamics"
                className="text-green-600 text-base"
              />
            </div>
            <div>
              <p className="text-gray-500 text-xs">Positive</p>
              <p className="font-bold text-green-700 text-sm">
                {examResultInfo?.positive_mark}
              </p>
            </div>
          </div>

          <div className="bg-red-50 p-2 rounded-lg flex items-center gap-2">
            <div className="bg-red-100 p-1 rounded-full">
              <Icon
                icon="mdi:arrow-down-bold"
                className="text-red-500 text-base"
              />
            </div>
            <div>
              <p className="text-gray-500 text-xs">Negative</p>
              <p className="font-bold text-red-600 text-sm">
                {examResultInfo?.negative_mark}
              </p>
            </div>
          </div>

          <div className="bg-yellow-50 p-2 rounded-lg flex items-center gap-2">
            <div className="bg-yellow-100 p-1 rounded-full">
              <Icon
                icon="mdi:star-circle"
                className="text-yellow-600 text-base"
              />
            </div>
            <div>
              <p className="text-gray-500 text-xs">Score</p>
              <p className="font-bold text-yellow-700 text-sm">
                {examResultInfo?.mark}/{examResultInfo?.exam_mark}
              </p>
            </div>
          </div>

          <div className="bg-purple-50 p-2 rounded-lg flex items-center gap-2">
            <div className="bg-purple-100 p-1 rounded-full">
              <Icon
                icon="mdi:check-circle"
                className="text-purple-600 text-base"
              />
            </div>
            <div>
              <p className="text-gray-500 text-xs">Correct</p>
              <p className="font-bold text-purple-700 text-sm">
                {getCorrectAnswers()}/{getTotalQuestions()}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SummaryCard;
