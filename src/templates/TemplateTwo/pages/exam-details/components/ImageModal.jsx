import React from 'react';
import { Icon } from "@iconify/react";

const ImageModal = ({ modalImage, closeModal }) => {
  if (!modalImage) return null;
  
  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-75 flex justify-center items-center z-50"
      onClick={closeModal}
    >
      <div className="relative bg-white p-1 rounded-lg max-w-4xl max-h-[90vh] overflow-auto">
        <button
          onClick={closeModal}
          className="absolute top-1 right-1 text-white bg-red-600 p-1 rounded-full z-10"
        >
          <Icon icon="mdi:close" className="text-sm" />
        </button>
        <img
          src={modalImage}
          alt="Modal Image"
          className="max-w-full max-h-[85vh] object-contain"
        />
      </div>
    </div>
  );
};

export default ImageModal;
