import React from 'react';
import { Icon } from "@iconify/react";
import { WEITTEN_ASSET_URL } from "@/config";

const WrittenAnswers = ({ examResultInfo, openModal }) => {
  if (!examResultInfo?.written_answers?.length) return null;
  
  return (
    <div className="my-3 bg-white p-3 rounded-lg shadow-sm border border-gray-200">
      <h3 className="text-sm font-semibold text-blue-800 mb-2 flex items-center gap-1">
        <Icon icon="mdi:file-document-outline" className="text-base" />
        Written Answers
      </h3>
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2 mt-2">
        {examResultInfo.written_answers.map((attachment, index) => (
          <div key={attachment.id} className="relative group">
            <img
              src={WEITTEN_ASSET_URL + attachment.attachment_url}
              alt={`Submitted ${index}`}
              className="w-full h-24 object-cover rounded-md border border-gray-200 transition-all duration-300 group-hover:shadow-sm cursor-pointer"
              onClick={() =>
                openModal(WEITTEN_ASSET_URL + attachment.attachment_url)
              }
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-md flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-all duration-300">
                <Icon icon="mdi:magnify-plus" className="text-white text-xl" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WrittenAnswers;
