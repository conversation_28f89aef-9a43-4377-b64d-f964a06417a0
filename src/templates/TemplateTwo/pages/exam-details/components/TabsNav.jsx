import React from 'react';
import { Icon } from "@iconify/react";

const TabsNav = ({ examResultInfo, activeTab, setActiveTab }) => {
  return (
    <div className="mb-3">
      <div className="flex flex-wrap border-b border-gray-200">
        {examResultInfo?.mcq_questions?.length > 0 && (
          <button
            onClick={() => setActiveTab('mcq')}
            className={`px-3 py-2 font-medium text-xs rounded-t-lg transition-all duration-300 ${
              activeTab === 'mcq'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <div className="flex items-center gap-1">
              <Icon icon="mdi:checkbox-marked-circle-outline" className="text-sm" />
              MCQ ({examResultInfo.mcq_questions.length})
            </div>
          </button>
        )}
        
        {examResultInfo?.true_false_questions?.length > 0 && (
          <button
            onClick={() => setActiveTab('true_false')}
            className={`px-3 py-2 font-medium text-xs rounded-t-lg transition-all duration-300 ${
              activeTab === 'true_false'
                ? 'bg-green-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            } ml-1`}
          >
            <div className="flex items-center gap-1">
              <Icon icon="mdi:toggle-switch-outline" className="text-sm" />
              T/F ({examResultInfo.true_false_questions.length})
            </div>
          </button>
        )}
        
        {examResultInfo?.fill_in_blank_questions?.length > 0 && (
          <button
            onClick={() => setActiveTab('fill_in_blank')}
            className={`px-3 py-2 font-medium text-xs rounded-t-lg transition-all duration-300 ${
              activeTab === 'fill_in_blank'
                ? 'bg-yellow-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            } ml-1`}
          >
            <div className="flex items-center gap-1">
              <Icon icon="mdi:form-textbox" className="text-sm" />
              Fill Blanks ({examResultInfo.fill_in_blank_questions.length})
            </div>
          </button>
        )}
        
        {examResultInfo?.matching_questions?.length > 0 && (
          <button
            onClick={() => setActiveTab('matching')}
            className={`px-3 py-2 font-medium text-xs rounded-t-lg transition-all duration-300 ${
              activeTab === 'matching'
                ? 'bg-purple-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            } ml-1`}
          >
            <div className="flex items-center gap-1">
              <Icon icon="mdi:arrow-decision-outline" className="text-sm" />
              Match ({examResultInfo.matching_questions.length})
            </div>
          </button>
        )}
      </div>
    </div>
  );
};

export default TabsNav;
