import React from 'react';
import { Icon } from "@iconify/react";
import { ASSET_URL } from "@/config";

const QuestionCard = ({ question, index, positiveMark, negativeMark, openModal, children }) => {
  return (
    <div
      className={`mb-3 p-3 rounded-lg border ${
        question.is_correct
          ? "bg-green-50 border-green-200"
          : "bg-red-50 border-red-200"
      } transition-all duration-300 hover:shadow-md`}
    >
      <div className="flex justify-between items-start mb-2">
        <div className="flex items-start gap-2">
          <div className={`w-6 h-6 flex items-center justify-center rounded-full text-white text-sm ${
            question.is_correct
              ? "bg-green-500"
              : "bg-red-500"
          }`}>
            {index + 1}
          </div>
          <h3 className="text-base font-medium text-gray-800">
            {question.question_text}
          </h3>
        </div>
        <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
          question.is_correct
            ? "bg-green-100 text-green-800"
            : "bg-red-100 text-red-800"
        }`}>
          {question.is_correct
            ? `+${positiveMark} point${positiveMark > 1 ? "s" : ""}`
            : `-${negativeMark} point${negativeMark > 1 ? "s" : ""}`}
        </span>
      </div>

      {question.question_image && (
        <div className="mb-2">
          <img
            src={ASSET_URL + question.question_image}
            alt="Question"
            className="max-w-full h-auto max-h-32 rounded-md border border-gray-200"
            onClick={() => openModal(ASSET_URL + question.question_image)}
          />
        </div>
      )}

      {/* Render children (options) */}
      {children}

      {/* Explanation Section */}
      {(question.explanation_text || question.explanation_image) && (
        <div className="mt-3 p-2 bg-blue-50 rounded-md border border-blue-100">
          <p className="text-blue-800 text-sm flex items-center gap-1">
            <Icon icon="mdi:information" className="text-base" />
            Explanation
          </p>
          {question.explanation_image && (
            <img
              src={ASSET_URL + question.explanation_image}
              alt="Explanation"
              className="max-w-full h-auto rounded mb-1"
              onClick={() => openModal(ASSET_URL + question.explanation_image)}
            />
          )}
          {question.explanation_text && (
            <p className="text-gray-700 text-xs">{question.explanation_text}</p>
          )}
        </div>
      )}
    </div>
  );
};

export default QuestionCard;
