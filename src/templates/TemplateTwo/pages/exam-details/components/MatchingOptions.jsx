import React from 'react';

const MatchingOptions = ({ question }) => {
  return (
    <div className="mt-4 overflow-x-auto">
      <table className="min-w-full bg-white rounded-lg overflow-hidden border border-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
            <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Your Answer</th>
            <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Correct Answer</th>
            <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {question.matches.map((match, i) => (
            <tr key={i} className={match.is_correct ? "bg-green-50" : "bg-red-50"}>
              <td className="py-3 px-4 text-sm font-medium text-gray-900">{match.left_item}</td>
              <td className="py-3 px-4 text-sm">
                <span className={match.is_correct ? "text-green-600 font-medium" : "text-red-600 font-medium"}>
                  {match.selected_right_item || "(No answer)"}
                </span>
              </td>
              <td className="py-3 px-4 text-sm font-medium text-blue-600">
                {match.correct_right_item}
              </td>
              <td className="py-3 px-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  match.is_correct
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}>
                  {match.is_correct ? "Correct" : "Incorrect"}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default MatchingOptions;
