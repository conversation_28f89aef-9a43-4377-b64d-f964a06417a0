import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import useFetch from "@/hooks/useFetch";
import { Icon } from "@iconify/react";
import { Formik, Field } from "formik";
import paymentImg from "@/assets/images/all-img/payment_step.jpg";
import { ASSET_URL } from "@/config";
import api from "@/server/api";
import * as Yup from "yup";
const Payment = ({payment, closeModal}) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [selectedPaymentType, setSelectedPaymentType] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [paymentInfo, setPaymentInfo] = useState({ trx_id: "" });
  const [isPaying, setIsPaying] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  const { data: paymentTypes = [], isLoading: paymentTypesIsLoading } = useFetch({
    queryKey: `organization-payment-typelist`,
    endPoint: `organization-payment-typelist`,
  });

  useEffect(() => {
    if (paymentTypes?.data?.length > 0 && !selectedPaymentType) {
      setSelectedPaymentType(paymentTypes.data[0]);
    }
  }, [paymentTypes]);


  const handlePaymentTypeSelect = (paymentType) => {
    setSelectedPaymentType(paymentType);
  };

  const handlePayment = async (values) => {
    setIsPaying(true);
    const formdata = new FormData();
    formdata.append("payment_id", payment.id);
    formdata.append("payment_method_id", selectedPaymentType?.id);
    formdata.append("payment_method", selectedPaymentType?.name);
    formdata.append("trx_id", values.trx_id);
    formdata.append("amount", payment.payable_amount - payment.paid_amount);
    if (values.image) {
      formdata.append("image", values.image);
    }
    const response = await api.filepost("pay-due", formdata);
    if (response?.data.status === true) {
      setIsPaying(false);
      closeModal();
    }
    
  };

  const openImageModal = (image) => {
    setSelectedImage(image);
    setIsModalOpen(true);
  };

  const closeImageModal = () => {
    setIsModalOpen(false);
    setSelectedImage(null);
  };

  if ( paymentTypesIsLoading) {
    return (
      <div className="min-h-[450px] flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-purple-500"></div>
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="py-6 bg-gray-50">
      <section className="space-y-6 container mx-auto px-4">

        <Formik
          initialValues={{
            trx_id: '',
            image: null,
            selectedItem: ""
          }}
          validationSchema={Yup.object({
            trx_id: Yup.string().required('Transaction ID is required'),
          })}

          onSubmit={handlePayment}
        >
          {({ handleChange, handleSubmit, setFieldValue, values, touched, errors }) => (
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="md:flex items-start md:gap-6 xl:gap-6">
                {/* Left Section: Course Details */}
                <div className="flex-1 bg-white shadow-md p-6 rounded-lg">


                  {/* Payment Type Selection */}
                  <div className="space-y-4 mt-6">
                    <label htmlFor="paymentType" className="block text-sm font-medium text-gray-700">
                      Choose Payment Type
                    </label>
                    <div className="space-x-4 flex flex-wrap justify-center">
                      {paymentTypes?.data.map((paymentType) => (
                        <label
                          key={paymentType.id}
                          className="flex flex-col items-center space-y-2 cursor-pointer hover:scale-105 transition duration-300"
                        >
                          <input
                            type="radio"
                            name="paymentType"
                            value={paymentType.id}
                            checked={selectedPaymentType?.id === paymentType.id}
                            onChange={() => handlePaymentTypeSelect(paymentType)}
                            className="hidden"
                          />
                          <div className="w-20 h-20 border-2 border-gray-300  bg-white flex items-center justify-center overflow-hidden relative">
                            {selectedPaymentType?.id === paymentType.id && (
                              <Icon
                                icon="heroicons:shield-check-solid"
                                className="absolute top-1 right-1 text-blue-500 text-2xl font-bold"
                              />
                            )}
                            <img
                              src={`${ASSET_URL}${paymentType.icon}`}
                              alt={paymentType.name}
                              className="w-18 h-18 object-cover"
                            />
                          </div>
                          <span className="text-sm text-center">{paymentType.name}</span>
                        </label>
                      ))}

                    </div>

                    {selectedPaymentType && (
                      selectedPaymentType.items.map((item) => (
                        <div key={item.id} className="space-y-4">
                          <label className="block text-sm text-gray-700">{item.field_name}</label>
                          {
                            item.field_value !== null &&
                            <Field
                              type="text"
                              name={`field_${item.id}`}
                              value={item.field_value || ""}
                              disabled
                              className="w-full p-3 border rounded-xl bg-gray-200 focus:outline-none"
                            />
                          }

                          {item.image !== null && (
                            <img
                              src={ASSET_URL + item.image}
                              className="w-full h-48 object-cover rounded-xl cursor-pointer transition duration-300 ease-in-out transform hover:scale-105"
                              alt={item.field_name}
                              onClick={() => openImageModal(item.image)}
                            />
                          )}
                        </div>
                      ))


                    )}
                  </div>


                </div>

                {/* Right Section: Payment Information */}
                <div className="flex-1">
                  <section className="space-y-6">

                    <div className="p-4 rounded-lg shadow-lg">

                      <div className="flex justify-between items-center">
                        <span className="bg-[#EFF4FF] text-sky-600 p-2 px-4 text-sm rounded-full">
                          Course
                        </span>
                      </div>
                      <h2 className="text-3xl text-sky-600 font-semibold">{payment?.course_title}</h2>

                      
                      <div className="space-y-4 mt-4">
                        <label className="block text-sm text-gray-700">
                          Amount
                          <span className="text-red-500"> *</span>
                        </label>
                        <input
                          type="text"
                          disabled
                          className="w-full p-2 border rounded-xl bg-gray-200 cursor-not-allowed"
                          value={payment.payable_amount - payment.paid_amount}
                        />
                      </div>


                      {/* Transaction ID Field */}
                      <div className="space-y-1 mt-4">
                        <label className="block text-sm text-gray-700">Transaction ID 
                          <span className="text-red-500"> *</span>
                        </label>
                        <Field
                          type="text"
                          name="trx_id"
                          onChange={(e) => {
                            handleChange(e);
                            setPaymentInfo({ ...paymentInfo, trx_id: e.target.value });
                          }}
                          className="w-full p-2 border rounded-xl focus:outline-none"
                          placeholder="Enter your payment transaction ID"
                        />

                        {touched.trx_id && errors.trx_id && (
                          <div className="text-red-500 text-sm">{errors.trx_id}</div>
                        )}
                        
                      </div>

                      {/* Screenshot Upload */}
                      <div className="space-y-4">
                        <label className="block text-sm text-gray-700 mt-4">Image (Optional)</label>
                        <input
                          type="file"
                          name="image"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files[0];
                            setFieldValue("image", file);
                            setImagePreview(URL.createObjectURL(file));
                          }}
                          className="w-full p-2 border rounded-xl focus:outline-none"
                        />
                      </div>

                      {/* Image Preview */}
                      {imagePreview && (
                        <div className="space-y-4">
                          <label className="block text-sm text-gray-700">Preview</label>
                          <img
                            src={imagePreview}
                            alt="Transaction Screenshot Preview"
                            className="w-full max-h-60 object-contain rounded-lg border"
                          />
                        </div>
                      )}

                      {/* Submit Button */}
                      <button
                        type="submit"
                        className="mt-2 w-full bg-sky-600 text-white py-3 rounded-full hover:bg-sky-700 transition duration-300"
                        disabled={isPaying}
                      >
                        {isPaying ? "Processing..." : "Pay Now"}
                      </button>
                    </div>
                  </section>
                </div>
              </div>
            </form>
          )}
        </Formik>
      </section>

      {/* Modal for image */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg transition-all duration-300 ease-in-out transform scale-110">
            <div className="flex justify-end">
              <button
                onClick={closeImageModal}
                className="text-xl text-gray-500 hover:text-red-500 transition"
              >
                &times;
              </button>
            </div>
            <img
              src={ASSET_URL + selectedImage}
              alt="Selected Item"
              className="w-full h-auto max-h-[80vh] object-contain rounded-xl"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Payment;


