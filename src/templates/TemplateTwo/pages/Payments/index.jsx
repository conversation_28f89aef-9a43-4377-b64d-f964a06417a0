import React, { useState } from "react";
import useFetch from "@/hooks/useFetch";
import Modal from "@/components/ui/Modal";
import Payment from "./Payment";

const formatCurrency = (amount, currency = "BDT") => {
  try {
    return new Intl.NumberFormat("en", {
      style: "currency",
      currency,
      maximumFractionDigits: 0,
    }).format(amount);
  } catch (e) {
    return `${amount} ${currency}`;
  }
};

const PaymentList = () => {
  const [showModal, setShowModal] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);

  const {
    data: response,
    isLoading,
    isError,
  } = useFetch({
    queryKey: "my-payments",
    endPoint: "my-payments",
    params: {},
  });

  const payments = response?.data?.payments;

  const handlePayNow = (payment) => {
    if (!payment.is_verified_payment) return;
    setSelectedPayment(payment);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
  };

  const hasPendingVerification = payments?.some(
    (payment) => payment.is_verified_payment === false
  );

  const getPaymentStatus = (payment) => {
    const due = payment.payable_amount - payment.paid_amount;
    
    if (!payment.is_verified_payment) {
      return { 
        text: "Pending ", 
        color: "yellow", 
        badgeClass: "bg-yellow-100 text-yellow-800" 
      };
    }
    
    if (due > 0) {
      return { 
        text: "Partial Payment", 
        color: "orange", 
        badgeClass: "bg-orange-100 text-orange-800" 
      };
    }
    
    return { 
      text: "Completed", 
      color: "green", 
      badgeClass: "bg-green-100 text-green-800" 
    };
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4 md:mb-0">Your Payments</h1>
        {payments?.length > 0 && (
          <div className="text-sm text-gray-500">
            Showing {payments.length} payment{payments.length !== 1 ? 's' : ''}
          </div>
        )}
      </div>

      {hasPendingVerification && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-lg mb-8">
          <div className="flex items-start gap-3">
            <svg
              className="h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            <div>
              <h3 className="text-sm font-medium text-yellow-800">Verification Required</h3>
              <p className="text-sm text-yellow-700 mt-1">
                One or more payments are pending verification. You won't be able to make additional payments until verification is complete.
              </p>
            </div>
          </div>
        </div>
      )}

      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-16">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-600">Loading your payments...</p>
        </div>
      ) : isError ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <svg
            className="h-10 w-10 text-red-500 mx-auto mb-3"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <h3 className="text-lg font-medium text-red-800 mb-1">Failed to load payments</h3>
          <p className="text-sm text-red-600">Please try again later</p>
        </div>
      ) : payments?.length === 0 ? (
        <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
          <svg
            className="h-12 w-12 text-gray-400 mx-auto mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-1">No payments found</h3>
          <p className="text-gray-500">You don't have any payment records yet</p>
        </div>
      ) : (
        <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {payments.map((payment) => {
            const due = payment.payable_amount - payment.paid_amount;
            const isPaid = due <= 0 && payment.is_verified_payment;
            const isPending = !payment.is_verified_payment;
            const currency = payment.currency || "BDT";
            const status = getPaymentStatus(payment);

            return (
              <div
                key={payment.id}
                className="bg-white rounded-xl border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                      {payment.course_title}
                    </h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${status.badgeClass}`}>
                      {status.text}
                    </span>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Paid Amount</span>
                      <span className="text-sm font-medium text-gray-900">
                        {formatCurrency(payment.paid_amount, currency)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Total Amount</span>
                      <span className="text-sm font-medium text-gray-900">
                        {formatCurrency(payment.payable_amount, currency)}
                      </span>
                    </div>
                    {payment.discount_amount > 0 && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Discount</span>
                        <span className="text-sm font-medium text-green-600">
                          -{formatCurrency(payment.discount_amount, currency)}
                        </span>
                      </div>
                    )}
                    {due > 0 && (
                      <div className="flex justify-between border-t border-gray-100 pt-3">
                        <span className="text-sm font-medium text-gray-900">Due Amount</span>
                        <span className="text-sm font-bold text-red-600">
                          {formatCurrency(due, currency)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-gray-50 px-6 py-4">
                  {isPending ? (
                    <div className="text-center text-yellow-700 bg-yellow-50 px-4 py-2 rounded-lg text-sm font-medium">
                      Waiting for verification
                    </div>
                  ) : due > 0 ? (
                    <button
                      onClick={() => handlePayNow(payment)}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors duration-200"
                    >
                      Pay Now
                    </button>
                  ) : isPaid ? (
                    <a
                      href={`/course/${payment.course?.slug || payment.course_id}`}
                      className="block w-full text-center bg-green-600 hover:bg-green-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors duration-200"
                    >
                      Access Course
                    </a>
                  ) : null}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Payment Modal */}
      {showModal && selectedPayment && (
        <Modal
          title={selectedPayment.course_title}
          activeModal={showModal}
          onClose={closeModal}
          className="max-w-5xl"
        >
          <div className="space-y-4">
            <Payment payment={selectedPayment} closeModal={closeModal} />
          </div>
        </Modal>
      )}
    </div>
  );
};

export default PaymentList;