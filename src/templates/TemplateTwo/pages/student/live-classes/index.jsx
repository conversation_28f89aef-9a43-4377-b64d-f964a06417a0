import React, { useMemo, useState } from "react";
import useFetch from "@/hooks/useFetch";
import Loading from "@/components/Loading";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Breadcrumbs from "@/components/ui/Breadcrumbs";
import bgShape from "@/assets/images/svg/shape2.svg";
import bgShape2 from "@/assets/images/svg/shape3.svg";
import { useDispatch } from "react-redux";

import Badge from "@/components/ui/Badge";
import { useSelector } from "react-redux";
import api from "@/server/api";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
const localizer = momentLocalizer(moment);
import { setShowModal } from "@/store/assignmentStore";
import { Icon } from "@iconify/react/dist/iconify.js";

const StudentLiveClasses = () => {
  const { showDeleteModal, deleteData, showEditModal, showModal, editData } =
    useSelector((state) => state.assignmentSlice);
  const dispatch = useDispatch();
  //   const [showModal, setShowModal] = useState(false);
  //   const [apiParam, setApiParam] = useState("");
  const navigate = useNavigate();
  const [calendarView, setCalendarView] = useState(true);

  const {
    data: liveClasses,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `live-class-list`,
    endPoint: `/class-schedules`,
  });

  //   const res = useGetMentorListQuery(apiParam);
  //   const changePage = (val) => {
  //     setApiParam(val);
  //   };
  const handleJoinClass = async (id) => {
    const { data } = await api.post("/join-class", { class_id: id });
    if (data && data.class_url) {
      window.open(data.class_url, "_blank");
    } else {
      toast.error("Something went wrong!");
    }
  };

  const data = liveClasses?.data;
  const columns = [
    {
      label: "#",
      field: "serial",
    },
    {
      label: "Class Title",
      field: "title",
    },
    {
      label: "Course Name",
      field: "course",
    },
    {
      label: "Date",
      field: "schedule_datetime",
    },
    {
      label: "Start Time",
      field: "start_time",
    },
    {
      label: "Action",
      field: "has_started",
    },
  ];

  const tableData = data?.map((item, index) => {
    return {
      serial: <p className="font-semibold">{index + 1}</p>,
      title: <p>{item.title}</p>,
      course: <p>{item.course}</p>,
      schedule_datetime: <p>{item.schedule_datetime.slice(0, 10)}</p>,
      start_time: <p>{item.start_time}</p>,
      //   status: (
      //     <p className="lowercase">{item.status || "https://meet.google.com"}</p>
      //   ),
      has_started: (
        <span className="space-x-2">
          {item.has_started && !item?.has_completed && (
            <button
              onClick={() => handleJoinClass(item.id)}
              className="px-3 py-1 bg-green-400 rounded font-semibold text-white"
            >
              Join Now
            </button>
          )}
          {!item?.has_started && (
            <button
              disabled
              className="px-3 py-1 bg-orange-400 rounded font-semibold text-white"
            >
              Upcoming
            </button>
          )}

          {item?.has_completed && (
            <button
              disabled
              className="px-3 py-1 bg-gray-600 rounded font-semibold text-white"
            >
              Completed
            </button>
          )}
        </span>
      ),
    };
  });

  const handleSubmit = () => {
    // setShowModal(false);
  };

  // calendar view codes.
  const [selectedDate, setSelectedDate] = useState(null);
  const [eventData, setEventData] = useState(null);
  const [contextMenu, setContextMenu] = useState(null);

  // Fetch data using RTK Query
  console.log(data);

  // Prepare events for the calendar
  const events = useMemo(() => {
    return data?.map((schedule) => ({
      id: schedule.id,
      title: ` ${schedule.mentor} - ${schedule.title}`,
      start: new Date(schedule.schedule_datetime), // Start time from `schedule_datetime`
      end: new Date(
        new Date(schedule.schedule_datetime).getTime() +
          schedule.duration * 60000
      ), // End time calculated using `duration`
      resource: schedule,
    }));
  }, [data]);

  // Slot selection handler (when clicking on an empty slot)
  const handleSlotSelect = (slotInfo) => {
    setSelectedDate(slotInfo.start); // Set the selected date
    dispatch(setShowModal(true)); // Open the modal
  };

  const closeContextMenu = () => {
    setContextMenu(null); // Hide the context menu
  };

  // Event selection handler
  const handleEventSelect = (event) => {
    setEventData(data.find((item) => item.id === event.id));
    setIsAttendancePage(true);
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div className="container mt-5">
      {/* <img className="absolute left-0 z-0" src={bgShape} alt="" />
      <img className="absolute right-0 z-0" src={bgShape2} alt="" /> */}
      <Breadcrumbs />
      <div className="relative text-center mb-8">
        <div className="flex justify-end mx-10">
          <div className="flex justify-center mb-4 w-28 border py-2 rounded-lg">
            <Icon
              icon="mdi:calendar-month-outline"
              className={`cursor-pointer text-2xl mx-4 ${
                calendarView ? "text-blue-600" : "text-gray-400"
              }`}
              onClick={() => setCalendarView(true)}
            />
            <div className="border"></div>
            <Icon
              icon="mdi:table"
              className={`cursor-pointer text-2xl mx-4 ${
                !calendarView ? "text-blue-600" : "text-gray-400"
              }`}
              onClick={() => setCalendarView(false)}
            />
          </div>
        </div>

        {!calendarView ? (
          <BasicTablePage
            title="Live Class List"
            createButton=""
            //   editPage={editPage}
            //   actions={actions}
            columns={columns}
            data={tableData}
            openCreateModal={() => ""}
            // changePage={changePage}
            currentPage={data?.current_page}
            submitForm={handleSubmit}
            totalPages={Math.ceil(data?.total / data?.per_page)}
            // filter={filter}
            // setFilter={setApiParam}
          />
        ) : (
          <div
            style={{ height: "800px" }}
            className="m-10 mt-4"
            onClick={closeContextMenu}
          >
            <Calendar
              localizer={localizer}
              events={events} // Events populated from API
              startAccessor="start" // Start time for events
              endAccessor="end" // End time for events
              style={{ height: 800 }}
              selectable // Enable slot selection
              onSelectSlot={handleSlotSelect} // Handler for selecting an empty slot
              onSelectEvent={handleEventSelect} // Handler for selecting an event
              views={["month", "week", "day"]} // Calendar views
              defaultView="month" // Default view when calendar loads
              defaultDate={new Date()} // Focus on today's date
              dayLayoutAlgorithm="no-overlap" // Prevent overlapping events in the day
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentLiveClasses;
