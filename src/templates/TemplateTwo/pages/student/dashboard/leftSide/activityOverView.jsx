import React, { useState } from "react";
import React<PERSON>pex<PERSON>hart from "react-apexcharts";
import useFetch from "@/hooks/useFetch";

const BarChart = () => {
  const [selectedDays, setSelectedDays] = useState(60); // Default to 60 days

  const { data, isLoading, isError, refetch } = useFetch({
    queryKey: ["graph-data", selectedDays], // Include selectedDays in query key
    endPoint: `learning-graph?days=${selectedDays}`, // Use selectedDays to filter data
  });

  const chartData = data?.data || [];

  // Reduce data to get category and count
  const categoryData = chartData.reduce((acc, item) => {
    if (item.category && item.count) {
      acc[item.category] = item.count;
    }
    return acc;
  }, {});

  // Chart options
  const chartOptions = {
    chart: {
      type: "bar",
      height: 350,
      toolbar: {
        show: false,
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        endingShape: "rounded",
        borderRadius: 3,
        distributed: true,
      },
    },
    series: [
      {
        name: "",
        data: Object.values(categoryData),
      },
    ],
    colors: Object.keys(categoryData).map((category) => {
      const item = chartData.find((dataItem) => dataItem.category === category);
      return item?.color ? `#${item.color}` : "#009688";
    }),
    xaxis: {
      categories: Object.keys(categoryData),
    },
    yaxis: {
      title: {
        text: "Count",
      },
    },
    grid: {
      borderColor: "#e2e8f0",
    },
  };

  // Handle loading and error states
  if (isLoading) return <p>Loading...</p>;
  if (isError) return <p>Error loading data.</p>;

  return (
    <div className="lg:p-4 p-2 bg-white rounded dark:bg-slate-800">
      {/* Dropdown for filtering by days */}
      <div className="mb-4 flex justify-between items-center">
        <label htmlFor="days-filter" className="mr-2">
          Filter by Days:
        </label>
        <select
          id="days-filter"
          value={selectedDays}
          onChange={(e) => {
            setSelectedDays(e.target.value);
            refetch(); // Refetch data when days change
          }}
          className="p-2 border rounded"
        >
          <option value="7">Last 7 Days</option>
          <option value="14">Last 14 Days</option>
          <option value="30">Last 30 Days</option>
          <option value="60">Last 60 Days</option>
          <option value="90">Last 90 Days</option>
        </select>
      </div>

      {/* Chart */}
      {Object.keys(categoryData).length > 0 ? (
        <ReactApexChart
          options={chartOptions}
          series={chartOptions.series}
          type="bar"
          height={350}
        />
      ) : (
        <p>No data available for the selected time period.</p>
      )}
    </div>
  );
};

export default BarChart;
