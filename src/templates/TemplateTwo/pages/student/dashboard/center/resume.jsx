import React from "react";
import CourseImage from "@/assets/StudentDashboard/resume.svg";
import { ASSET_URL } from "@/config";
import { Link } from "react-router-dom";

const ResumeCourseCard = ({ lastActivity }) => {
  // Sample data for the course
  // const courseData = {
  //   title:
  //     "Course Name Course name Course Name Course Name Course name Course Name",
  //   thumbnail: CourseImage,
  // };
  return (
    <section className="container bg-[#E2F6F0] rounded-lg p-5 md:flex justify-between items-center">
      <div className="flex gap-4">
        {/* <img className="w-10 object-contain rounded-lg" src={avatar} alt="" /> */}
        <div className="space-y-2">
          <span className="bg-white text-sky-600 w-32 text-center p-1.5 px-4 text-sm rounded-full">
            Last Watched
          </span>
          <h2 className="text-xl text-gray-700">
            {lastActivity?.title}
          </h2>
          <p className="text-black text-sm">
            {lastActivity?.module_name}
          </p>
        </div>
      </div>

      <div className="flex gap-4 items-center">
        {lastActivity?.type === "video" ||
        lastActivity?.type === "script" ? (
          <Link
            to={`/content-details/${lastActivity?.id}`}
            className="bg-blue-600 text-white px-5 py-2 rounded-lg hover:bg-blue-500"
          >
            Resume
          </Link>
        ) : (
          <Link
            to={`/quiz/${lastActivity?.element_id}/${lastActivity?.course_id}/${lastActivity?.id}`}
            className="bg-blue-600 text-white px-5 py-2 rounded-lg hover:bg-blue-500"
          >
            Resume
          </Link>
        )}
      </div>
    </section>
  );
};

export default ResumeCourseCard;
