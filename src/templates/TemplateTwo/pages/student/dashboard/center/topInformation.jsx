import React, { useState } from "react";
import CourseIcon from "@/assets/StudentDashboard/onprogress.svg";
import CompletedIcon from "@/assets/StudentDashboard/Completed.svg";
import EnrollIcon from "@/assets/StudentDashboard/enrooled.svg";
import PendingIcon from "@/assets/images/svg/project_assignment.svg";
import useFetch from "@/hooks/useFetch";
import { Link } from "react-router-dom";

const topInformation = () => {
  // const [isLoading, setLoading] = useState(false);
  // const { data: mentorDashboard, isError } = useFetch({
  //   queryKey: "mentorDashboard",
  //   endPoint: "mentor-dashboard",
  // });

  // if (isLoading) return <div>Loading...</div>;
  // if (isError) return <div>Error fetching data</div>;

  const stats = [
    {
      title: "Ongoing Courses",
      iconImage: CourseIcon,
      link: '/ongoing-courses'
    },
    {
      title: "Completed Course",
      iconImage: CompletedIcon,
      link: '/completed-courses'
    },
    {
      title: "Enrolled Courses",
      iconImage: EnrollIcon,
      link: '/my-courses'
    },
    {
      title: "Pending Assignment",
      iconImage: PendingIcon,
      link: '/my-assignments',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3  rounded-lg">
      {stats.map((stat, index) => (
        <Link
          key={index}
          to={stat.link}
          className="flex flex-col items-center p-3 border rounded-lg shadow-md"
        >
          {/* Icon Section */}
          <div className="w-16 h-16 flex items-center justify-center mb-4">
            <img src={stat.iconImage} className="w-10 h-10" alt={stat.title} />
          </div>

          {/* Title Section */}
          <p className="text-md font-semibold text-center text-gray-800">
            {stat.title}
          </p>
        </Link>
      ))}
    </div>
  );
};

export default topInformation;
