import React from "react";
import timeUpAnimation from "@/assets/images/all-img/timeup.gif";

const AccessExpired = ({ organization }) => {
  console.log(organization);

  return (
    <div className="h-[70vh] flex items-center justify-center flex-col gap-2">
      <img className="h-[200px]" src={timeUpAnimation} alt="" />
      <div className="text-center p-6 bg-red-50 rounded-lg shadow-md border border-red-200 mx-5">
        <h3 className="text-2xl font-semibold font-serif text-red-600 mb-2">
          Oops! Something Went Wrong
        </h3>
        <p className="text-gray-600">
          Please contact your organization for assistance. <br />
          <span className="font-semibold text-red-500">
            Hotline: {organization?.hotline_number} <br /> Email:{" "}
            {organization?.email}
          </span>
        </p>
      </div>
    </div>
  );
};

export default AccessExpired;
