import React, { useEffect, useRef } from "react";
import api from "@/server/api";

export default function CyberSourceRedirect() {
  const formRef = useRef(null);

  useEffect(() => {
    async function loadForm() {
      const res = await api.post("/cybersource/redirect", {
        amount: 100,
        currency: "USD",
      });

      const form = formRef.current;
      if (!form) return;

      Object.entries(res.data).forEach(([key, value]) => {
        const input = document.createElement("input");
        input.type = "hidden";
        input.name = key;
        input.value = value;
        form.appendChild(input);
      });

      form.submit();
    }

    loadForm();
  }, []);

  return (
    <div className="text-center p-10">
      <p>Redirecting to CyberSource...</p>
      <form
        ref={formRef}
        method="POST"
        action="https://testsecureacceptance.cybersource.com/pay"
      />
    </div>
  );
}
