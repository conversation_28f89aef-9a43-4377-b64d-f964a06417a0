import React, { useState, useEffect, useRef } from "react";
import api from "@/server/api";
import { useNavigate } from "react-router-dom";

export default function CyberSourcePaymentPage({
  amount = 100,
  currency = "USD",
  customerInfo = {},
  onSuccess,
  onError,
}) {
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [captureContext, setCaptureContext] = useState(null);

  const [cardNumberMicroform, setCardNumberMicroform] = useState(null);
  const [expirationDateMicroform, setExpirationDateMicroform] = useState(null);
  const [securityCodeMicroform, setSecurityCodeMicroform] = useState(null);

  const navigate = useNavigate();
  const cardNumberRef = useRef(null);
  const expirationDateRef = useRef(null);
  const securityCodeRef = useRef(null);

  // Initialize CyberSource Flex
  useEffect(() => {
    initializeFlex();
    return () => {
      // Cleanup microforms on unmount
      if (cardNumberMicroform) cardNumberMicroform.remove();
      if (expirationDateMicroform) expirationDateMicroform.remove();
      if (securityCodeMicroform) securityCodeMicroform.remove();
    };
  }, []);

  const initializeFlex = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Get capture context from backend
      const response = await api.post("/cybersource/capture-context", {
        target_origin: window.location.origin,
      });

      if (!response.data.success) {
        throw new Error(response.data.message || "Failed to initialize payment");
      }

      const context = response.data.data.captureContext;
      setCaptureContext(context);

      // Load CyberSource Flex SDK if not already loaded
      if (!window.Flex) {
        await loadFlexSDK();
      }

      // Initialize Flex with capture context
      const flex = new window.Flex(context);

      // Create microforms
      const cardNumber = flex.microform({
        placeholder: "•••• •••• •••• ••••",
        styles: {
          input: {
            "font-size": "16px",
            "font-family": "inherit",
            color: "#374151",
          },
        },
      });

      const expirationDate = flex.microform({
        placeholder: "MM/YY",
        styles: {
          input: {
            "font-size": "16px",
            "font-family": "inherit",
            color: "#374151",
          },
        },
      });

      const securityCode = flex.microform({
        placeholder: "•••",
        styles: {
          input: {
            "font-size": "16px",
            "font-family": "inherit",
            color: "#374151",
          },
        },
      });

      // Load microforms into DOM
      cardNumber.load("#card-number-container");
      expirationDate.load("#expiration-date-container");
      securityCode.load("#security-code-container");

      setCardNumberMicroform(cardNumber);
      setExpirationDateMicroform(expirationDate);
      setSecurityCodeMicroform(securityCode);

    } catch (err) {
      console.error("Flex initialization error:", err);
      setError("Failed to initialize secure payment form. Please try again.");
      onError?.(err);
    } finally {
      setIsLoading(false);
    }
  };

  const loadFlexSDK = () => {
    return new Promise((resolve, reject) => {
      if (window.Flex) {
        resolve();
        return;
      }

      // Check if script is already being loaded
      const existingScript = document.querySelector('script[src*="flex-microform"]');
      if (existingScript) {
        existingScript.addEventListener('load', resolve);
        existingScript.addEventListener('error', reject);
        return;
      }

      const script = document.createElement("script");
      script.src = "https://flex.cybersource.com/cybersource/assets/microform/0.11/flex-microform.min.js";
      script.onload = resolve;
      script.onerror = () => reject(new Error("Failed to load CyberSource Flex SDK"));
      document.head.appendChild(script);
    });
  };

  const handlePayment = async (e) => {
    e.preventDefault();

    if (!cardNumberMicroform || !expirationDateMicroform || !securityCodeMicroform) {
      setError("Payment form not properly initialized. Please refresh and try again.");
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Create tokens from microforms
      const [cardToken, expirationToken, securityToken] = await Promise.all([
        cardNumberMicroform.createToken(),
        expirationDateMicroform.createToken(),
        securityCodeMicroform.createToken(),
      ]);

      // Validate tokens
      if (!cardToken) {
        throw new Error("Invalid card number. Please check and try again.");
      }
      if (!expirationToken) {
        throw new Error("Invalid expiration date. Please check and try again.");
      }
      if (!securityToken) {
        throw new Error("Invalid security code. Please check and try again.");
      }

      // Process payment with tokens
      const paymentData = {
        amount: amount.toString(),
        currency,
        cardToken,
        expirationToken,
        securityToken,
        customerInfo: {
          firstName: customerInfo.firstName || "John",
          lastName: customerInfo.lastName || "Doe",
          email: customerInfo.email || "<EMAIL>",
          address1: customerInfo.address1 || "123 Main St",
          locality: customerInfo.city || "San Francisco",
          administrativeArea: customerInfo.state || "CA",
          postalCode: customerInfo.postalCode || "94105",
          country: customerInfo.country || "US",
          phoneNumber: customerInfo.phoneNumber || "4155551234",
        },
      };

      const response = await api.post("/cybersource/process-payment", paymentData);

      if (response.data.success) {
        onSuccess?.(response.data);
        // Redirect or show success message
        navigate("/payment-success", {
          state: {
            transactionId: response.data.transactionId,
            amount,
            currency
          }
        });
      } else {
        throw new Error(response.data.message || "Payment processing failed");
      }

    } catch (err) {
      console.error("Payment processing error:", err);
      setError(
        err.response?.data?.message ||
        err.message ||
        "Payment failed. Please check your card details and try again."
      );
      onError?.(err);
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col gap-6 items-center justify-center p-6 bg-white border rounded shadow-md max-w-md mx-auto mt-10">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="text-gray-600">Initializing secure payment...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6 p-6 bg-white border rounded-lg shadow-md max-w-md mx-auto mt-10">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          Secure Payment
        </h2>
        <p className="text-lg font-semibold text-blue-600">
          ${amount} {currency}
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md text-sm">
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            {error}
          </div>
        </div>
      )}

      <form onSubmit={handlePayment} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Card Number
          </label>
          <div
            id="card-number-container"
            ref={cardNumberRef}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 bg-white min-h-[42px]"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Expiry Date
            </label>
            <div
              id="expiration-date-container"
              ref={expirationDateRef}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 bg-white min-h-[42px]"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CVV
            </label>
            <div
              id="security-code-container"
              ref={securityCodeRef}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 bg-white min-h-[42px]"
            />
          </div>
        </div>

        <button
          type="submit"
          disabled={isProcessing || !captureContext}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-3 px-4 rounded-md transition-colors duration-200 flex items-center justify-center"
        >
          {isProcessing ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Processing...
            </>
          ) : (
            `Pay $${amount} ${currency}`
          )}
        </button>
      </form>

      <div className="text-xs text-gray-500 text-center">
        <div className="flex items-center justify-center mb-2">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          Secured by CyberSource
        </div>
        <p>Your payment information is encrypted and secure.</p>
      </div>
    </div>
  );
}
