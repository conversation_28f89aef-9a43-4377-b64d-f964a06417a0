import React, { useState } from "react";
import api from "@/server/api"; // Adjust to match your project path
import { useNavigate } from "react-router-dom";

export default function CyberSourcePaymentPage({
  amount = 100,
  currency = "USD",
  onSuccess,
  onError,
}) {
  const [error, setError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  const handlePayByLink = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      const res = await api.post("/cybersource/paybyrest", {
  "clientReferenceInformation": {
    "code": "TC50171_3"
  },
  "paymentInformation": {
    "card": {
      "number": "****************",
      "expirationMonth": "12",
      "expirationYear": "2031",
      // "securityCode": "123"
    }
  },
  "orderInformation": {
    "amountDetails": {
      "totalAmount": "102.21",
      "currency": "USD"
    },
    "billTo": {
      "firstName": "John",
      "lastName": "Doe",
      "address1": "1 Market St",
      "locality": "san francisco",
      "administrativeArea": "CA",
      "postalCode": "94105",
      "country": "US",
      "email": "<EMAIL>",
      "phoneNumber": "4158880000"
    }
  }
});

      const { url, params } = res.data;

      if (!url || !params || !params.signature) {
        throw new Error("Invalid response from server.");
      }

      // Dynamically build and submit the form
      const form = document.createElement("form");
      form.method = "POST";
      form.action = url;
      form.target = "_self"; // You can change to "_blank" to open in new tab
      form.style.display = "none";

      Object.entries(params).forEach(([key, value]) => {
        const input = document.createElement("input");
        input.type = "hidden";
        input.name = key;
        input.value = value;
        form.appendChild(input);
      });

      document.body.appendChild(form);
      form.submit();
    } catch (err) {
      console.error("Pay by link error:", err);
      setError("Failed to initiate CyberSource payment.");
      onError?.(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col gap-6 items-center justify-center p-6 bg-white border rounded shadow-md max-w-md mx-auto mt-10">
      <h2 className="text-xl font-semibold text-green-600">
        ✅ CyberSource Pay-by-Link
      </h2>

      <p className="text-sm text-gray-600 bg-yellow-100 p-2 rounded text-center">
        You will be securely redirected to CyberSource to complete your payment.
      </p>

      {error && (
        <div className="bg-red-100 text-red-700 p-3 rounded w-full text-center">
          {error}
        </div>
      )}

      <button
        onClick={handlePayByLink}
        disabled={isSubmitting}
        className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded transition disabled:opacity-50 w-full text-lg font-semibold"
      >
        {isSubmitting ? "Redirecting..." : `Pay $${amount} Now`}
      </button>

      <style>{`
        button {
          font-family: inherit;
        }
      `}</style>
    </div>
  );
}
