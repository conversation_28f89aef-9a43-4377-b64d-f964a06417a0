import React from "react";
import { Icon } from "@iconify/react";
import useFetch from "@/hooks/useFetch";
import moment from "moment";
import { BASE_URL } from "@/config";
import Loading from "@/components/Loading";

const Profile = () => {
  const {
    data: studentProfile,
    isLoading,
    isError,
  } = useFetch({
    queryKey: "studentProfile",
    endPoint: `${BASE_URL}/profile`,
  });

  // console.log(studentProfile);

  if (isLoading) return <Loading />;
  // console.log(isLoading);
  if (isError) return <div>Error fetching data</div>;

  return (
    <>
      <div className="rounded p-3">
        <div className="border-l-4 border-primary-500 rounded ps-2">
          <h6 className="mt-2">Profile Details</h6>
        </div>
        <div className="mt-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Name : {studentProfile.data.user.name}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                User Type : {studentProfile.data.user.user_type}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Contact Number : {studentProfile.data.contact_no}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Alternative Contact Number :{" "}
                {studentProfile.data.alternative_contact_no}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Student Code : {studentProfile.data.student_code}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Student ID : {studentProfile.data.student_id}</p>
            </div>
          </div>
          <div className="grid md:grid-cols-1 gap-4 mt-3">
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">BIO : {studentProfile.data.bio}</p>
            </div>
          </div>
        </div>
      </div>

      {/* //education details  */}

      <div className="bg-zinc- rounded p-3 my-3">
        <div className="border-l-4 border-primary-500 rounded ps-2">
          <h6 className="mt-2">Education Details</h6>
        </div>
        <div className="mt-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Education : {studentProfile.data.education}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Institute : {studentProfile.data.institute}</p>
            </div>
          </div>
        </div>
      </div>

      {/* //account overview */}

      <div className="rounded p-3 my-3">
        <div className="border-l-4 border-primary-500 rounded ps-2">
          <h6 className="mt-2">Account Overview</h6>
        </div>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>User Name : {studentProfile.data.username}</p>
          </div>
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>Email Address : {studentProfile.data.email}</p>
          </div>
          <div className="border p-2 rounded-lg flex justify-start items-start">
            <p className="inline-block my-1">
              Status:{" "}
              <span
                className={`p-2 rounded ${
                  studentProfile.data.is_active
                    ? "bg-green-500 text-white"
                    : "bg-red-500 text-white"
                }`}
              >
                {studentProfile.data.is_active ? "Active" : "Inactive"}
              </span>
            </p>
          </div>
          <div className="border p-2 rounded-lg flex justify-start items-start">
            <p className="inline-block my-1">
              Foreigner:{" "}
              <span
                className={`p-2 rounded ${
                  studentProfile.data.is_foreigner
                    ? "bg-green-500 text-white"
                    : "bg-red-500 text-white"
                }`}
              >
                {studentProfile.data.is_foreigner ? "Yes" : "No"}
              </span>
            </p>
          </div>
        </div>
      </div>

      {/* //personal details */}

      <div className="bg-zinc- rounded p-3 my-3">
        <div className="border-l-4 border-primary-500 rounded ps-2">
          <h6 className="mt-2">Personal Details</h6>
        </div>
        <div className="mt-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Father Name : {studentProfile.data.father_name}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Mother Name : {studentProfile.data.mother_name}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">NID Number : {studentProfile.data.nid_no}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Birth Certification Number :{" "}
                {studentProfile.data.birth_certificate_no}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Passport Number : {studentProfile.data.passport_no}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Date of Birthday :{" "}
                {moment(studentProfile.data.date_of_birth).format(
                  "Do-MMMM,YYYY"
                )}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Blood Group : {studentProfile.data.blood_group}
              </p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Gender : {studentProfile.data.gender}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">Religion : {studentProfile.data.religion}</p>
            </div>
            <div className="border border-iron-200 p-2 rounded-lg">
              <p className="">
                Marital Status : {studentProfile.data.marital_status}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* //address details */}

      <div className="rounded p-3 my-3">
        <div className="border-l-4 border-primary-500 rounded ps-2">
          <h6 className="mt-2">Address Details</h6>
        </div>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>Current Address : {studentProfile.data.current_address}</p>
          </div>
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>Permanent Address : {studentProfile.data.permanent_address}</p>
          </div>
        </div>
      </div>

      {/* //other details */}

      <div className="rounded p-3 my-3">
        <div className="border-l-4 border-primary-500 rounded ps-2">
          <h6 className="mt-2">Other Information</h6>
        </div>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>Device ID : {studentProfile.data.device_id}</p>
          </div>
          <div className="mt-4 border border-iron-200 p-3 rounded-lg">
            <p>Rating : {studentProfile.data.rating}</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Profile;
