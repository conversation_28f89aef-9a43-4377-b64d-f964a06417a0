import React, { useState } from "react";
import CyberSourcePaymentPage from "./CyberSourcePaymentPage";

export default function PaymentDemo() {
  const [showPayment, setShowPayment] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState(100);
  const [currency, setCurrency] = useState("USD");
  const [customerInfo, setCustomerInfo] = useState({
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    address1: "123 Main Street",
    city: "San Francisco",
    state: "CA",
    postalCode: "94105",
    country: "US",
    phoneNumber: "4155551234",
  });

  const handlePaymentSuccess = (response) => {
    console.log("Payment successful:", response);
    alert("Payment successful! Check console for details.");
  };

  const handlePaymentError = (error) => {
    console.error("Payment error:", error);
    alert("Payment failed! Check console for details.");
  };

  if (showPayment) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="mb-6">
            <button
              onClick={() => setShowPayment(false)}
              className="text-blue-600 hover:text-blue-800 flex items-center"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Demo Settings
            </button>
          </div>
          
          <CyberSourcePaymentPage
            amount={paymentAmount}
            currency={currency}
            customerInfo={customerInfo}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            CyberSource Payment Demo
          </h1>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Payment Configuration */}
            <div>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Payment Configuration
              </h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Amount
                  </label>
                  <input
                    type="number"
                    value={paymentAmount}
                    onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    min="0.01"
                    step="0.01"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Currency
                  </label>
                  <select
                    value={currency}
                    onChange={(e) => setCurrency(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                    <option value="CAD">CAD - Canadian Dollar</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Customer Information */}
            <div>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Customer Information
              </h2>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      First Name
                    </label>
                    <input
                      type="text"
                      value={customerInfo.firstName}
                      onChange={(e) => setCustomerInfo({...customerInfo, firstName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Last Name
                    </label>
                    <input
                      type="text"
                      value={customerInfo.lastName}
                      onChange={(e) => setCustomerInfo({...customerInfo, lastName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    value={customerInfo.email}
                    onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={customerInfo.phoneNumber}
                    onChange={(e) => setCustomerInfo({...customerInfo, phoneNumber: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Test Card Information */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-800 mb-3">
              Test Card Information
            </h3>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <p><strong>Visa:</strong> 4111 1111 1111 1111</p>
                <p><strong>Mastercard:</strong> 5555 5555 5555 4444</p>
                <p><strong>Amex:</strong> 3782 8224 6310 005</p>
              </div>
              <div>
                <p><strong>Expiry:</strong> Any future date (e.g., 12/25)</p>
                <p><strong>CVV:</strong> Any 3-4 digits (e.g., 123)</p>
              </div>
            </div>
          </div>

          {/* Action Button */}
          <div className="mt-8 text-center">
            <button
              onClick={() => setShowPayment(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-md transition-colors duration-200 text-lg"
            >
              Start Payment Process
            </button>
          </div>

          {/* Implementation Status */}
          <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">
              Implementation Status
            </h3>
            <div className="text-sm text-yellow-700">
              <p className="mb-2">✅ Frontend: Complete</p>
              <p className="mb-2">⚠️ Backend: Requires Laravel implementation</p>
              <p>
                Please refer to <code className="bg-yellow-100 px-1 rounded">CYBERSOURCE_IMPLEMENTATION_GUIDE.md</code> 
                for backend setup instructions.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
