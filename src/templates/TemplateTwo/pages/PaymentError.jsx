import React, { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";

export default function PaymentError() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [errorDetails, setErrorDetails] = useState({});

  useEffect(() => {
    const details = {
      reason: searchParams.get('reason'),
      message: searchParams.get('message'),
      referenceNumber: searchParams.get('reference_number'),
      amount: searchParams.get('amount'),
      currency: searchParams.get('currency') || 'USD',
      errorCode: searchParams.get('error_code')
    };
    setErrorDetails(details);
  }, [searchParams]);

  const handleTryAgain = () => {
    navigate(-1); // Go back to previous page
  };

  const handleBackHome = () => {
    navigate("/");
  };

  const handleContactSupport = () => {
    // You can customize this to your support page or contact method
    navigate("/support");
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {/* Error Icon */}
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
          <svg
            className="h-8 w-8 text-red-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>

        {/* Error Message */}
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Payment Error
        </h1>
        
        <p className="text-gray-600 mb-6">
          An error occurred while processing your payment. Please try again or contact support.
        </p>

        {/* Error Details */}
        {(errorDetails.reason || errorDetails.message || errorDetails.amount) && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            {errorDetails.amount && (
              <div className="mb-3">
                <div className="text-sm text-red-600 mb-1">Amount</div>
                <div className="text-lg font-semibold text-red-800">
                  ${errorDetails.amount} {errorDetails.currency}
                </div>
              </div>
            )}

            {errorDetails.referenceNumber && (
              <div className="mb-3">
                <div className="text-sm text-red-600 mb-1">Reference Number</div>
                <div className="text-sm font-mono text-red-800">
                  {errorDetails.referenceNumber}
                </div>
              </div>
            )}

            {errorDetails.reason && (
              <div className="mb-3">
                <div className="text-sm text-red-600 mb-1">Error</div>
                <div className="text-sm text-red-800 font-medium">
                  {errorDetails.reason}
                </div>
              </div>
            )}

            {errorDetails.message && (
              <div className="mb-3">
                <div className="text-sm text-red-600 mb-1">Details</div>
                <div className="text-sm text-red-800">
                  {errorDetails.message}
                </div>
              </div>
            )}

            {errorDetails.errorCode && (
              <div>
                <div className="text-sm text-red-600 mb-1">Error Code</div>
                <div className="text-sm font-mono text-red-800">
                  {errorDetails.errorCode}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleTryAgain}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-md transition-colors duration-200"
          >
            Try Again
          </button>
          
          <button
            onClick={handleContactSupport}
            className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-4 rounded-md transition-colors duration-200"
          >
            Contact Support
          </button>

          <button
            onClick={handleBackHome}
            className="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 px-4 rounded-md transition-colors duration-200"
          >
            Back to Home
          </button>
        </div>

        {/* Additional Info */}
        <div className="mt-6 text-xs text-gray-500">
          <p>
            If this error persists, please save the error details above and contact our support team.
          </p>
        </div>
      </div>
    </div>
  );
}
