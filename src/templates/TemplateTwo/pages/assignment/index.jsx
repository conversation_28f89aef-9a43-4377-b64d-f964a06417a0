import React, { useState } from "react";
import useFetch from "@/hooks/useFetch";
import { useNavigate } from "react-router-dom";
import GoBack from "@/components/ui/GoBack";
import AssignmentList from "./list";
import Select from "react-select";

const Assignment = () => {
  const [courseId, setCourseId] = useState("");
  const {
    data: response,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `student-assignment-list${courseId ? `?course_id=${courseId}` : ""}`,
    endPoint: `student-assignment-list${courseId ? `?course_id=${courseId}` : ""}`,
  });
  const assignments = response?.data;

  // Fetch course list
  const { data: courseList } = useFetch({
    queryKey: `my-course-list`,
    endPoint: `my-course-list`,
  });

  // if (isLoading) {
  //   return (
  //     <div className="min-h-[450px] flex flex-col items-center justify-center">
  //       <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-purple-500"></div>
  //       <p>Loading...</p>
  //     </div>
  //   );
  // }

  if (isError || !assignments) {
    return (
      <div className="min-h-[450px] flex flex-col items-center justify-center">
        <p className="text-red-500">You are not assigned with assignments</p>
      </div>
    );
  }

  return (
    <div className="container py-5 xl:py-10">
      <GoBack title={"My Assignments"} />

      {/* Course Filter Dropdown */}
      <div className="flex flex-col md:flex-row items-center gap-4 mb-6">
        <label htmlFor="courseFilter" className="text-sm font-medium">
          Filter by Course:
        </label>

        <Select
            className="w-72"
            placeholder="Select Course"
            options={courseList?.data?.map((course) => ({
                value: course.id,
                label: course.title,
            }))}
            name="course_id"
            onChange={(e) => {
                setCourseId(e.value);
            }}
        />
        {/* <select
          id="courseFilter"
          value={courseId}
          onChange={(e) => setCourseId(e.target.value)}
          className="border rounded-lg px-4 py-2 w-full md:w-auto"
        >
          <option value="">All Courses</option>
          {courseList?.data?.map((course) => (
            <option key={course.id} value={course.id}>
              {course.name}
            </option>
          ))}
        </select> */}
      </div>

      {/* Assignment List */}
      <div className="flex flex-col gap-10 mt-6">
        { isLoading ? 
        <div className="min-h-[450px] flex flex-col items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-purple-500"></div>
        <p>Loading...</p>
        </div> : 
        <AssignmentList assignments={assignments} />
        }
      </div>
    </div>
  );
};

export default Assignment;
