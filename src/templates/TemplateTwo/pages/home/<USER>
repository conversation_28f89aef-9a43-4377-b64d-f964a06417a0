import React, { useState } from "react";
import contactBG from "@/assets/images/all-img/contactBG.png";
import contactInfoIcon from "@/assets/images/all-img/contactInfoIcon.png";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Formik, Form } from "formik";
import Button from "@/components/ui/Button";
import * as Yup from "yup";
import InputField from "@/templates/TemplateTwo/components/form/InputField";
import TextAreaField from "@/templates/TemplateTwo/components/form/TextAreaField";
import api from "@/server/api";

const ContactUs = ({ organization }) => {
  const initialValues = {
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    message: ""
  };

  const validationSchema = Yup.object().shape({
    first_name: Yup.string().required("First name is required"),
    last_name: Yup.string().required("Last name is required"),
    email: Yup.string().email("Invalid email address").required("Email is required"),
    phone: Yup.string().required("Phone number is required"),
    message: Yup.string().required("Message is required")
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState("");

  const handleSubmit = async (values, { resetForm }) => {
    setIsSubmitting(true);
    setSubmitSuccess(false);
    setSubmitError("");

    try {
      // Combine first_name and last_name into a single name field
      const formData = {
        name: `${values.first_name} ${values.last_name}`,
        email: values.email,
        phone: values.phone,
        message: values.message,
        organization_id: organization?.id
      };

      // Send data to the API endpoint
      const response = await api.post("/submit-contact-form", formData);

      console.log("Form submission response:", response.data);
      setSubmitSuccess(true);
      resetForm();
    } catch (error) {
      console.error("Form submission error:", error);
      setSubmitError(error.response?.data?.message || "Failed to submit the form. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <div className="relative">
      <img
        className="relative lg:h-full w-full object-cover h-[95vh] md:h-[70vh] z-0 my-10"
        src={contactBG}
        alt=""
      />
      <div className="container bg-blue-100 rounded-xl p-10 max-sm:p-5 absolute left-1/2 z-10 top-1/2 -translate-y-1/2 -translate-x-1/2">
        <h4 className="text-xl font-semibold text-black text-center mb-5">
          Contact Us
        </h4>

        <div className="p-5 rounded-lg grid grid-cols-3 gap-4 bg-gray-50 border border-blue-200">
          <div className="relative bg-[#1B69B3] text-white lg:col-span-1 col-span-3 p-5 rounded space-y-2 text-sm">
            <h5 className="text-xl text-white">Contact Information</h5>
            <p className="text-base">
              Get in touch with us for any questions about our courses, programs, or services. We're here to help you achieve your learning goals.
            </p>

            <div className="flex gap-2">
              <Icon
                icon="bx:map"
                className="h-6 w-6 mt-1 text-white flex-shrink-0"
              />
              <p className="items-center">
                {organization?.address ? organization?.address : "Address not available"}
              </p>
            </div>
              <p className="flex items-center">
                <Icon
                  icon="mdi:phone"
                  className="h-6 w-6 mr-2 text-white flex-shrink-0"
                />
                {organization?.hotline_number || organization?.contact_no
                  ? [organization?.hotline_number, organization?.contact_no]
                      .filter(Boolean)
                      .join(" | ")
                  : "Phone number not available"}
              </p>
            <p className="flex items-center">
              <Icon
                icon="fluent:mail-16-regular"
                className="h-6 w-6 mr-2 text-white flex-shrink-0"
              />
              {organization?.email || "Email not available"}
            </p>

            <img
              className="absolute right-0 bottom-0"
              src={contactInfoIcon}
              alt="Contact decoration"
            />
          </div>

          <div className="lg:col-span-2 space-y-4 col-span-3">
            <Formik
              initialValues={initialValues}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
            >
              {({ isSubmitting: formikSubmitting }) => (
                <Form className="space-y-4">
                  <div className="flex items-center gap-5">
                    <div className="w-full">
                      <InputField
                        name="first_name"
                        type="text"
                        placeholder="Enter your first name"
                        label="First Name"
                        required
                      />
                    </div>
                    <div className="w-full">
                      <InputField
                        name="last_name"
                        type="text"
                        placeholder="Enter your last name"
                        label="Last Name"
                        required
                      />
                    </div>
                  </div>
                  <InputField
                    name="email"
                    type="email"
                    placeholder="Enter your email address"
                    label="Email Address"
                    required
                  />
                  <InputField
                    name="phone"
                    type="text"
                    placeholder="Enter your phone number"
                    label="Phone Number"
                    required
                  />
                  <TextAreaField
                    name="message"
                    placeholder="How can we help you? Type your message here..."
                    label="Message"
                    rows={4}
                    required
                  />
                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      className="btn bg-blue-500 hover:bg-blue-600 text-white rounded"
                      isLoading={isSubmitting || formikSubmitting}
                    >
                      Send Message
                    </Button>
                  </div>

                  {submitSuccess && (
                    <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                      <strong className="font-bold">Success!</strong>
                      <span className="block sm:inline"> Your message has been sent successfully.</span>
                    </div>
                  )}

                  {submitError && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                      <strong className="font-bold">Error!</strong>
                      <span className="block sm:inline"> {submitError}</span>
                    </div>
                  )}
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactUs;
