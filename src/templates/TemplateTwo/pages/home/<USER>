// import React from "react";
// import useFetch from "@/hooks/useFetch";
// import { Icon } from "@iconify/react";
// import Grid from "@/components/skeleton/Grid";
// import { useNavigate } from "react-router-dom";
// import { ASSET_URL } from "@/config";

// const CourseCard = ({ course }) => {
//   const navigate = useNavigate();
//   return (
//     <div
//       className="border rounded-b-lg relative bg-white cursor-pointer"
//       onClick={() => navigate(`/course-details/${course.id}`)}
//     >
//       <div className="relative">
//         <img
//           src={
//             course.thumbnail
//               ? `${ASSET_URL}${course.thumbnail}`
//               : "https://picsum.photos/200/300"
//           }
//           alt={course.title}
//           className="mb-4 w-full h-[200px] object-cover"
//         />
//         <div className="absolute inset-0 flex items-center justify-center">
//           <div className="bg-opacity-50 bg-white rounded-full p-4 flex items-center justify-center">
//             <div className="bg-white rounded-full p-3 flex items-center justify-center">
//               <Icon icon="mdi:play" className="h-8 w-8 text-danger-500" />
//             </div>
//           </div>
//         </div>
//       </div>
//       <div className="px-4 pb-4">
//         <h2 className="text-xl">{course.title}</h2>
//         <p className="text-gray-600 line-clamp-2 my-2">{course.description}</p>
//         <div className="flex justify-center">
//           <button className="border border-primary-500 text-primary-500 py-2 px-10 rounded hover:bg-primary-500 hover:text-white transition">
//             Enroll Now
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// };

// const Courses = () => {
//   const {
//     data: courseList,
//     isLoading,
//     isError,
//   } = useFetch({ queryKey: "courseList", endPoint: "course-list" });

//   if (isLoading) {
//     return (
//       <div className="container mx-auto">
//         <h1 className="mb-6 text-4xl text-primary-500">Our Courses</h1>
//         <Grid count={8} />
//       </div>
//     );
//   }
//   if (isError) return <div>Error fetching courses</div>;

//   return (
//     <div className="container mx-auto">
//       <h1 className="mb-6 text-4xl text-primary-500">Our Courses</h1>
//       <div className="grid grid-cols-4 gap-6">
//         {courseList.data.data.map((course) => (
//           <CourseCard key={course.id} course={course} />
//         ))}
//       </div>
//     </div>
//   );
// };

// export default Courses;

import { Icon } from "@iconify/react/dist/iconify.js";
import React, { useEffect, useRef, useState } from "react";
import { Link } from "react-router-dom";
import Course from "./Course";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import SwiperCore, { Autoplay, Navigation } from "swiper";
import "./swiper.css";
import { ASSET_URL } from "@/config";
import schoolIcon from "@/assets/images/svg/school.svg";

SwiperCore.use([Autoplay, Navigation]);

const Courses = ({ category, coursesCategory, categoryShow }) => {
  const swiperRef = useRef(null);
  const [isPrevDisabled, setPrevDisabled] = useState(true);
  const [isNextDisabled, setNextDisabled] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(
    category?.sub_categories && category.sub_categories.length > 0
      ? category.sub_categories[0].id
      : null
  );

  const filteredItem = category?.sub_categories?.filter(
    (item) => item.id === selectedCategory
  );
  // console.log(filteredItem[0]?.courses);

  // Update button states based on current index and total slides
  const updateButtonStates = () => {
    if (!swiperRef.current) return; // Check if swiperRef is initialized

    const swiper = swiperRef.current;
    const currentIndex = swiper.activeIndex;
    const totalSlides = swiper.slides.length;
    const slidesPerView = swiper.params.slidesPerView;

    // Disable the previous button if at the start
    setPrevDisabled(currentIndex === 0);

    // Disable the next button if we're at or beyond the last viewable slide
    setNextDisabled(currentIndex >= totalSlides - slidesPerView);
  };

  useEffect(() => {
    if (!swiperRef.current) return;

    const swiper = swiperRef.current;
    // Initial update
    updateButtonStates();

    // Add event listener
    swiper.on("slideChange", updateButtonStates);

    // Cleanup on unmount
    return () => {
      if (swiper) swiper.off("slideChange", updateButtonStates);
    };
  }, [coursesCategory?.length]);
  // console.log(courses);

  return (
    <div
      className={`z-30 container my-14 mb-24 ${
        coursesCategory?.length > 0 ? "text-center" : "text-start"
      }`}
    >
      <div className="container space-y-5 relative text-center mx-auto">
        <span className="bg-sky-100 text-sky-600 p-2 px-4 text-lg rounded-full">
          Courses
        </span>
        <h2 className="text-4xl text-sky-700 max-sm:text-3xl">
          {category?.name}
        </h2>
        {coursesCategory?.length > 0 && (
          <>
            <div className="relative py-2">
              <Swiper
                onSwiper={(swiper) => {
                  swiperRef.current = swiper; // Set the swiperRef directly
                  updateButtonStates(); // Update buttons initially after ref is set
                }}
                slidesPerView={8}
                spaceBetween={20}
                breakpoints={{
                  320: { slidesPerView: 2 },
                  640: { slidesPerView: 3 },
                  768: { slidesPerView: 4 },
                  1024: { slidesPerView: 8 },
                }}
              >
                <div className="mx-auto">
                  {coursesCategory?.length > 0 &&
                    coursesCategory?.map((item, index) => (
                      <SwiperSlide
                        key={index}
                        className="flex flex-col items-center pb-5 cursor-pointer"
                        onClick={() => setSelectedCategory(item?.id)}
                      >
                        <div
                          className={`p-2 rounded-lg shadow-md text-center w-full border ${
                            selectedCategory === item?.id
                              ? "bg-sky-100"
                              : "bg-white"
                          }`}
                        >
                          <img
                            src={item.icon ? ASSET_URL + item.icon : schoolIcon}
                            alt={item.name}
                            className="w-full h-12 mx-auto"
                          />
                          <p className="text-[17px] mt-1 text-sky-600">
                            {item.name?.length > 12
                              ? item.name.slice(0, 18) + ".."
                              : item.name}
                          </p>
                        </div>
                      </SwiperSlide>
                    ))}
                </div>
              </Swiper>

              <div
                className={`container absolute top-14 ${
                  coursesCategory?.length > 8 ? "" : "xl:hidden"
                }`}
              >
                <button
                  className={`absolute xl:-left-20 md:-left-0 max-sm:-left-5 top-1/2 transform -translate-y-1/2 p-3 bg-white rounded-full shadow-lg z-10 ${
                    isPrevDisabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  onClick={() => swiperRef.current.slidePrev()}
                  disabled={isPrevDisabled}
                >
                  <Icon
                    icon="ic:round-arrow-back"
                    className="text-2xl text-sky-600"
                  />
                </button>
                <button
                  className={`absolute xl:-right-20 md:-right-2 max-sm:-right-5 top-1/2 transform -translate-y-1/2 p-3 bg-white rounded-full shadow-lg z-10 ${
                    isNextDisabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  onClick={() => swiperRef.current.slideNext()}
                  disabled={isNextDisabled}
                >
                  <Icon
                    icon="ic:round-arrow-forward"
                    className="text-2xl text-sky-600"
                  />
                </button>
              </div>
            </div>
            <div className="border-b-2 border-dashed border-gray-300"></div>
          </>
        )}

        {/* {courses?.sub_categories[selectedCategory]?.courses?.map((course, idx) => ( */}

        {filteredItem?.map((subItem, idx) => (
          <div key={idx}>
            {subItem?.courses?.length > 0 ? (
              <>
                <div className="flex justify-between items-center">
                  <h2 className="text-4xl text-sky-700 max-sm:text-3xl text-start">
                    {subItem?.name}
                  </h2>
                  <Link
                    className="hover:bg-sky-50 py-2 w-24 hover:w-28 justify-center rounded-lg border border-sky-600 text-sky-600 flex items-center group gap-1 hover:gap-3 transition-all duration-300"
                    to={`/courses/${category?.id}/${subItem?.id}`}
                  >
                    See All
                    <Icon
                      icon="line-md:arrow-right"
                      className="text-lg transition-all duration-300 transform"
                    />
                  </Link>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 pt-6">
                  {subItem?.courses?.slice(0, 3)?.map((course, idx) => (
                    <Course key={idx} course={course} />
                  ))}
                </div>
              </>
            ) : (
              <p className="my-20">No Course available</p>
            )}
          </div>
        ))}
      </div>
      {/* ))} */}

      {/* {console.log(courses?.sub_categories[selectedCategory])} */}
    </div>
  );
};

export default Courses;
