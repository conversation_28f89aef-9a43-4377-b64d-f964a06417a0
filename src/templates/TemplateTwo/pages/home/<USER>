import React from "react";
import { Link } from "react-router-dom";
import { Icon } from "@iconify/react/dist/iconify.js";
import { ASSET_URL } from "@/config";

const HighlightSection = ({ promotionalItems }) => {
  return (
    <div className="py-16 bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <span className="inline-block mb-3 text-sm font-semibold text-primary-500 tracking-wider uppercase">
            Featured Collection
          </span>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Discover Our Highlights
          </h2>
          <div className="w-24 h-1.5 bg-primary-500 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our carefully selected offerings designed just for you
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
          {promotionalItems?.map((item, index) => (
            <div 
              key={index}
              className="group relative overflow-hidden rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 "
            >
              {/* Image Container */}
              <div className="relative h-72 overflow-hidden">
                <img
                  className="w-full h-full object-cover"
                  src={ASSET_URL + item.image}
                  alt={item.title}
                  loading="lazy"
                />
                {/* Overlay Gradient */}
                <div className="absolute inset-0 bg-black to-transparent opacity-90" />
              </div>

              {/* Content */}
              <div className="absolute inset-0 flex flex-col justify-end p-6">
                <div className="mb-4">
                  {item.category && (
                    <span className="inline-block px-3 py-1 text-xs font-medium tracking-wide text-white bg-primary-500 rounded-full mb-2">
                      {item.category}
                    </span>
                  )}
                  <h3 className="text-2xl font-bold text-white bg-black mb-2 transition-colors duration-300  bg-gray-900 bg-opacity-30  p-2 rounded">
                    {item.title}
                  </h3>
                  {item.description && (
                    <p className="text-gray-200 text-base mb-4 line-clamp-2 bg-gray-900 bg-opacity-30 p-2 rounded">
                      {item.description}
                    </p>
                  )}
                
                </div>
                <Link
                  to={item.link}
                  className="inline-flex items-center justify-between w-full max-w-[180px] bg-white/90 hover:bg-white text-gray-900 px-5 py-3 rounded-lg transition-all duration-300 group-hover:max-w-[200px]"
                >
                  <span className="font-medium">Explore</span>
                  <Icon 
                    icon="heroicons:arrow-right-20-solid" 
                    className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1"
                  />
                </Link>
              </div>

              {/* Top Badge */}
              {item.badge && (
                <div className="absolute top-4 right-4 bg-white text-gray-900 px-3 py-1 rounded-full text-xs font-bold tracking-wide shadow-sm">
                  {item.badge}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Optional View All Button */}
        <div className="text-center mt-16">
          <Link
            to="/highlights"
            className="inline-flex items-center justify-center px-8 py-3.5 border border-transparent text-base font-medium rounded-full text-white bg-primary-500 hover:bg-primary-600 transition-colors duration-300 shadow-sm hover:shadow-md"
          >
            View All Highlights
            <Icon icon="heroicons:chevron-right-20-solid" className="w-5 h-5 ml-2" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HighlightSection;