import React, { useEffect, useRef, useState } from "react";
import classIcon from "@/assets/images/all-img/classIcon.png";
import Course from "./Course";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper";
import "swiper/swiper-bundle.css";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Link } from "react-router-dom";
import Book from "./Book";
import {ASSET_URL} from "@/config";

const OurEBook = ({ courseCategories, allCourses }) => {
  const [category, setCategory] = useState("6-12 Class");
  const swiperRef = useRef(null);
  const [isBeginning, setIsBeginning] = useState(true); // Track if at the beginning
  const [isEnd, setIsEnd] = useState(false);
  const [showNavButtons, setShowNavButtons] = useState(true);

  const filteredCourses = allCourses?.filter(
    (data, idx) => data.category === category
  );

  const updateNavButtonsVisibility = () => {
    const swiperInstance = swiperRef.current?.swiper;
    if (swiperInstance) {
      const { slidesPerView } = swiperInstance.params;
      setShowNavButtons(filteredCourses.length > slidesPerView);
    }
  };

  const handleSlideChange = () => {
    if (swiperRef.current?.swiper) {
      const swiperInstance = swiperRef.current.swiper;
      setIsBeginning(swiperInstance.isBeginning);
      setIsEnd(swiperInstance.isEnd);
    }
  };

  useEffect(() => {
    handleSlideChange();
    updateNavButtonsVisibility();
  }, [filteredCourses]);
  return (
    <div className="relative py-20 xl:mt-8 bg-blue-100">
      <div className="container">
        <h2 className="text-3xl font-semibold text-center text-sky-700">
          Our E-Books
        </h2>

        <div className="flex items-center justify-center flex-wrap gap-4 mt-8">
          {courseCategories?.map((data, idx) => (
            <div
              key={idx}
              onClick={() => setCategory(data?.name)}
              className={`flex items-center px-5 py-1.5 gap-2 border rounded-full cursor-pointer ${
                category === data?.name ? "border-blue-500 bg-blue-100 text-blue-500" : " bg-white"
              }`}
            >
              <img
                className="w-6"
                src={data?.icon ? ASSET_URL + data?.icon : classIcon}
                alt=""
              />
              <span>
                <p className="text-md">{data.name}</p>
              </span>
            </div>
          ))}
        </div>

        <div className="grid max-sm:gap-10 gap-28 xl:gap-32 grid-cols-1 md:grid-cols-3 items-center mt-8">
          <div className="md:col-span-3">
            <div className="relative">
              {/* Custom Navigation Buttons */}
              {showNavButtons && (
                <>
                  <button
                    className={`absolute left-0 lg:-left-14 backclip-blur-sm top-1/2 transform -translate-y-1/2 bg-gray-100 z-20 text-gray-500 border border-blue-200 rounded-full p-2 shadow-md hover:bg-gray-50 ${
                      isBeginning ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                    onClick={() => swiperRef.current?.swiper?.slidePrev()}
                    disabled={isBeginning}
                  >
                    <Icon icon="mdi:chevron-left" className="text-2xl" />
                  </button>
                  <button
                    className={`absolute right-0 lg:-right-14 backclip-blur-sm top-1/2 transform -translate-y-1/2 bg-gray-100 z-20 text-gray-500 border border-blue-200 rounded-full p-2 shadow-md hover:bg-gray-50 ${
                      isEnd ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                    onClick={() => swiperRef.current?.swiper?.slideNext()}
                    disabled={isEnd}
                  >
                    <Icon icon="mdi:chevron-right" className="text-2xl" />
                  </button>
                </>
              )}
              <Swiper
                ref={swiperRef}
                modules={[Navigation]}
                spaceBetween={30}
                slidesPerView={2}
                navigation={false} // Disable default Swiper navigation
                onSlideChange={handleSlideChange}
                onInit={() => {
                  handleSlideChange();
                  updateNavButtonsVisibility();
                }}
                breakpoints={{
                  640: {
                    slidesPerView: 3,
                  },
                  768: {
                    slidesPerView: 4,
                  },
                  1024: {
                    slidesPerView: 6,
                  },
                }}
              >
                {category && filteredCourses?.length > 0 ? (
                  allCourses
                    ?.filter((data, idx) => data.category === category)
                    .map((data, idx) => (
                      <SwiperSlide key={idx}>
                        <Book course={data} index={idx} />
                      </SwiperSlide>
                    ))
                ) : (
                  <h2 className="text-xl border-t border-dashed border-sky-700 w-[90%] mx-auto col-span-3 pt-4 font-semibold text-center text-sky-700">
                    No Course Available
                  </h2>
                )}
              </Swiper>

              {/* {filteredCourses?.length > 0 && (
                <Link
                  to="/courses"
                  className="sm:w-52 px-3 mt-8 py-2 mx-auto text-center block rounded-2xl border hover:border-sky-600 flex items-center justify-center gap-2 gap-5 bg-[#13497C] hover:bg-gray-100 hover:text-blue-500 text-white duration-300"
                >
                  Explore Courses
                  <Icon icon="line-md:arrow-right" className="text-lg" />
                </Link>
              )} */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OurEBook;
