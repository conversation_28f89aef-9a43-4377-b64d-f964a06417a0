import React, { useState, useRef, useEffect } from "react";
import { Icon } from "@iconify/react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper";
import "swiper/swiper-bundle.css";
import DefaultAvatar from "@/assets/images/all-img/user5.jpeg";


const TestimonialTabs = ({ clientData }) => {
  const [activeTab, setActiveTab] = useState("all"); // Set default to "all"
  const swiperRef = useRef(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const [showNavButtons, setShowNavButtons] = useState(true);

  // Filter testimonials based on active tab
  const filteredTestimonials = activeTab === "all" 
    ? clientData 
    : clientData?.filter((client) => {
        return client.category.toLowerCase() === activeTab.toLowerCase();
      });

  // Check visibility of navigation buttons
  const updateNavButtonsVisibility = () => {
    const swiperInstance = swiperRef.current?.swiper;
    if (swiperInstance) {
      const { slidesPerView } = swiperInstance.params;
      setShowNavButtons(filteredTestimonials?.length > slidesPerView);
    }
  };

  // Handle Swiper state updates
  const handleSlideChange = () => {
    if (swiperRef.current?.swiper) {
      const swiperInstance = swiperRef.current.swiper;
      setIsBeginning(swiperInstance.isBeginning);
      setIsEnd(swiperInstance.isEnd);
    }
  };

  useEffect(() => {
    updateNavButtonsVisibility();
  }, [filteredTestimonials, activeTab]);

  return (
    <div className="bg-[#EFF6FF]">
      <div className="container relative">
        <div className="py-16 space-y-6">
          {/* Tabs - Added "All" as first tab */}
          <div className="flex justify-center pb-6">
            {["all", "teacher", "student", "guardian"].map((category) => (
              <button
                key={category}
                onClick={() => setActiveTab(category)}
                className={`bg-white px-6 py-3 text-sm w-full font-semibold ${
                  activeTab === category
                    ? "border-b-2 border-blue-300 text-blue-600"
                    : "text-gray-600 hover:text-gray-800"
                }`}
              >
                {category === "all" 
                  ? "All" 
                  : category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>

          {/* Testimonials */}
          <div className="grid max-sm:gap-10 gap-28 xl:gap-32 grid-cols-1 md:grid-cols-3 items-center">
            <div className="md:col-span-3">
              <div className="relative">
                {/* Custom Navigation Buttons */}
                {showNavButtons && (
                  <>
                    <button
                      className={`absolute left-0 lg:-left-14 backclip-blur-sm top-1/2 transform -translate-y-1/2 bg-gray-100 z-20 text-gray-500 border border-blue-200 rounded-full p-2 shadow-md hover:bg-gray-50 ${
                        isBeginning ? "opacity-50 cursor-not-allowed" : ""
                      }`}
                      onClick={() => swiperRef.current?.swiper?.slidePrev()}
                      disabled={isBeginning}
                    >
                      <Icon icon="mdi:chevron-left" className="text-2xl" />
                    </button>
                    <button
                      className={`absolute right-0 lg:-right-14 backclip-blur-sm top-1/2 transform -translate-y-1/2 bg-gray-100 z-20 text-gray-500 border border-blue-200 rounded-full p-2 shadow-md hover:bg-gray-50 ${
                        isEnd ? "opacity-50 cursor-not-allowed" : ""
                      }`}
                      onClick={() => swiperRef.current?.swiper?.slideNext()}
                      disabled={isEnd}
                    >
                      <Icon icon="mdi:chevron-right" className="text-2xl" />
                    </button>
                  </>
                )}

                <Swiper
                  ref={swiperRef}
                  modules={[Navigation]}
                  spaceBetween={30}
                  slidesPerView={1}
                  navigation={false}
                  onSlideChange={handleSlideChange}
                  onInit={() => {
                    handleSlideChange();
                    updateNavButtonsVisibility();
                  }}
                  breakpoints={{
                    640: {
                      slidesPerView: 1,
                    },
                    768: {
                      slidesPerView: 2,
                    },
                    1024: {
                      slidesPerView: 3,
                    },
                  }}
                >
                  {filteredTestimonials?.map((client, index) => (
                    <SwiperSlide key={index}>
                      <div className="flex items-center gap-3">
                        <img
                          className="h-20 w-20 rounded-full"
                          src={client.image}
                          alt={client.name}
                          onError={(e) => {
                            e.target.src = DefaultAvatar;
                          }}
                        />
                        <div>
                          <h3 className="text-xl text-blue-500">{client.name}</h3>
                          <p className="text-gray-600">{client.title}</p>
                          <p className="text-sm text-gray-500">{client.companyName}</p>
                        </div>
                      </div>
                      <div className="p-5 rounded-lg relative shadow-md mb-5 mt-10 bg-[#E8F0F7]">
                        <div className="h-12 w-12 rotate-45 border-t border-l border-gray-200 absolute -top-6 z-0 bg-[#E8F0F7]"></div>
                        <p className="text-lg relative z-10">
                          <Icon
                            icon="ri:double-quotes-l"
                            className="text-2xl inline mr-1 text-sky-600"
                          />
                          {client.testimonial}
                          <Icon
                            icon="ri:double-quotes-r"
                            className="text-2xl inline ml-1 text-sky-600"
                          />
                        </p>
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialTabs;