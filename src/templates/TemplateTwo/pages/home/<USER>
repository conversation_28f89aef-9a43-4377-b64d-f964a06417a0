import React from "react";
import PropTypes from "prop-types";

const Expert = ({ expert }) => {
  return (
    <div className="flex flex-col md:flex-row border border-pictonBlue-500 rounded-lg shadow-md gap-3 p-2 text-white h-48 w-full md:w-96">
      
      <div className="flex gap-2">
      <img
        src={expert.image}
        alt={expert.name}
        className="w-12 h-12 object-cover rounded-full my-auto md:my-0"
      />

        <div className="flex md:hidden block items-center">
          <h3 className="text-xl text-white font-semibold mb-2 md:mb-0">{expert.name}</h3>
        </div>
      </div>
      {/* <img
        src={expert.image}
        alt={expert.name}
        className="w-20 h-20 object-cover rounded-full my-auto md:my-0"
      /> */}
      <div className="md:gap-2">
        <div className="flex md:block hidden">
          <h3 className="text-xl text-white font-semibold mb-2 md:mb-0">{expert.name}</h3>
        </div>
        <p className="mb-2" style={{ WebkitLineClamp: 2, overflow: "hidden", textOverflow: "ellipsis", display: "-webkit-box", WebkitBoxOrient: "vertical" }}>{expert.bio.slice(0, 120)}{expert.bio.length > 120 && "..."}</p>
        <p className="text-sm mb-2">{expert.institute}</p>
      </div>
    </div>
  );
};

// Expert.propTypes = {
//   expert: PropTypes.shape({
//     name: PropTypes.string.isRequired,
//     bio: PropTypes.string.isRequired,
//     email: PropTypes.string.isRequired,
//     institute: PropTypes.string.isRequired,
//   }).isRequired,
// };

export default Expert;
