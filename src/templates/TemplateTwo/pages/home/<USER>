import React from "react";
import { Icon } from "@iconify/react";
import { ASSET_URL } from "@/config";
import DemoCourseImage from "@/assets/course_demo.jpg";
import { useNavigate } from "react-router-dom";
import Rating from "@/components/ui/Rating";

const Course = ({ course, index, purchased = false, boughtCourses = [] }) => {
  const navigate = useNavigate();

  return (
    <div
      onClick={() => navigate(course.slug ? `/course/${course.slug}` : `/course-details/${course.id}`)}
      className="border border-gray-200 rounded-xl shadow-sm hover:shadow-lg transition-shadow duration-300 bg-white group cursor-pointer overflow-hidden flex flex-col"
    >
      <div className="relative overflow-hidden">
        <img
          src={course.thumbnail ? ASSET_URL + course.thumbnail : DemoCourseImage}
          alt={course.title}
          className="w-full h-44 object-cover transition-transform duration-300 group-hover:scale-105"
        />
      </div>

      <div className="flex-1 p-4 flex flex-col justify-between">
        <div className="flex items-center justify-between mb-2">
          {course.rating > 0 &&  <Rating rating={course.rating} /> }
          {boughtCourses?.includes(course.id) ? (
            <span className="bg-green-100 text-green-600 px-2 py-0.5 rounded-full text-xs font-medium">Enrolled</span>
          ) : (
            <span className="bg-red-100 text-red-600 px-2 py-0.5 rounded-full text-xs font-medium">Enroll Now</span>
          )}
        </div>

        <h2 className="text-sm font-semibold text-gray-800 line-clamp-2 mb-1">{course.title}</h2>

        {course?.mentors.length === 1 && (
          <p className="text-xs text-gray-500 mb-2">by {course.mentors[0].name}</p>
        )}
        {course?.mentors.length > 1 && (
          <p className="text-xs text-gray-500 mb-2">
            by {course.mentors[0].name} and {course.mentors.length - 1} others
          </p>
        )}

        {!purchased && course?.show_price && (
          <div className="text-sm text-gray-700 font-medium mb-2">
            {course.is_free ? (
              <span className="text-green-600">Free</span>
            ) : course?.monthly_amount > 0 && course?.installment_type === "Monthly" ? (
              <span>

                {course.monthly_amount?.toLocaleString() + ' '}
                
                 {course.currency}/Month</span>
            ) : course?.sale_price && course?.regular_price ? (
              <span>
                <span className="text-red-500">{course.sale_price?.toLocaleString()} {course.currency}</span>
                { course.regular_price > course.sale_price && (
                  <span className="text-gray-400 line-through ml-2">{course.regular_price?.toLocaleString()} {course.currency}</span>
                )}
                {/* <span className="text-gray-400 line-through ml-2">{course.regular_price?.toLocaleString()} {course.currency}</span> */}
              </span>
            ) : null}
          </div>
        )}
      </div>

      <div className="w-full mt-auto border-t bg-white px-4 py-3 flex items-center justify-center gap-2 transition-all duration-300 group-hover:bg-sky-600 group-hover:text-white text-sky-600 font-semibold">
        See Details
        <Icon icon="line-md:arrow-right" className="text-lg transition-transform group-hover:translate-x-1" />
      </div>
    </div>
  );
};

export default Course;
