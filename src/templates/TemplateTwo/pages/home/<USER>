import React, { useState, useRef, useEffect } from "react";
import Testimonial from "./Testimonial";
// import CallToAction from "./CallToAction";
// import Courses from "./Courses";
import HeroSection from "./HeroSection";
import Experts from "./Experts";
import Loading from "@/components/Loading";
import { useSelector } from "react-redux";
import Course from "./Course";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import SwiperCore, { Autoplay, Navigation } from "swiper";
import "./swiper.css";

import MobileApp from "./MobileApp";
import useFetch from "@/hooks/useFetch";
import catBg from "@/assets/images/all-img/cat.png";
import { ASSET_URL } from "@/config";
import ContactUs from "./ContactUs";
import HighlightSection from "./HighlightSection";
import AllCourses from "./AllCourses";
import OurEBook from "./OurEBook";

SwiperCore.use([Autoplay, Navigation]);

const Home = () => {
  const { organization } = useSelector((state) => state.commonSlice);
  const { data: landingPageData = [], isLoading } = useFetch({
    queryKey: "homeDetails",
    endPoint: `landing-page-information-temp2?id=${organization.id}`,
  });
  const prevRef = useRef(null);
  const nextRef = useRef(null);
  const courseCategories = landingPageData?.data?.category_with_courses;
  const promotionalItems = landingPageData?.data?.promotional_items;


  const { data: testimonials = [], isTestimonialLoading } = useFetch({
    queryKey: "testimonials",
    endPoint: `testimonials`,
  });

  const clientData = testimonials?.data?.map((testimonial) => ({
    id: testimonial.id,
    name: testimonial.name,
    title: testimonial.designation,
    companyName: testimonial.user_type,
    category: testimonial.user_type,
    testimonial: testimonial.message,
    image: ASSET_URL + testimonial.image,
  }));



  if (isLoading) {
    return <Loading />;
  }

  return (
    <div>
      <div className="mx-auto">
        <HeroSection organization={organization} />
      </div>


      <AllCourses courseCategories={courseCategories} />

      <HighlightSection promotionalItems={promotionalItems} />

      {/* <OurEBook courseCategories={courseCategories} allCourses={allCourses} /> */}

      {/* contact us section  */}
      <ContactUs organization={organization} />

      {clientData && clientData?.length && <Testimonial clientData={clientData} />}

      {/* mobile app section  */}
      <MobileApp organization={organization} />
    </div>
  );
};

export default Home;
