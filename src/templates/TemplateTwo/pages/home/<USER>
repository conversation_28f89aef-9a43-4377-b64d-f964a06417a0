import React, { useEffect, useState } from "react";
import one from "@/assets/sass-lms/1.png";
import two from "@/assets/sass-lms/2.png";
import three from "@/assets/sass-lms/3.png";
import four from "@/assets/sass-lms/4.png";
import { Link, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import "swiper/swiper.min.css";
import SwiperCore, { Autoplay } from "swiper";
import heroBgImg from "@/assets/images/all-img/heroBg1.png";
import heroMoveIcon from "@/assets/images/all-img/heroMoveIcon.png";
import heroShape from "@/assets/images/all-img/heroShape.png";
import heroImg2 from "@/assets/images/all-img/heroImg2.png";
import heroCourseIcon from "@/assets/images/all-img/heroCourseIcon.png";
import heroUserIcon from "@/assets/images/all-img/heroUserIcon.png";
import heroBooksIcon from "@/assets/images/all-img/heroBooksIcon.png";
import heroInstructorIcon from "@/assets/images/all-img/heroInstructorIcon.png";
import { ASSET_URL } from "@/config";
SwiperCore.use([Autoplay]);

const HeroSection = ({ organization }) => {
  const { isAuth } = useSelector((state) => state.auth);
  const { labels } = useSelector((state) => state.language);
  const [count, setCount] = useState(0);
  // Fallback for invalid or undefined studentsCount
  const targetCount = Number(organization.custom_course_number + organization.courses_count) || 0;
  const steps = 10;
  const incrementValue = Math.ceil(targetCount / steps);
  const [transformStyle, setTransformStyle] = useState("");

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const translateY = scrollY * 0.5;


      setTransformStyle(`translateY(${translateY}px)`);
    };

    window.addEventListener("scroll", handleScroll);


    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    if (targetCount === 0) return;

    const interval = setInterval(() => {
      setCount((prev) => {
        if (prev + incrementValue >= targetCount) {
          clearInterval(interval);
          return targetCount;
        }
        return prev + incrementValue;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [incrementValue, targetCount]);

  // console.log(organization);

  return (
    <div>
      <div className="py-10  relative overflow-hidden">
        {/* Background Images */}
        <img
          src={heroBgImg}
          className="max-sm:opacity-25 absolute left-0 top-0 z-0 h-full object-cover"
          alt=""
        />
        <img
          src={heroMoveIcon}
          style={{ transform: transformStyle }}
          className="max-sm:opacity-50 absolute right-0 bottom-0 z-0 transition-transform duration-75"
          alt="Hero Icon"
        />

        {/* Content Container */}
        <div className="flex items-center">
          <div className="container mx-auto flex flex-col lg:flex-row items-center gap-5 z-10 py-10">
            {/* Left Column */}
            <div className="flex-1 space-y-5 lg:text-left">
              <h2 className="text-2xl md:text-4xl text-[#1B69B3]">
                {organization.headline}
              </h2>
              <p className="text-sm md:text-base">{organization.sub_headline}</p>
              <div className="flex items-center gap-5 max-sm:gap-2">
                <Link
                  to="/courses"
                  className="sm:w-52 px-3 max-sm:py-2 py-3 text-center hover:text-sky-600 block rounded-full border border-sky-600 flex items-center justify-center hover:bg-gray-100 hover:gap-2 gap-5 bg-sky-700 text-white duration-300"
                >
                  {labels?.["Explore Courses"] || "Explore Courses"}
                  <Icon icon="line-md:arrow-right" className="text-lg" />
                </Link>
              </div>
            </div>

            <div className="">
              <img
                className="xl:max-w-2xl md:max-w-xl relative z-10 "
                src={organization?.banner ? ASSET_URL + organization?.banner : heroImg2}
                alt=""
              />
              <img
                className="absolute -right-20 bottom-0 z-0"
                src={heroShape}
                alt=""
              />
            </div>
          </div>
        </div>
      </div>

      <div className="relative z-20 flex items-center justify-center my-6 mx-5">
        <div className="flex items-center justify-around container bg-white rounded-xl absolute p-8 border border-blue-100 text-lg max-sm:text-base max-sm:text-center shadow-lg">
          <div className="flex items-center justify-center flex-col gap-2">
            <img className="max-sm:w-10 mx-auto" src={heroCourseIcon} alt="" />
            <p className="text-blue-600">
              <span className="text-2xl max-sm:text-xl font-semibold">
                {count}+
              </span>{" "}
              Courses
            </p>
          </div>
          <div className="flex items-center justify-center flex-col gap-2">
            <img className="max-sm:w-10 mx-auto" src={heroUserIcon} alt="" />
            <p className="text-blue-600">
              <span className="text-2xl max-sm:text-xl font-semibold">
                {organization?.custom_student_number + organization?.students_count}
              </span>{" "}
              Students
            </p>
          </div>
          <div className="flex items-center justify-center flex-col gap-2">
            <img className="max-sm:w-10 mx-auto" src={heroBooksIcon} alt="" />
            <p className="text-blue-600">
              <span className="text-2xl max-sm:text-xl font-semibold">
                {organization?.custom_book_number}
              </span>{" "}
              Books
            </p>
          </div>
          <div className="flex items-center justify-center flex-col gap-2">
            <img
              className="max-sm:w-10 mx-auto"
              src={heroInstructorIcon}
              alt=""
            />
            <p className="text-blue-600">
              <span className="text-2xl max-sm:text-xl font-semibold">

                {organization?.custom_instructor_number + organization?.mentors_count}
              </span>{" "}
              Instructors
            </p>
          </div>
        </div>
      </div>
      <div className="h-16"></div>
    </div>
  );
};

export default HeroSection;
