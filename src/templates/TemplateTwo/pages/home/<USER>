import React from "react";
import { Icon } from "@iconify/react";
import { ASSET_URL } from "@/config";
import DemoCourseImage from "@/assets/course_demo.jpg";
import bookImg from "@/assets/images/all-img/bookImg.jpg";
import { useNavigate } from "react-router-dom";
import Tooltip from "@/components/ui/Tooltip";
import Rating from "@/components/ui/Rating";
const Book = ({ course, index, purchased = false }) => {
  const navigate = useNavigate();
  // console.log(course);
  return (
    // <Tooltip content={course.name} placement="top">
    <div
      onClick={() => navigate(`/course-details/${course.id}`)}
      className="card rounded-lg relative pb-10 group cursor-pointer"
      // onClick={() => navigate(`/course-details/${course.id}`)}
    >
      <img
        src={course.thumbnail ? ASSET_URL + course.thumbnail : bookImg}
        alt={course.title}
        className="card-img-top rounded-t-lg w-full h-60 object-cover"
      />
      <div className="card-body py-2">
        {/* <div className="flex items-center justify-between">
            <Rating rating={course.rating} />
            {course.isEnrolled ? (
                <p className="text-green-400 font-semibold text-sm">Enrolled</p>
            ) : (
                <p className="text-red-400 font-semibold text-sm">Enroll Now</p>
            )}
            </div> */}
        <h2 className="card-title text-sm text-sky-700 truncate text-left">
          {course.title}
        </h2>
        <div className="card-text text-gray-600 flex items-center text-right justify-between text-xs">
          {course?.mentors.length > 0 && (
            <span className="">
              {course?.mentors[0]?.name +
                ` and ${course?.mentors?.length - 1} others`}
            </span>
          )}
          {/* {course.description.slice(0, 50)} {course.description.length > 50 && "..."}  */}
        </div>
        {!purchased && (
          <div
            className={`text-sm py-1 rounded ${
              course.is_free ? "text-sky-600" : "text-sky-600"
            }`}
          >
            {course.is_free ? (
              "Free"
            ) : course?.monthly_amount > 0 &&
              course?.installment_type === "Monthly" ? (
              <span> {course.monthly_amount} {course?.currency} /Month</span>
            ) : course?.sale_price && course?.regular_price ? (
              <p className="flex justify-start items-center font-semibold gap-3 text-red-500">
                 {course.sale_price} {course?.currency}
                <span className="text-gray-500 line-through">
                   {course.regular_price} {course?.currency}
                </span>
              </p>
            ) : null}
          </div>
        )}
      </div>
    </div>
    // </Tooltip>
  );
};

export default Book;
