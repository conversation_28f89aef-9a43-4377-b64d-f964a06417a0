import React, { useEffect, useRef, useState } from "react";
import classIcon from "@/assets/images/all-img/classIcon.png";
import Course from "./Course";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper";
import "swiper/swiper-bundle.css";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Link } from "react-router-dom";
import { ASSET_URL } from "@/config";

const AllCourses = ({ courseCategories }) => {
  const [category, setCategory] = useState(courseCategories[0]?.id);
  const swiperRef = useRef(null);
  const [isBeginning, setIsBeginning] = useState(true); // Track if at the beginning
  const [isEnd, setIsEnd] = useState(false);
  const [showNavButtons, setShowNavButtons] = useState(true);

  const filteredCourses = courseCategories?.find(
    (data, idx) => data.id === category
  )?.courses;


  const updateNavButtonsVisibility = () => {
    const swiperInstance = swiperRef.current?.swiper;
    if (swiperInstance) {
      const { slidesPerView } = swiperInstance.params;
      setShowNavButtons(filteredCourses?.length > slidesPerView);
    }
  };

  const handleSlideChange = () => {
    if (swiperRef.current?.swiper) {
      const swiperInstance = swiperRef.current.swiper;
      setIsBeginning(swiperInstance.isBeginning);
      setIsEnd(swiperInstance.isEnd);
    }
  };

  useEffect(() => {
    handleSlideChange();
    updateNavButtonsVisibility();
  }, [filteredCourses]);
  return (
    <div className="relative py-20 xl:mt-8 bg-[#13497C]">
      <div className="container">
        <h2 className="text-3xl font-semibold text-center text-white">
          All Courses
        </h2>

        <div className="flex items-center justify-center flex-wrap gap-4 mt-8">
          {courseCategories?.map((data, idx) => (
            <div
              key={idx}
              onClick={() => setCategory(data?.id)}
              className={`flex items-center px-5 py-1.5 gap-2 border rounded-full cursor-pointer ${
                category == data?.id
                  ? "border-blue-500 bg-blue-100 text-blue-500"
                  : " bg-white"
              }`}
            >
              <img
                className="w-8"
                src={data?.icon ? ASSET_URL + data?.icon : classIcon}
                alt=""
              />
              <span>
                <p className="text-md">{data.name}</p>
                <p className="text-sm">({data?.courses?.length} courses)</p>
              </span>
            </div>
          ))}
        </div>

        <div className="grid max-sm:gap-10 gap-28 xl:gap-32 grid-cols-1 md:grid-cols-3 items-center mt-8">
          <div className="md:col-span-3">
            <div className="relative">
              {/* Custom Navigation Buttons */}
              {showNavButtons && (
                <>
                  <button
                    className={`absolute left-0 lg:-left-14 backclip-blur-sm top-1/2 transform -translate-y-1/2 bg-[#FEFEFE33] z-20 text-white border border-blue-200 rounded-full p-2 shadow-md ${
                      isBeginning ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                    onClick={() => swiperRef.current?.swiper?.slidePrev()}
                    disabled={isBeginning}
                  >
                    <Icon icon="mdi:chevron-left" className="text-2xl" />
                  </button>
                  <button
                    className={`absolute right-0 lg:-right-14 backclip-blur-sm top-1/2 transform -translate-y-1/2 bg-[#FEFEFE33] z-20 text-white border border-blue-200 rounded-full p-2 shadow-md ${
                      isEnd ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                    onClick={() => swiperRef.current?.swiper?.slideNext()}
                    disabled={isEnd}
                  >
                    <Icon icon="mdi:chevron-right" className="text-2xl" />
                  </button>
                </>
              )}
              <Swiper
                ref={swiperRef}
                modules={[Navigation]}
                spaceBetween={30}
                slidesPerView={1}
                navigation={false} // Disable default Swiper navigation
                onSlideChange={handleSlideChange}
                onInit={() => {
                  handleSlideChange();
                  updateNavButtonsVisibility();
                }}
                breakpoints={{
                  640: {
                    slidesPerView: 1,
                  },
                  768: {
                    slidesPerView: 2,
                  },
                  1024: {
                    slidesPerView: 3,
                  },
                }}
              >
                {category && filteredCourses?.length > 0 ? (
                  filteredCourses.map((data, idx) => (
                    <SwiperSlide key={idx}>
                      <Course course={data} index={idx} />
                    </SwiperSlide>
                  ))
                ) : (
                  <h2 className="text-2xl border-t border-dashed w-[90%] mx-auto col-span-3 pt-4 font-semibold text-center text-white">
                    No Course Available
                  </h2>
                )}
              </Swiper>

              {/* {showNavButtons && ( */}
                <Link
                  to="/courses"
                  className="sm:w-52 px-3 mt-8 py-2 mx-auto text-center block rounded-2xl border hover:border-sky-600 flex items-center justify-center gap-2 gap-5 bg-[#13497C] hover:bg-gray-100 hover:text-blue-500 text-white duration-300"
                >
                  Explore Courses
                  <Icon icon="line-md:arrow-right" className="text-lg" />
                </Link>
              {/* )} */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AllCourses;
