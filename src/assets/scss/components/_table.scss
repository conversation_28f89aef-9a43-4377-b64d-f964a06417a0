.table-th {
    @apply text-slate-600 dark:text-slate-300 text-xs  font-bold  uppercase py-4 px-6 ltr:text-left rtl:text-right;
}

.table-td {
    @apply text-slate-600 dark:text-slate-300 text-sm font-normal capitalize px-6 py-4 border-slate-100 dark:border-slate-700;
}

.table-checkbox {
    @apply relative before:flex before:flex-col before:items-center before:justify-center   
  before:w-[18px] before:h-[18px] before:m-[-0.7px] before:bg-slate-400 dark:before:bg-slate-500  before:absolute
    before:inset-0 before:rounded w-4 h-4 rounded checked:before:bg-primary-400 
    checked:before:content-[url("https://api.iconify.design/heroicons-outline/check.svg?color=white")] cursor-pointer checked:before:leading-[10px];
}

.striped-table {
    @apply min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700;
}
.striped-table thead {
    @apply bg-jaggedice-200 dark:bg-slate-700;
}
.striped-table tbody {
    @apply bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700;
}
.striped-table th {
    @apply text-slate-600 dark:text-slate-300 text-xs  font-semibold  uppercase py-5 px-6 ltr:text-left rtl:text-right;
}
.striped-table td {
    @apply text-slate-600 dark:text-slate-300 text-sm font-normal px-6 py-4 border-slate-100 dark:border-slate-700;
}
.striped-table tbody > tr {
    @apply even:bg-blacksqueeze-50 dark:even:bg-slate-700;
}
