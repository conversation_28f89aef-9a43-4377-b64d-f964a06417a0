/* CKEditor Content Display Fix - Comprehensive CSS */
/* This file ensures CKEditor content displays properly with Tailwind CSS */

/* High specificity selectors to override Tailwind CSS resets */
.ck-content ul:not(.custom-list),
.editor-data-content ul:not(.custom-list),
.ck-content ol:not(.custom-list),
.editor-data-content ol:not(.custom-list) {
  list-style-position: outside !important;
  margin: 1em 0 !important;
  padding-left: 40px !important;
}

.ck-content ul:not(.custom-list),
.editor-data-content ul:not(.custom-list) {
  list-style-type: disc !important;
}

.ck-content ol:not(.custom-list),
.editor-data-content ol:not(.custom-list) {
  list-style-type: decimal !important;
}

.ck-content ul:not(.custom-list) ul:not(.custom-list),
.editor-data-content ul:not(.custom-list) ul:not(.custom-list) {
  list-style-type: circle !important;
  margin: 0.5em 0 !important;
}

.ck-content ul:not(.custom-list) ul:not(.custom-list) ul:not(.custom-list),
.editor-data-content ul:not(.custom-list) ul:not(.custom-list) ul:not(.custom-list) {
  list-style-type: square !important;
}

.ck-content li:not(.custom-list),
.editor-data-content li:not(.custom-list) {
  display: list-item !important;
  margin: 0.25em 0 !important;
  line-height: 1.6 !important;
}

/* Headings */
.ck-content h1,
.editor-data-content h1 {
  font-size: 2.25rem !important;
  font-weight: 700 !important;
  margin: 1.5rem 0 1rem 0 !important;
  line-height: 1.2 !important;
}

.ck-content h2,
.editor-data-content h2 {
  font-size: 1.875rem !important;
  font-weight: 600 !important;
  margin: 1.25rem 0 0.75rem 0 !important;
  line-height: 1.3 !important;
}

.ck-content h3,
.editor-data-content h3 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin: 1rem 0 0.5rem 0 !important;
  line-height: 1.4 !important;
}

.ck-content h4,
.editor-data-content h4 {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0.75rem 0 0.5rem 0 !important;
  line-height: 1.4 !important;
}

.ck-content h5,
.editor-data-content h5 {
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  margin: 0.75rem 0 0.25rem 0 !important;
  line-height: 1.5 !important;
}

.ck-content h6,
.editor-data-content h6 {
  font-size: 1rem !important;
  font-weight: 600 !important;
  margin: 0.5rem 0 0.25rem 0 !important;
  line-height: 1.5 !important;
}

/* Paragraphs */
.ck-content p,
.editor-data-content p {
  margin: 1em 0 !important;
  line-height: 1.6 !important;
}

/* Text formatting */
.ck-content strong,
.editor-data-content strong,
.ck-content b,
.editor-data-content b {
  font-weight: 700 !important;
}

.ck-content em,
.editor-data-content em,
.ck-content i,
.editor-data-content i {
  font-style: italic !important;
}

.ck-content u,
.editor-data-content u {
  text-decoration: underline !important;
}

/* Tables */
.ck-content table,
.editor-data-content table {
  border-collapse: collapse !important;
  border: 1px solid #d1d5db !important;
  margin: 1em 0 !important;
  width: 100% !important;
}

.ck-content table td,
.editor-data-content table td,
.ck-content table th,
.editor-data-content table th {
  border: 1px solid #d1d5db !important;
  padding: 8px 12px !important;
  text-align: left !important;
}

.ck-content table th,
.editor-data-content table th {
  background-color: #f9fafb !important;
  font-weight: 600 !important;
}

/* Blockquotes */
.ck-content blockquote,
.editor-data-content blockquote {
  border-left: 4px solid #e5e7eb !important;
  padding-left: 1rem !important;
  margin: 1em 0 !important;
  font-style: italic !important;
  color: #6b7280 !important;
}

/* Images */
.ck-content img,
.editor-data-content img {
  max-width: 100% !important;
  height: auto !important;
  margin: 1em 0 !important;
}

/* Links */
.ck-content a,
.editor-data-content a {
  color: #3b82f6 !important;
  text-decoration: underline !important;
}

.ck-content a:hover,
.editor-data-content a:hover {
  color: #1d4ed8 !important;
}

/* Code */
.ck-content code,
.editor-data-content code {
  background-color: #f3f4f6 !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-family: 'Courier New', monospace !important;
  font-size: 0.875em !important;
}

.ck-content pre,
.editor-data-content pre {
  background-color: #f3f4f6 !important;
  padding: 1rem !important;
  border-radius: 6px !important;
  overflow-x: auto !important;
  margin: 1em 0 !important;
}

.ck-content pre code,
.editor-data-content pre code {
  background-color: transparent !important;
  padding: 0 !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .ck-content table,
  .editor-data-content table {
    border-color: #4b5563 !important;
  }
  
  .ck-content table td,
  .editor-data-content table td,
  .ck-content table th,
  .editor-data-content table th {
    border-color: #4b5563 !important;
  }
  
  .ck-content table th,
  .editor-data-content table th {
    background-color: #374151 !important;
    color: #f9fafb !important;
  }
  
  .ck-content blockquote,
  .editor-data-content blockquote {
    border-left-color: #6b7280 !important;
    color: #9ca3af !important;
  }
  
  .ck-content code,
  .editor-data-content code,
  .ck-content pre,
  .editor-data-content pre {
    background-color: #374151 !important;
    color: #f9fafb !important;
  }
}

/* Additional specificity for stubborn cases */
.ck-content.ck-editor__editable ul:not(.custom-list),
.editor-data-content ul:not(.custom-list) {
  list-style: disc outside !important;
  padding-left: 40px !important;
  margin: 1em 0 !important;
}

.ck-content.ck-editor__editable ol:not(.custom-list),
.editor-data-content ol:not(.custom-list) {
  list-style: decimal outside !important;
  padding-left: 40px !important;
  margin: 1em 0 !important;
}

.ck-content.ck-editor__editable li:not(.custom-list),
.editor-data-content li:not(.custom-list) {
  display: list-item !important;
  margin: 0.25em 0 !important;
}

/* Line clamp utilities are now handled by Tailwind CSS */

/* Line clamp compatibility with EditorData */
.line-clamp-4 .ck-content,
.line-clamp-4 .editor-data-content {
  overflow: hidden !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 4 !important;
  -webkit-box-orient: vertical !important;
  height: auto !important;
  max-height: none !important;
}

.line-clamp-none .ck-content,
.line-clamp-none .editor-data-content {
  overflow: visible !important;
  display: block !important;
  -webkit-line-clamp: none !important;
  -webkit-box-orient: unset !important;
}

/* Ensure list items work within line-clamp */
.line-clamp-4 .ck-content ul:not(.custom-list),
.line-clamp-4 .editor-data-content ul:not(.custom-list),
.line-clamp-4 .ck-content ol:not(.custom-list),
.line-clamp-4 .editor-data-content ol:not(.custom-list) {
  margin: 0.5em 0 !important;
}

.line-clamp-4 .ck-content li:not(.custom-list),
.line-clamp-4 .editor-data-content li:not(.custom-list) {
  margin: 0.125em 0 !important;
}

.line-clamp-4 .ck-content p,
.line-clamp-4 .editor-data-content p {
  margin: 0.5em 0 !important;
}

.line-clamp-4 .ck-content h1,
.line-clamp-4 .editor-data-content h1,
.line-clamp-4 .ck-content h2,
.line-clamp-4 .editor-data-content h2,
.line-clamp-4 .ck-content h3,
.line-clamp-4 .editor-data-content h3,
.line-clamp-4 .ck-content h4,
.line-clamp-4 .editor-data-content h4,
.line-clamp-4 .ck-content h5,
.line-clamp-4 .editor-data-content h5,
.line-clamp-4 .ck-content h6,
.line-clamp-4 .editor-data-content h6 {
  margin: 0.25em 0 !important;
}

/* Prose styling for better text readability */
.prose {
  color: #374151;
  max-width: none;
}

.prose p {
  margin-bottom: 1em;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  color: #111827;
  font-weight: 600;
}

.prose ul, .prose ol {
  margin: 1em 0;
}

.prose li {
  margin: 0.25em 0;
}

.prose strong {
  font-weight: 600;
  color: #111827;
}

.prose em {
  font-style: italic;
}

.prose code {
  color: #e11d48;
  background-color: #f1f5f9;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.prose blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  font-style: italic;
  color: #6b7280;
  margin: 1.5em 0;
}

.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
}

.prose th, .prose td {
  border: 1px solid #e5e7eb;
  padding: 0.5rem 0.75rem;
  text-align: left;
}

.prose th {
  background-color: #f9fafb;
  font-weight: 600;
}
