import React, { useEffect, useState } from "react";
import useFetch from "@/hooks/useFetch";
import { useDispatch, useSelector } from 'react-redux';
import { setOrganization } from "@/store/common";
import TemplateOneRoute from "../templates/TemplateOne/routes/Route";
import TemplateTwoRoute from "./../templates/TemplateTwo/routes/Route";
import AllRoute from "./AllRoute";
import Loading from "@/components/Loading";
import { setLabel } from "../store/languageSlice";

const commonRoute = () => {

  const dispatch = useDispatch();
  const { data: orgDetails, isLoading, isError } = useFetch({ queryKey: 'orgDetails', endPoint: 'organization-details' });
  const { data: languageData } = useFetch({ queryKey: 'label-translation', endPoint: 'get-label-translations' });

  // Update organization data when it's available
  useEffect(() => {
    if (orgDetails?.data) {
      dispatch(setOrganization(orgDetails.data));
    }
  }, [orgDetails, dispatch]);

  // Update language data when it's available
  useEffect(() => {
    if (languageData?.data) {
      dispatch(setLabel(languageData.data));
    }
  }, [languageData, dispatch]);
  const { labels = {} } = useSelector((state) => state.language || {});

  // const user_type = JSON.parse(window.localStorage.getItem("user_type"));
  const folderName = orgDetails?.data?.template?.folder_name;
  const getTemplate = () => {

    if (folderName === "TemplateOne") {
      return <TemplateOneRoute />;
    } else if (folderName === "TemplateTwo") {
      return <TemplateTwoRoute />;
    } else {
      return <TemplateTwoRoute />;
    }
  }
  return (
    isLoading ? <Loading /> : getTemplate()

  );
};

export default commonRoute;
