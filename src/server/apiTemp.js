import axios from "axios";
import { toast } from "react-toastify";
import { toastOption } from "@/constant/data";
import { MOBILE_URL } from "@/config";

const _token = JSON.parse(localStorage.getItem("_token"));
let token = "82|sWDNrKcd5n226ztyEE7bLx7SvaZH4oWyjea0QzyRngTcSWgS7I4YMn2n1FOdpjo1hbBqrXfflQ3BKTfO43zKZsge7ZzhccFmpD4ds5YMOwxlDPWOLR4WSMqtJHmKHFRHocChWeCgP2t1rPVbt67WayFIl0lrrJezd7JSfLHACK4i5nCYsRw4Yepi5K1keLgGp47yir7Upptg5n9U4AvgYlIjbdAtTmkgOtuDoI6qFfSlZQiS";
const instance = axios.create({
  baseURL: MOBILE_URL,
  headers: {
    "Content-Type": "application/json",
    Authorization: `Bearer ${token}`,
  },
});

instance.interceptors.response.use(
  (response) => {
    const method = response.config.method;
    const showToaster = response.config.showToaster !== false;
    if (method == "get" && (response.status < 200 || response.status > 204) && showToaster) {
      toast.info("Data not found", toastOption);
    }
    if (
      method === "post" ||
      method === "put" ||
      method === "patch" ||
      method === "delete"
    ) {
      const message = response.data?.message;
      if (response.status >= 200 || response.status <= 204) {
        if (showToaster) toast.success(message, toastOption);
      } else {
        if (showToaster) toast.error(message, toastOption);
      }
    }
    return response;
  },
  (err) => {
    // toast.error(err.response?.data?.message, toastOption);
    return new Promise((resolve, reject) => {
      if (err.response?.status === 401) {
        // Invalid token
        // window.location.href = "/";
        invalidTokenHandler();
        return reject(err);
      } else if (err.response?.status === 403) {
        // Not authorized
        toast.error(err.response.data.message, toastOption);
        return reject(err.response.data);
        // window.location.href = "/dashboard";
      } else if (err.response?.status === 409) {
        return reject(err.response.data);
      } else {
        return reject(err);
      }
    });
  }
);

const invalidTokenHandler = () => {
  instance.interceptors.request.use(
    (config) => {
      const _token = window.localStorage.getItem("_token");
      const storageToken = _token ? JSON.parse(_token) : null;
      if (storageToken && storageToken != undefined ) {
        config.headers["Authorization"] = `Bearer ${storageToken}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
};
const loginSuccess = (token) => {
  instance.defaults.headers["Authorization"] = `Bearer ${token}`;
};

export default {
  get: (url, params = "", showToaster = true) =>
    instance({
      method: "GET",
      url,
      params,
      showToaster,
    }),
  post: (url, data, showToaster = true) =>
    instance({
      method: "POST",
      url,
      data,
      showToaster,
    }),
    domainTest: (url, data, showToaster = false) =>
      instance({
        method: "POST",
        url,
        data,
        showToaster,
      }),
  filepost: (url, data, showToaster = true) =>
    instance({
      method: "POST",
      url,
      data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      showToaster,
    }),
  put: (url, data, showToaster = true) =>
    instance({
      method: "PUT",
      url,
      data,
      showToaster,
    }),
  fileput: (url, data, showToaster = true) => {
    data.append("_method", "PUT");
    return instance({
      method: "POST",
      url,
      data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      showToaster,
    });
  },
  patch: (url, data, showToaster = true) =>
    instance({
      method: "PATCH",
      url,
      data,
      showToaster,
    }),
  filepatch: (url, data, showToaster = true) => {
    data.append("_method", "PATCH");
    return instance({
      method: "PATCH",
      url,
      data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      showToaster,
    });
  },
  delete: (url, data, showToaster = true) =>
    instance({
      method: "DELETE",
      url,
      data,
      showToaster,
    }),
    loginSuccess
};
