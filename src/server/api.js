import axios from "axios";
import { toast } from "react-toastify";
import { toastOption } from "@/constant/data";
import { BASE_URL } from "@/config";

const _token = window.localStorage.getItem("_token");
const instance = axios.create({
  baseURL: BASE_URL,
  headers: {
    "Content-Type": "application/json",
    Authorization: `Bearer ${JSON.parse(_token)}`,
  },
});

instance.interceptors.response.use(
  (response) => {
    const method = response.config.method;
    const showToaster = response.config.showToaster !== false;
    if (method == "get" && (response.status < 200 || response.status > 204) && showToaster) {
      toast.info("Data not found", toastOption);
    }
    if (
      method === "post" ||
      method === "put" ||
      method === "patch" ||
      method === "delete"
    ) {
      const message = response.data?.message;
      // console.log(response.status)
      if (response.status >= 200 || response.status <= 204) {
        if (showToaster) toast.success(message, toastOption);
      } else {
        if (showToaster) toast.error(message, toastOption);
      }
    }
    return response;
  },
  (err) => {
    // toast.error(err.response?.data?.message, toastOption);
    return new Promise((resolve, reject) => {
      if (err.response?.status === 401) {
        localStorage.clear();
        window.location.href = "/";
        invalidTokenHandler();
        return reject(err);
      } else if (err.response?.status === 403) {
        // Not authorized
        toast.error(err.response.data.message, toastOption);
        return reject(err.response.data);
        // window.location.href = "/dashboard";
      } else if (err.response?.status === 409) {
        return reject(err.response.data);
      } else {
        return reject(err);
      }
    });
  }
);

const invalidTokenHandler = () => {
  instance.interceptors.request.use(
    (config) => {
      const _token = window.localStorage.getItem("_token");
      const storageToken = _token ? JSON.parse(_token) : null;
      if (storageToken && storageToken != undefined ) {
        config.headers["Authorization"] = `Bearer ${storageToken}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
};
const loginSuccess = (token) => {
  instance.defaults.headers["Authorization"] = `Bearer ${token}`;
};

export default {
  setTokenHeader: (token) => {
    instance.defaults.headers["Authorization"] = `Bearer ${token}`;
  },
  removeTokenHeader : () => (instance.defaults.headers["Authorization"] = null),
  get: (url, params = "", showToaster = true) =>
    instance({
      method: "GET",
      url,
      params,
      showToaster,
    }),
  post: (url, data, showToaster = true) =>
    instance({
      method: "POST",
      url,
      data,
      showToaster,
    }),
    domainTest: (url, data, showToaster = false) =>
      instance({
        method: "POST",
        url,
        data,
        showToaster,
      }),
  filepost: (url, data, showToaster = true) =>
    instance({
      method: "POST",
      url,
      data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      showToaster,
    }),
  put: (url, data, showToaster = true) =>
    instance({
      method: "PUT",
      url,
      data,
      showToaster,
    }),
  fileput: (url, data, showToaster = true) => {
    data.append("_method", "PUT");
    return instance({
      method: "POST",
      url,
      data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      showToaster,
    });
  },
  patch: (url, data, showToaster = true) =>
    instance({
      method: "PATCH",
      url,
      data,
      showToaster,
    }),
  filepatch: (url, data, showToaster = true) => {
    data.append("_method", "PATCH");
    return instance({
      method: "PATCH",
      url,
      data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      showToaster,
    });
  },
  delete: (url, data, showToaster = true) =>
    instance({
      method: "DELETE",
      url,
      data,
      showToaster,
    }),
    loginSuccess
};
