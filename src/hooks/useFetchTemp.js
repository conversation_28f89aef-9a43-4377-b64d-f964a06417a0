import { useQuery } from "@tanstack/react-query";
import apiTemp from "@/server/apiTemp";

const useFetchTemp = ({ queryKey, endPoint, params={}}) => {
    return useQuery({
        queryKey: [queryKey, params],
        queryFn: () => {
            const { id, ...otherParams } = params;

            if (!id) {
                return apiTemp.get(`${endPoint}`, otherParams ).then((res) => res.data);
            } else {
                return apiTemp.get(`${endPoint}/${id}`).then((res) => res.data);
            }
        },
    });
};

export default useFetchTemp;
