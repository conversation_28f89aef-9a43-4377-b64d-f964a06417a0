import { useQuery } from "@tanstack/react-query";
import api from "@/server/api";

const useFetch = ({ queryKey, endPoint, params={}, enabled=true }) => {
    return useQuery({
        queryKey: [queryKey, params],
        queryFn: () => {
            const { id, ...otherParams } = params;

            if (!id) {
                return api.get(`${endPoint}`, otherParams ).then((res) => res.data);
            } else {
                return api.get(`${endPoint}/${id}`).then((res) => res.data);
            }
        },
        enabled, 
    });
};

export default useFetch;
