import { createSlice } from "@reduxjs/toolkit";


const initialState = {
    showModal: false,
    showEditModal: false,
    editData: {},
    showDeleteModal: false,
    deleteData: {},
}

export const assignmentSlice = createSlice({
    name: 'assignments',
    initialState,
    reducers: {
        setShowModal: (state, action) => {
            state.showModal = action.payload;
        },
        setShowEditModal: (state, action) => {
            state.showEditModal = action.payload;
        },
        setEditData: (state, action) => {
            state.editData = action.payload;
        },
        setDeleteData: (state, action) => {
            state.deleteData =action.payload;
        },
        setDeleteModalShow: (state, action) => {
            state.showDeleteModal = action.payload;
        }
    }
})

export const {showModal, showEditModal, setShowModal, setShowEditModal, editData, setEditData, deleteData, setDeleteData, setDeleteModalShow, showDeleteModal} = assignmentSlice.actions;
export default assignmentSlice.reducer;