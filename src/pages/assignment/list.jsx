import { useNavigate, useLocation } from "react-router-dom";
import { Icon } from "@iconify/react";
import { useState, useEffect } from "react";

const AssignmentList = ({ assignments }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isFromDetails, setIsFromDetails] = useState(false);

  let pathname = location.pathname;


  // Check if the user came from "course-details"
  useEffect(() => {

  if (pathname.includes("course-details")) { 
    setIsFromDetails(true);
  }
  }, [pathname]);

  // Check if assignments are empty
  if (!assignments || assignments.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">No assignments available.</p>
      </div>
    );
  }

  return (
    <div className="container w-full grid gap-4 cursor-pointer">
      {/* Display a message if navigated from course-details */}

      {assignments.map((assignment) => (
        <div
          key={assignment.id}
          className="border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow w-full"
          onClick={() => navigate(`/assignment/${assignment.id}`)}
        >
          <div className="flex items-center justify-between">
            <h2 className="text-lg text-sky-700">{assignment.title}</h2>
            {assignment.is_submitted ? (
              <div className="flex items-center gap-2"> <p className="text-md font-semibold text-green-500">Submitted</p> </div>
            ) : new Date(assignment.deadline) < new Date() ? (
              <span className="text-sm text-red-500">Late</span>
            ) : (
              <span className="text-sm text-gray-600">{assignment.status}</span>
            )}
          </div>
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between mt-3">
            {!isFromDetails && (
            <p className="text-sm text-gray-600">{assignment.course_title}</p>
            )}
            <div className="flex gap-4 mt-2 md:mt-0 text-sm text-gray-600">
              <div>
                <span className="font-semibold">Published:</span>{" "}
                {new Date(assignment.publish_date).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                })}
              </div>
              <div>
                <span className="font-semibold">Deadline:</span>{" "}
                {new Date(assignment.deadline).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                })}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default AssignmentList;
