import React from "react";
import { useDispatch } from "react-redux";

import Badge from "@/components/ui/Badge";
import { useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import api from "@/server/api";
import EduPackLoader from "@/components/Loading";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import useFetch from "@/hooks/useFetch";

const ExamResult = () => {
  const { showDeleteModal, deleteData, showEditModal, showModal, editData } =
    useSelector((state) => state.assignmentSlice);
  const dispatch = useDispatch();
  //   const [showModal, setShowModal] = useState(false);
  //   const [apiParam, setApiParam] = useState("");
  const navigate = useNavigate();

  const {
    data: examResults,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `result-list`,
    endPoint: `student-quiz-participated-list`,
  });

  //   const res = useGetMentorListQuery(apiParam);
  //   const changePage = (val) => {
  //     setApiParam(val);
  //   };
  const handleJoinClass = async (id) => {
    const { data } = await api.post("/join-class", { class_id: id });
    if (data && data.class_url) {
      window.open(data.class_url, "_blank");
    } else {
      toast.error("Something went wrong!");
    }
  };

  const data = examResults?.data;
  const columns = [
    {
      label: "#",
      field: "serial",
    },
    {
      label: "Class Title",
      field: "title",
    },
    {
      label: "Course Name",
      field: "course",
    },
    {
      label: "Mark",
      field: "mark",
    },
    {
      label: "Total Mark",
      field: "exam_mark",
    },
    {
      label: "Action",
      field: "action",
    },
  ];


  const tableData = data?.map((item, index) => {
    return {
      serial: <p className="font-semibold">{index + 1}</p>,
      title: <p>{item.title}</p>,
      course: <p>{item?.course ? item?.course : "--"}</p>,
      mark: <p>{item?.mark}</p>,
      exam_mark: <p>{item.exam_mark}</p>,
        action: <Link className="font-semibold hover:underline" to={`/exam-details/${item?.id}`}>Details</Link>
    };
  });

  //   const actions = [
  //     {
  //       name: "edit",
  //       icon: "heroicons:pencil-square",
  //       onClick: (val) => {
  //         console.log(val);
  //         // dispatch(setEditData(data[val]));
  //         // dispatch(setShowEditModal(true));
  //       },
  //     },
  //     {
  //       name: "delete",
  //       icon: "heroicons-outline:trash",
  //       onClick: (val) => {
  //         console.log(val);
  //         // dispatch(setDeleteData(data[val]));
  //         // dispatch(setDeleteModalShow(true));
  //       },
  //     },
  //   ];

  const handleSubmit = () => {
    // setShowModal(false);
  };

  //   const createPage = <AddLiveClass />;
  //   const editPage = <EditLiveClass />;

  if (isLoading) {
    return <EduPackLoader />;
  }

  return (
    <div className="container mt-8 min-h-[70vh]">
      {/* <img className="absolute left-0 z-0" src={bgShape} alt="" />
      <img className="absolute right-0 z-0" src={bgShape2} alt="" /> */}
      {/* <Breadcrumbs /> */}
      <div className="relative text-center">
        <BasicTablePage
          title="Result List"
          createButton=""
          //   editPage={editPage}
          //   actions={actions}
          columns={columns}
          data={tableData}
          openCreateModal={() => ""}
          // changePage={changePage}
          currentPage={data?.current_page}
          submitForm={handleSubmit}
          totalPages={Math.ceil(data?.total / data?.per_page)}
          // filter={filter}
          // setFilter={setApiParam}
        />
      </div>
    </div>
  );
};

export default ExamResult;
