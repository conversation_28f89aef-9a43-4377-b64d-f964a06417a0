import React from "react";
import StudentIcon from "@/assets/StudentDashboard/studentIcon.svg";
import BadgeIcon from "@/assets/StudentDashboard/BadgeIcon.svg";
import LockIcon from "@/assets/StudentDashboard/lockIcon.svg";
import UnlockIcon1 from "@/assets/StudentDashboard/Reward.svg";
import UnlockIcon2 from "@/assets/StudentDashboard/Simplification.svg";
import EarnLockIcon from "@/assets/StudentDashboard/EarnLock.svg";
import EarnIcon from "@/assets/StudentDashboard/earnIcon.svg";
import { useSelector } from "react-redux";
import { ASSET_URL } from "@/config";
import userImg from "@/assets/images/all-img/user5.jpeg";


const studentInformation = () => {
  const {isAuth} = useSelector(state => state.auth);
  return (
    <>
      <div className="p-2 flex items-center space-x-2 lg:space-x-4">
        {/* Student Profile Image */}
        <img
          src={isAuth?.image ? ASSET_URL + isAuth.image : userImg}
          alt="Mentor"
          className="lg:w-20 lg:h-20 w-14 h-14 rounded-full border"
        />
        {/* Student Information */}
        <div>
          <span className="text-cello-900 text-xl font-semibold">
            {isAuth.name}
          </span>
          {/* Profession Name */}
          <div className="items-center text-gray-700 mt-1 w-full">
            <p className="text-base font-normal">{isAuth.user_type}</p>
          </div>
        </div>
        {/* <div className=" my-5"></div> */}
      </div>
      {/* Badge path start */}
      {/* <div className="bg-[#E8E9EB] my-5 rounded-sm shadow-xl">
        <div className="flex p-5 gap-4 my-auto items-center">
          <img
            src={BadgeIcon}
            alt="Mentor"
            className="w-14 h-14 rounded-full"
          />
          <span className="text-semibold text-2xl text-[#4D4D4D]">Badge</span>
        </div>
      </div> */}
      {/* Badge path end */}

      {/* unlocked badge start */}
      {/* <>
        <div className="flex p-3 gap-4 my-auto items-center border-b-2 border-[#E2F6F0]">
          <img src={LockIcon} alt="Mentor" className="w-8 h-8" />
          <span className="text-semibold text-2xl text-[#16AA51]">
            Unlocked Badge
          </span>
        </div>
        <div className="flex px-1 py-4 gap-5 my-auto items-center">
          <div className="border bg-[#FDF3D9] rounded-md border-[#E8E9EB]">
            <img src={UnlockIcon1} alt="Mentor" className="w-12 h-12 m-3" />
          </div>
          <div className="border bg-[#FDF3D9] rounded-md border-[#E8E9EB]">
            <img src={UnlockIcon2} alt="Mentor" className="w-12 h-12 m-3" />
          </div>
        </div>
      </>

      <>
        <div className="flex p-3 gap-4 my-auto items-center border-b-2 border-[#E2F6F0]">
          <img src={EarnLockIcon} alt="Mentor" className="w-8 h-8" />
          <span className="text-semibold text-2xl text-[#F98012]">
            Earn a Badge
          </span>
        </div>
        <div className="flex px-1 py-4 gap-3 my-auto items-center">
          <div className="border bg-[#CCCDC6] rounded-md border-[#CCCDC6]">
            <img src={EarnIcon} alt="Mentor" className="w-10 h-10 m-2" />
          </div>
          <div className="border bg-[#CCCDC6] rounded-md border-[#CCCDC6]">
            <img src={EarnIcon} alt="Mentor" className="w-10 h-10 m-2" />
          </div>
          <div className="border bg-[#CCCDC6] rounded-md border-[#CCCDC6]">
            <img src={EarnIcon} alt="Mentor" className="w-10 h-10 m-2" />
          </div>
          <div className="border bg-[#CCCDC6] rounded-md border-[#CCCDC6]">
            <img src={EarnIcon} alt="Mentor" className="w-10 h-10 m-2" />
          </div>
          <div className="border bg-[#CCCDC6] rounded-md border-[#CCCDC6]">
            <img src={EarnIcon} alt="Mentor" className="w-10 h-10 m-2" />
          </div>
        </div>
      </> */}
      {/* Earn badge end */}
    </>
  );
};

export default studentInformation;
