import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import { Icon } from "@iconify/react";
import { useNavigate, useParams } from "react-router-dom";
import api from "@/server/api";
import Modal from "@/components/ui/Modal";
import {ASSET_URL} from "@/config"
const QuizForm = ({ data, courseId }) => {
  const navigate = useNavigate();
  const { resultId } = useParams();
  const [showWrittenExamModal, setShowWrittenExamModal] = useState(false);

  console.log(data);

  const formik = useFormik({
    initialValues: {
      chapter_quiz_id: data.id,
      result_id: resultId,
      answers: data.questions.map((question) => ({
        question_id: question.id,
        // question_text: question.question_text,
        // option1: question.option1,
        // option2: question.option2,
        // option3: question.option3,
        // option4: question.option4,
        answer1: false,
        answer2: false,
        answer3: false,
        answer4: false,
      })),
    },
    onSubmit: (values) => {
      api.post(`submit-quiz`, values).then((response) => {
        console.log(response)
        if (data.written_questions) {
          setShowWrittenExamModal(true);
        } else {
          navigate(`/exam-details/${resultId}`, { replace: true });
        }
      });
    },
  });

  useEffect(() => {
    const timeout = setTimeout(() => {
      formik.handleSubmit();
    }, data.duration * 60000); // Convert minutes to milliseconds
    return () => clearTimeout(timeout);
  }, [data.duration]);


  return (
    <div>
      <form onSubmit={formik.handleSubmit} className="space-y-4">
        {data.questions.map((question, index) => (
          <div key={index} className="p-4 rounded-md">
            <b className="mb-6">
              <span className="text-sky-600">Question:</span>{" "}
              {question.question_text}
            </b>
            <div className="flex justify-between">
            
              {[1, 2, 3, 4].map((opt, idx) => (
                <div key={idx} className="flex flex-col space-y-2 max-md:w-1/2  p-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      name={`answers[${index}].answer${opt}`}
                      value={formik.values.answers[index][`answer${opt}`]}
                      checked={formik.values.answers[index][`answer${opt}`]}
                      onChange={() => {
                        const newAnswers = formik.values.answers.map(
                          (ans, idx) => {
                            if (idx === index) {
                              return {
                                ...ans,
                                [`answer${opt}`]: !ans[`answer${opt}`],
                              };
                            }
                            return ans;
                          }
                        );
                        formik.setFieldValue("answers", newAnswers);
                      }}
                      className="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
                    />
                    {question?.[`option${opt}_image`] ? (
                      <span className="flex items-center gap-2"><img src={ASSET_URL + question?.[`option${opt}_image`]} className="lg:max-w-[200px] sm:w-[150px] max-sm:w-[100px]" alt="" /> <span>{question[`option${opt}`]}</span></span>
                    ) : (
                      <span className="text-sm">{question[`option${opt}`]}</span>
                    )}
                  </label>
                </div>
              ))}
            </div>
          </div>
        ))}
        {!data.written_question ? (
          <div className="w-full text-center py-10">
            <button
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded-md"
            >
              Submit
            </button>
          </div>
        ) : (
          <button
            onClick={() => setShowWrittenExamModal(true)}
            className="px-4 py-2 bg-blue-500 text-white rounded-md"
          >
            Submit and Start Written Exam
          </button>
        )}
      </form>
      {showWrittenExamModal && (
        <Modal
          activeModal={showWrittenExamModal}
          onClose={() => setShowWrittenExamModal(false)}
        >
          <div>
            <h4 className="text-lg mb-4">
              Do you want to start written exam? The MCQ Answer will be
              submitted autometically before start!
            </h4>
            <button
              onClick={() =>
                navigate(
                  `/quiz/written-exam/${resultId}/${data.id}/${courseId}`
                )
              }
              className="px-4 py-2 bg-blue-500 text-white rounded-md mr-4"
            >
              Start Written Exam{" "}
            </button>
            <button
              onClick={() => (setShowWrittenExamModal(false), navigate('/exam-result'))}
              className="px-4 py-2 bg-red-500 text-white rounded-md"
            >
              No
            </button>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default QuizForm;
