import React from "react";
import { useParams } from "react-router-dom";
import Loading from "@/components/Loading";
import useFetch from "@/hooks/useFetch";
import QuizForm from "./QuizForm";
import Timer from "./Timer";
import GoBack from "@/components/ui/GoBack";
import examIcon from "@/assets/images/svg/exam.svg";
import { Icon } from "@iconify/react/dist/iconify.js";

const Quiz = () => {
  const { courseId, quizId } = useParams();
  const {
    data: quizdetails,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `quizdetails`,
    endPoint: `quiz-details/${quizId}`,
    params: {
      item_id: courseId,
      item_type: "Course",
    },
  });

  if (quizdetails) {
    if (!localStorage.getItem('startTime')) {
        localStorage.setItem('startTime', new Date().getTime());
        localStorage.setItem(
          'endTime',
          new Date().getTime() + quizdetails?.data?.duration * 60000
        );
    }
  }

  const quizQuestion = quizdetails?.data;
  if (isLoading) return <Loading />;
  if (isError) return <div>Error fetching data</div>;
  return (
    <div className="relative ">
      <div className="container">
        <GoBack title={quizQuestion?.title} />
        <div className="bg-[#FEF2E7] p-7 rounded container">
          <div className="md:flex items-center justify-between w-full">
            <h4 className="text-gray-600 flex items-center gap-2">
              <img src={examIcon} alt="" />
              {quizdetails.data.title}
            </h4>
            <span className="flex items-center gap-3">
              <p className="text-xl font-semibold text-black">Timer:</p>
              <Timer duration={quizQuestion.duration} />
            </span>
          </div>

          <div className="flex justify-between items-center">
            <div className="rounded-lg w-full">
              <h3 className=" text-xl text-sky-700  my-4">Exam Summary</h3>

              <div className="flex max-sm:flex-wrap gap-10 items-center text-gray-700 text-sm mb-2">
                <div className="flex items-center gap-4">
                  <Icon
                    icon="bi:question-circle-fill"
                    className="text-blue-600 text-2xl mb-1"
                  />
                  <span>
                    <p className="font-semibold text-gray-500">
                      No. of Questions
                    </p>
                    <span className="font-bold">
                      {quizQuestion?.number_of_question}
                    </span>
                  </span>
                </div>

                <div className="flex items-center gap-4">
                  <Icon
                    icon="icon-park-outline:positive-dynamics"
                    className="text-green-600 text-2xl mb-1"
                  />
                  <span>
                    <p className="font-semibold text-gray-500">
                      Positive Mark
                    </p>
                    <span className="font-bold">
                      {quizQuestion?.positive_mark}
                    </span>
                  </span>
                </div>

                <div className="flex items-center gap-4">
                  <Icon
                    icon="mdi:star-circle"
                    className="text-yellow-500 text-2xl mb-1"
                  />
                  <span>
                    <p className="font-semibold text-gray-500">
                      Total Marks
                    </p>
                    <span className="font-bold">
                      {quizQuestion?.total_mark}
                    </span>
                  </span>
                </div>

                <div className="flex items-center gap-4">
                  <Icon
                    icon="material-symbols:timer-outline"
                    className="text-orange-600 text-2xl mb-1"
                  />
                  <span>
                    <p className="font-semibold text-gray-500">
                      Total Time
                    </p>
                    <span className="font-bold">
                      {quizQuestion?.duration} Minutes
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="pt-6  border shadow-lg rounded-lg">
          {/* {quizQuestion?.questions?.map((question, idx) => ( */}
            <QuizForm data={quizdetails.data} courseId={courseId} />
          {/* ))} */}
        </div>
        {/* <div className="mt-4"> */}
          {/* <table className="min-w-full divide-y divide-gray-200 border">
                        <thead className="bg-gray-50">
                            <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Duration
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Positive Mark
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Negative Mark
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total Mark
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Number of Questions
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            <tr>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">
                                        {quizdetails.data.duration}
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">
                                        {quizdetails.data.positive_mark}
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">
                                        {quizdetails.data.negative_mark}
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">
                                        {quizdetails.data.total_mark}
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">
                                        {quizdetails.data.number_of_question}
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table> */}
        {/* </div> */}
      </div>
    </div>
  );
};

export default Quiz;
