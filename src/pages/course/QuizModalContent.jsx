import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { useNavigate, useParams } from "react-router-dom";
import useFetch from "@/hooks/useFetch";
import api from "@/server/api";
import Modal from "@/components/ui/Modal";
import Loading from "@/components/Loading";

const QuizModalContent = ({ data, onClose, activeModal }) => {
  const navigate = useNavigate();
  // console.log(data);

  const {
    data: contentDetails,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `content-details/${data.id}`,
    endPoint: `get-content-details?id=${data.id}`,
    dependencies: [data.id],
  });

  const content = contentDetails?.data;
  console.log(content)

  const handleStartQuiz = () => {
    // if (content.is_free) {
    //   navigate(`/quiz/${content.element_id}/${content.course_id}/${content.id}`);
    //   return;
    // } else {
      api
      .post("start-quiz", { course_id: content.course_id, chapter_quiz_id: content.element_id })
      .then((response) => {
        let result = response?.data?.data;
        console.log(result);
        if (response.data.status) {
          navigate(
            `/quiz/${result.chapter_quiz_id}/${result.course_id}/${result.id}`
          );
        }
      })
      .catch((error) => {
        console.error("Failed to start quiz:", error);
      });
    // }
    // navigate(`/quiz/${data.chapter_quiz_id}/${id}/${data.id}`);
  };
  // const { data: quizdetails, isLoading, isError } = useFetch({ queryKey: `quizStart`, endPoint: `quiz-start-details/${data.chapter_quiz_id}` });
  if (isLoading) return <Loading />;
  return (
    <div>
      {/* {isLoading ? (
                <div className="flex flex-col items-center justify-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-purple-500"></div>
                    <p>Loading...</p>
                </div>
            ) : (
                <>
                    <h2 className="text-lg font-semibold mb-2">{quizdetails?.data?.title}</h2>
                    <p className="mb-2">Time: 5 Minutes</p>
                    <p className="mb-2">No. Of Questions: {quizdetails?.data?.number_of_question}</p>
                    <p>Total Marks: {quizdetails?.data?.total_mark}</p>
                    <div className="flex justify-between mt-4">
                        <button className="bg-blue-500 px-4 py-2 text-white rounded-full" onClick={handleStartQuiz}>Start Quiz</button>
                        <button className="bg-red-500 px-4 py-2 text-white rounded-full" onClick={onClose}>Close</button>
                    </div>
                </>
            )} */}

      <Modal onClose={onClose} activeModal={activeModal} className="max-w-3xl">
        <div className="flex flex-col items-center w-full gap-2 mx-auto">
          <div className="flex flex-col items-center mb-4 bg-blue-50 rounded-lg shadow-lg w-full p-5">
            <Icon
              icon="fluent:clipboard-task-list-rtl-24-regular"
              className="text-sky-600 text-5xl mb-2"
            />
            <h2 className="text-2xl font-semibold text-sky-700">
              {data?.title}
            </h2>
          </div>

          <div className="border rounded-lg p-4 bg-white border-2 border-blue-200 border-dashed w-full mb-6">
            <h3 className="text-center text-lg text-sky-700  mb-4">
              Exam Summary
            </h3>

            <div className="flex max-sm:flex-wrap justify-between items-center text-gray-700 text-sm mb-2">
              <div className="flex items-start gap-2">
                <Icon
                  icon="bi:question-circle-fill"
                  className="text-blue-600 text-2xl mb-1"
                />
                <span>
                  <p className="text-md">No. of Questions</p>
                  <span className="font-bold text-lg">
                    {content?.quiz?.number_of_question}
                  </span>
                </span>
              </div>

              <div className="flex items-start gap-2">
                <Icon
                  icon="mdi:book-edit-outline"
                  className="text-green-600 text-2xl mb-1"
                />
                <span>
                  <p>Positive Mark</p>
                  <span className="font-bold text-lg">
                    {content?.quiz?.positive_mark}
                  </span>
                </span>
              </div>

              <div className="flex items-start gap-2">
                <Icon
                  icon="mdi:star-circle"
                  className="text-yellow-600 text-2xl mb-1"
                />
                <span>
                  <p>Total Marks</p>
                  <span className="font-bold text-lg">
                    {content?.quiz?.total_mark}
                  </span>
                </span>
              </div>

              <div className="flex items-start gap-2">
                <Icon
                  icon="material-symbols:timer-outline"
                  className="text-orange-600 text-2xl mb-1"
                />
                <span>
                  <p>Total Time</p>
                  <span className="font-bold text-lg">
                    {content?.quiz?.duration} Minutes
                  </span>
                </span>
              </div>
            </div>
          </div>

          <div className="w-full text-gray-800">
            <h4 className="text-sky-700 text-2xl mb-2">Instruction</h4>
            <p>{content?.quiz?.description}</p>
          </div>

          <button
            onClick={handleStartQuiz}
            className="mt-6 bg-sky-600 text-white text-lg py-2 px-6 rounded-lg flex items-center"
          >
            Start Now
            <Icon icon="lucide:arrow-right" className="ml-2 text-2xl" />
          </button>
        </div>
      </Modal>
    </div>
  );
};

export default QuizModalContent;
