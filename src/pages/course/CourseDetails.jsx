import React, { useState } from "react";
import { Link, useLocation, useParams, useNavigate } from "react-router-dom";
import useFetch from "@/hooks/useFetch";
import { Icon } from "@iconify/react";
import Accordion from "@/components/ui/Accordion";
import Badge from "@/components/ui/Badge";
import EditorData from "@/components/EditorData";
import Syllabus from "./Syllabus";
import Loading from "@/components/Loading";
import { ASSET_URL } from "@/config";
import ModalLogin from "@/pages/auth/ModalLogin";
import avatar from "@/assets/images/avatar/av-1.svg";
import { useSelector } from "react-redux";
import Rating from "@/components/ui/Rating";
import banner from "@/assets/images/all-img/video-poster.png";
import ModuleAccordion from "@/components/ui/ModuleAccordion";
import Hourglass from "@/assets/images/all-img/Hourglass.png";
import Certificate from "@/assets/images/all-img/Certificate.png";
import notes from "@/assets/images/all-img/3d notes.png";
import Attachment from "@/assets/images/all-img/Attachment.png";
import Mediaplayer from "@/assets/images/all-img/Media player.png";
import Project from "@/assets/images/all-img/Project.png";
import Note from "@/assets/images/all-img/Note.png";
import Youtube from "@/assets/images/all-img/Youtube.png";
import shape from "@/assets/images/svg/shape5.svg";
import Mentors from "./Mentors";
import curveShape from "@/assets/images/svg/curveShape.svg";
import curveShape2 from "@/assets/images/svg/curveShpae2.svg";
import Courses from "../home/<USER>";
import RoutineAccordion from "@/components/ui/RoutineAccordion";
import ReactPlayer from "react-player";
import RelatedCoures from "./RelatedCourses";
import certificateLoadingImg from "@/assets/images/all-img/certificateLoading.png";
import ApexCharts from "react-apexcharts";
import QuizModalContent from "./QuizModalContent";
import GoBack from "@/components/ui/GoBack";
import paymentProcessImg from "@/assets/images/all-img/paymentProcess.png";
import AssignmentList from "../assignment/list";
import SubjectAccordion from "@/components/ui/SubjectAccordion";
import api from "@/server/api";
import { useQueryClient } from "@tanstack/react-query";

const CourseDetails = () => {
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [subjectOpen, setSubjectOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const {
    data: coursedetails = [],
    isLoading,
    isError,
  } = useFetch({
    queryKey: `course-details/${id}`,
    endPoint: `course-details?id=${id}`,
    dependencies: [id],
  });
  const queryClient = useQueryClient();

  if (isError) return <div>Error fetching data</div>;

  const { isAuth } = useSelector((state) => state.auth);

  const course = coursedetails?.data;
  // console.log(course);

  const chartOptions = {
    chart: {
      type: "radialBar",
    },
    plotOptions: {
      radialBar: {
        hollow: {
          size: "70%",
        },
        dataLabels: {
          show: true,
          name: {
            show: false,
          },
          value: {
            show: true,
            fontSize: "25px",
          },
        },
      },
    },
  };

  const handleFreeEnroll = async () => {
    if (isAuth) {
      try {
        setLoading(true);
        console.log(id);
        const response = await api.post("purchase-course", {
          course_id: id,
          payment_method: "Free",
        });
        if (response?.data.status) {
          queryClient.invalidateQueries(`course-details?id=${id}`);
        }
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    } else {
      navigate("/login", { state: { from: location }, replace: true }); // Use navigate here
    }
  };

  const chartSeries = [75];

  return isLoading ? (
    <div className="min-h-[450px] flex flex-col items-center justify-center">
      <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-purple-500"></div>
      <p>Loading...</p>
    </div>
  ) : (
    <div className=" py-8">
      <section className="space-y-5 container ">
        {/* <GoBack title={"Course Details"} /> */}
        <div className="md:flex items-center md:gap-5">
          <div className="flex-1">
            {coursedetails?.data?.trailer_video ? (
              <div className="w-full xl:h-[450px] max-sm:h-[280px] md:h-[350px] max-sm:mb-5">
                <ReactPlayer
                  url={`https://www.youtube.com/watch?v=${coursedetails?.data?.trailer_video}`}
                  className="rounded-xl overflow-hidden"
                  width="100%"
                  height="100%"
                  controls={true}
                />
              </div>
            ) : (
              <img
                src={course?.thumbnail ? ASSET_URL + course?.thumbnail : banner}
                className="rounded-xl w-full h-auto max-sm:mb-5"
                alt=""
              />
            )}
          </div>
          <div className="flex-1 space-y-4">
            <div className="flex justify-between items-center">
              <span className="bg-[#EFF4FF] text-sky-600 p-2 px-4 text-sm rounded-full">
                Course Details
              </span>
              <Rating rating={course?.rating} />
            </div>
            <h2 className="text-3xl max-sm:text-2xl text-sky-600">
              {course?.title}
            </h2>
            <p className="text-sm">
              {isExpanded && course?.description?.length > 0
                ? course.description
                : course?.description?.slice(0, 300) || "..."}

              {course?.description?.length > 300 && (
                <span
                  className="text-sky-600 cursor-pointer"
                  onClick={() => setIsExpanded(!isExpanded)}
                >
                  {isExpanded ? " Show less" : " ...See more"}
                </span>
              )}
            </p>
            {course?.is_enrolled !== true ? (
              course?.is_free ? (
                <button
                  onClick={handleFreeEnroll}
                  className="flex items-center justify-center gap-2 bg-sky-600 text-white px-6 py-2 rounded-full border border-transparent hover:bg-white hover:text-sky-600 hover:border-sky-600 text-lg transition-color duration-300 max-w-48"
                >
                  Enroll Now
                  {loading ? (
                    <Icon icon="eos-icons:loading" width="24" height="24" />
                  ) : (
                    <Icon
                      icon="line-md:arrow-right"
                      className="text-lg transition-all duration-300 transform"
                    />
                  )}
                </button>
              ) : (
                <Link
                  to={`/course-checkout/${course?.id}`}
                  className="flex items-center justify-center gap-2 bg-sky-600 text-white px-6 py-2 rounded-full border border-transparent hover:bg-white hover:text-sky-600 hover:border-sky-600 text-lg transition-color duration-300 max-w-48"
                >
                  Enroll Now
                  <Icon
                    icon="line-md:arrow-right"
                    className="text-lg transition-all duration-300 transform"
                  />
                </Link>
              )
            ) : (
              ""
            )}
          </div>
        </div>
      </section>

      {/* certification progress */}
      <section className="my-16 container">
        <h2 className="text-xl font-semibold text-sky-600 my-5">Certificate</h2>
        <div className="xl:h-36 shadow-lg border rounded-lg p-3 flex flex-col md:flex-row gap-5 bg-[#E8F0F7] justify-between items-center">
          <div className="flex flex-col gap-2 w-full md:w-2/3">
            <span className="bg-white text-sky-600 w-32 text-center p-2 px-4 text-sm rounded-full">
              Certification
            </span>
            <h2 className="text-xl text-sky-700">{course?.title}</h2>
            <p className="text-black">{/* {course?.description} */}</p>

            {/* Progress Bar */}
            {course?.is_enrolled == true && (
              <div className="w-full mt-3">
                <div className="w-full bg-gray-300 rounded-full h-3">
                  <div
                    className="bg-sky-600 h-3 rounded-full"
                    style={{ width: `${course?.progress}%` }} // Replace `progress` with the actual value
                  ></div>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {course?.progress.toFixed(1)}% Complete
                </p>
              </div>
            )}
          </div>

          <div>
            <img src={certificateLoadingImg} alt="Certificate Loading" />
          </div>
        </div>
      </section>

      {course?.latest_activities ? (
        <section className="my-10 container bg-[#E2F6F0] rounded-lg p-5 py-2 md:flex justify-between items-center">
          <div className="flex gap-4">
            <img
              className="w-10 object-contain rounded-lg"
              src={avatar}
              alt=""
            />
            <div className="space-y-2">
              <span className="bg-white text-sky-600 w-32 text-center p-2 px-4 text-sm rounded-full">
                Last Watched
              </span>
              <h2 className="text-xl text-black">
                {course?.latest_activities?.title}
              </h2>
              <p className="text-black text-sm">
                {course?.latest_activities?.module_name}
              </p>
            </div>
          </div>

          <div className="flex gap-4 items-center">
            {course?.latest_activities?.type === "video" ||
            course?.latest_activities?.type === "script" ? (
              <Link
                to={`/content-details/${course?.latest_activities?.id}`}
                className="bg-sky-700 text-white px-5 py-2 rounded-lg hover:bg-sky-600"
              >
                Resume
              </Link>
            ) : (
              <Link
                to={`/quiz/${course?.latest_activities?.element_id}/${course?.id}/${course?.latest_activities?.id}`}
                className="bg-sky-700 text-white px-5 py-2 rounded-lg hover:bg-sky-600"
              >
                Resume
              </Link>
            )}
          </div>
        </section>
      ) : (
        ""
      )}
      {/* course outline section  */}
      <section className="space-y-5 my-14 max-sm:my-10 container ">
        <h2 className="text-xl max-sm:text-2xl text-sky-700">Course Outline</h2>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-5">
          {coursedetails?.data?.subjects?.length > 0 && (
            <div className="col-span-2 ">
              {coursedetails?.data?.subjects?.map((subject, idx) => (
                <SubjectAccordion key={idx} subject={subject} />
              ))}
            </div>
          )}

          {coursedetails?.data?.outlines?.length > 0 && (
            <div className="col-span-2 ">
              {coursedetails?.data?.outlines?.map((module, idx) => (
                <ModuleAccordion key={idx} module={module} />
              ))}
            </div>
          )}

          <div className="bg-transparent col-span-1">
            <div className="bg-white shadow-lg border rounded-lg p-5">
              {/* Price and discount section */}

              {course?.is_enrolled != true ? (
                <div className="bg-red-200 rounded-lg shadow-md px-6 py-5 text-center space-y-3">
                  <p className="text-xl text-center text-sky-700 font-semibold">
                    Course Fee:{" "}
                    {course.is_free ? (
                      "Free"
                    ) : course?.monthly_amount > 0 &&
                      course?.installment_type === "Monthly" ? (
                      <span>&#2547; {course.monthly_amount}/Month</span>
                    ) : course?.sale_price && course?.regular_price ? (
                      <p className="flex justify-center items-center font-semibold text-lg gap-3 text-red-500">
                        &#2547; {course.sale_price}
                        <span className="text-gray-500 line-through">
                          &#2547; {course.regular_price}
                        </span>
                      </p>
                    ) : null}
                  </p>

                  <h2 className="text-xl font-semibold text-[#FF0000]">
                    {coursedetails?.data?.discount_percentage}% Discount
                  </h2>
                  <div className="w-full">
                    {course?.is_enrolled != true ? (
                      course?.is_free ? (
                        <button
                          // to={`/course-checkout/${course?.id}`}
                          onClick={handleFreeEnroll}
                          className="flex items-center justify-center gap-2 bg-sky-600 text-white px-6 py-2 rounded-full border border-transparent text-lg transition-color duration-300 max-w-60 mx-auto"
                        >
                          Start Course Now{" "}
                          <Icon
                            icon="line-md:arrow-right"
                            className="text-lg transition-all duration-300 transform"
                          />
                        </button>
                      ) : (
                        <Link
                          to={`/course-checkout/${course?.id}`}
                          className="flex items-center justify-center gap-2 bg-sky-600 text-white px-6 py-2 rounded-full border border-transparent hover:bg-white hover:text-sky-600 hover:border-sky-600 text-lg transition-color duration-300 max-w-60 mx-auto"
                        >
                          Start Course Now{" "}
                          <Icon
                            icon="line-md:arrow-right"
                            className="text-lg transition-all duration-300 transform"
                          />
                        </Link>
                      )
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              ) : (
                ""
              )}

              {/* Course features section */}
              <div className="space-y-3">
                <h2 className="text-xl max-sm:text-2xl font-semibold text-sky-700 my-6">
                  You’re getting in this course
                </h2>

                {coursedetails?.data?.features?.map((feature, idx) => (
                  <div key={idx} className="space-y-3">
                    <span className="flex items-center gap-2 text-gray-500 text-md">
                      <img
                        src={ASSET_URL + feature?.icon}
                        alt="Youtube Icon"
                        className="h-7"
                      />
                      <p className="text-sm">{feature?.title}</p>
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
      {coursedetails?.data?.assignments?.length > 0 && (
        <section className="container space-y-5">
          <h2 className="text-xl text-sky-700">Assignments</h2>
          <AssignmentList assignments={coursedetails?.data?.assignments} />
        </section>
      )}
      {/* what will learn section  */}
      <section className="my-16 max-sm:my-12 space-y-5 container ">
        <h2 className="text-xl text-sky-700">
          What you will learn by doing this course
        </h2>

        {coursedetails?.data?.learning_outcomes?.map((feature, idx) => (
          <div key={idx} className="flex items-start gap-3">
            <img className="h-8" src={ASSET_URL + feature?.icon} alt="" />
            <p className="text-sm">{feature?.title}</p>
          </div>
        ))}
      </section>

      {/* routine section  */}
      <section className="container my-16">
        <RoutineAccordion
          course={coursedetails?.data}
          classSchedule={coursedetails?.data?.class_schedules}
        />
      </section>

      {/* course instructor section  */}
      <section className="h-[500px] max-sm:h-[350px] bg-blue-50 relative overflow-hidden">
        <img
          src={shape}
          className="absolute left-0 top-0 opacity-10 z-0"
          alt=""
        />
        <img
          src={shape}
          className="absolute -right-5 -bottom-10 rotate-180 opacity-10 z-0"
          alt=""
        />
        <Mentors
          mentors={coursedetails?.data?.mentors}
          sectionTitle="Course Instructor"
        />
      </section>

      {/* payment progress section  */}
      {coursedetails?.data.is_enrolled != true && (
        <section className="container my-16 space-y-10">
          <div className="relative border">
            <img
              src={curveShape}
              className="absolute left-0 top-0 z-0"
              alt=""
            />
            <img
              src={curveShape2}
              className="absolute right-0 bottom-0 z-0"
              alt=""
            />

            <div className="relative z-10 flex items-center justify-center h-full">
              <div className="flex max-sm:flex-col items-center justify-center max-sm:gap-5 max-sm:p-5 gap-10">
                <div className="space-y-4">
                  <h2 className="text-4xl text-sky-600 flex items-center gap-3">
                    <Icon
                      icon="fluent:payment-48-regular"
                      className="text-2xl text-sky-600"
                    />{" "}
                    Payment Process
                  </h2>
                  <p className="text-gray-500 text-md font-semibold">
                    To know how to make payments, please{" "}
                  </p>
                </div>

                <img
                  src={paymentProcessImg}
                  alt=""
                  style={{ maxHeight: "250px", width: "auto" }}
                  className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl"
                />
              </div>
            </div>
          </div>
        </section>
      )}
      {/* what you need  */}
      <section className="space-y-5 container my-14">
        <h2 className="text-xl text-sky-700">
          What you need to do this course
        </h2>

        {coursedetails?.data?.prerequisites?.map((item, idx) => (
          <div className="flex gap-2 items-center text-lg" key={idx}>
            <img className="h-8" src={ASSET_URL + item?.icon} alt="" />
            <p className="text-sm">{item?.title}</p>
          </div>
        ))}
      </section>

      {/* faq section  */}
      <section className="container my-14 space-y-5">
        <h2 className="text-xl text-sky-700">
          Frequently Asking Question (FAQ){" "}
        </h2>
        {coursedetails?.data?.faqs?.map((item, idx) => (
          <Accordion key={idx} title={item?.title} content={item?.answer} />
        ))}
      </section>

      {/* recommended course section  */}
      <section className="container py-10">
        <RelatedCoures
          courses={coursedetails?.data?.related_courses}
          sectionTitle={"Recommended Courses"}
        />
      </section>
    </div>
  );
};

export default CourseDetails;
