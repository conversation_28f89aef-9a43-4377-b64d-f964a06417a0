import React, { useState, useEffect } from "react";
import useFetch from "@/hooks/useFetch";
import { ASSET_URL } from "@/config";
import { useNavigate, useParams, Link } from "react-router-dom";
import { useSelector } from "react-redux";
import { Icon } from "@iconify/react/dist/iconify.js";
import GoBack from "@/components/ui/GoBack";
import Course from "../home/<USER>";
import Loading from "@/components/Loading";
import Courses from "./Courses";
import bgShape1 from "@/assets/images/svg/netVector.svg";
import bgShape3 from "@/assets/images/svg/shape3.svg";

const CategoryList = () => {
  const { organization } = useSelector((state) => state.commonSlice);
  const { menuId, subMenuId } = useParams();
  const navigate = useNavigate();
  const [page, setPage] = useState(0);

  const {
    data: categories,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `course-list-web${menuId}-${subMenuId}`,
    endPoint: `category-wise-course-list?organization_id=${organization.id}`,
  });
  const categoryList = categories?.data?.data;
  return isLoading ? (
    <div className="min-h-[450px] flex flex-col items-center justify-center">
      <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-purple-500"></div>
      <p>Loading...</p>
    </div>
  ) : (
    <div className="container items-center justify-center py-5 xl:py-8">
      <div className="flex items-center justify-between">
        <GoBack title={"All Courses"} />
        {/* <h2 className="text-xl text-sky-600 font-semibold">Courses</h2> */}
        <p className="text-sky-600 flex items-center gap-1 font-semibold cursor-pointer text-sm">
          {/* <Icon icon="mage:filter" className="text-md mr-1" /> Filter */}
        </p>
      </div>
      <div className="relative">
        {categoryList?.length > 0 &&
          categoryList?.map((data, idx) => (
            <div key={idx} className="flex flex-col relative gap-4 pt-10">
              <div className="flex justify-between items-center">
                <h2 className="text-4xl text-sky-700 max-sm:text-3xl text-start">
                  {data?.name}
                </h2>
                <Link
                  className="hover:bg-sky-50 py-2 w-24 hover:w-28 justify-center rounded-lg border border-sky-600 text-sky-600 flex items-center group gap-1 hover:gap-3 transition-all duration-300"
                  to={`/courses/${data?.id}`}
                >
                  See All
                  <Icon
                    icon="line-md:arrow-right"
                    className="text-lg transition-all duration-300 transform"
                  />
                </Link>
              </div>

              <div key={idx}>
                <Courses category={data} />
              </div>
            </div>
          ))}
      </div>
      {/* <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-10 mt-6 text-sm">
        {courses.map((course, index) => (
          <Course key={index} course={course} index={index} />
        ))}
      </div> */}
    </div>
  );
};

export default CategoryList;
