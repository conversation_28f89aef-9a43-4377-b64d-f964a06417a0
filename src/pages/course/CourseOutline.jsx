import React from "react";
import useFetch from "@/hooks/useFetch";
import { useSelector } from "react-redux";

const CourseOutline = () => {
    const {
        data: CourseOutline = [],
        isLoading,
        isError,
      } = useFetch({
        queryKey: `course-details`,
        endPoint: `course-details/${id}`,
      });
    
      if (isError) return <div>Error fetching data</div>;
    
      const { isAuth } = useSelector((state) => state.auth);
  return (
    <section className="space-y-5 my-14 max-sm:my-10 container ">
      <h2 className="text-3xl max-sm:text-2xl text-sky-600">Course Outline</h2>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-5">
        <div className="col-span-2 ">
          {courseModules.map((module, idx) => (
            <ModuleAccordion key={idx} content={module} />
          ))}
        </div>
        <div className="bg-white shadow-lg border rounded-lg col-span-1 h-full p-5">
          {/* Price and discount section */}
          <div className="bg-red-200 rounded-lg shadow-md px-6 py-5 text-center space-y-3">
            <p className="text-3xl text-center text-sky-600 font-semibold">
              Course Fee:{" "}
              <span className="text-gray-600 line-through">1500</span> 1000
            </p>
            <h2 className="text-3xl font-semibold text-[#FF0000]">
              50% Discount
            </h2>
            <div className="w-full">
              <Link
                className="py-3 px-4 mx-auto max-w-72 rounded-full block bg-sky-600 text-white text-xl"
                to={"/"}
              >
                Start Course Now <Icon icon="majesticons:arrow-right " />
              </Link>
            </div>
          </div>

          {/* Course features section */}
          <div className="">
            <h2 className="text-3xl max-sm:text-2xl font-semibold text-sky-600 my-6">
              You’re getting in this course
            </h2>

            <div className="space-y-3">
              <span className="flex items-center gap-2 text-gray-500 text-lg font-semibold">
                <img src={Youtube} alt="Youtube Icon" className="h-8" /> 8 Video
                Classes
              </span>
              <span className="flex items-center gap-2 text-gray-500 text-lg font-semibold">
                <img src={Attachment} alt="Attachment Icon" className="h-8" /> 5
                Assignments
              </span>
              <span className="flex items-center gap-2 text-gray-500 text-lg font-semibold">
                <img
                  src={Mediaplayer}
                  alt="Media Player Icon"
                  className="h-8"
                />{" "}
                300+ Pre-recorded Videos
              </span>
              <span className="flex items-center gap-2 text-gray-500 text-lg font-semibold">
                <img src={Project} alt="Project Icon" className="h-8" />{" "}
                Real-life Project
              </span>
              <span className="flex items-center gap-2 text-gray-500 text-lg font-semibold">
                <img src={notes} alt="Notes Icon" className="h-8" /> 10+ Hand
                Notes
              </span>
              <span className="flex items-center gap-2 text-gray-500 text-lg font-semibold">
                <img
                  src={Project}
                  alt="Downloadable Resource Icon"
                  className="h-8"
                />{" "}
                Downloadable Resource
              </span>
              <span className="flex items-center gap-2 text-gray-500 text-lg font-semibold">
                <img src={Certificate} alt="Certificate Icon" className="h-8" />{" "}
                Certificate
              </span>
              <span className="flex items-center gap-2 text-gray-500 text-lg font-semibold">
                <img src={Hourglass} alt="Hourglass Icon" className="h-8" />{" "}
                Lifetime Access
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CourseOutline;
