import React, { useEffect, useState } from 'react'
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { Icon } from "@iconify/react";
import api from "@/server/api";
import { handleLogin } from "@/pages/auth/common/store";
import { useDispatch } from 'react-redux';
import Modal from '@/components/ui/Modal';
import { useLocation, useNavigate, Link } from 'react-router-dom';
const ModalLogin = ({activeModal, onClose}) => {
    const [isNeedVerification, setIsNeedVerification] = useState(false);
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(false);
    const initialValues = {
        username: '',
        password: ''
    };

    const validationSchema = Yup.object().shape({
        username: Yup.string().required('Username is required'),
        password: Yup.string().required('Password is required')
    });
    
    const onSubmit = async (values, { setSubmitting }) => {
       
        try {
            setLoading(true);
            const response = await api.post(import.meta.env.VITE_BASE_URL +'/api/auth/login', values);
            
            if (response?.data.data.length === 0) {
                setIsNeedVerification(true);
            } else {
                dispatch(handleLogin(response.data));
                onClose();
                window.location.reload();
            }
            

        } catch (error) {
            console.error(error);
        } finally {
            setSubmitting(false);
            setLoading(false);
        }
    };

    return (
    
    <Modal activeModal={activeModal} onClose={onClose} title={'Login Now'} className={'max-w-xl'}>
                
            <div className="grid md:grid-cols-1 items-center gap-8 h-full justify-center ">
                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={onSubmit}
                >
                    <Form className="max-w-lg max-md:mx-auto w-full p-6 mx-auto">
                        <div className="mb-12">
                            <h3 className="text-gray-800 text-4xl font-extrabold text-center">Sign in</h3>
                            
                        </div>
                            {isNeedVerification && <div className="text-red-500 text-xs my-2">You are not still not verified, Please check your email.</div>}
                            
                        <div>
                            <label className="text-gray-800 text-[15px] mb-2 block">Username</label>
                            <Field name="username" type="text" required className="w-full text-sm text-gray-800 bg-gray-100 focus:bg-transparent px-4 py-3.5 rounded-md outline-blue-600" placeholder="Enter username" />
                            <ErrorMessage name="username" component="div" className="text-red-500 text-xs mt-1" />
                            <Icon icon="akar-icons:user" />
                        </div>

                        <div className="mt-4">
                            <label className="text-gray-800 text-[15px] mb-2 block">Password</label>
                            <Field name="password" type="password" required className="w-full text-sm text-gray-800 bg-gray-100 focus:bg-transparent px-4 py-3.5 rounded-md outline-blue-600" placeholder="Enter password" />
                            <ErrorMessage name="password" component="div" className="text-red-500 text-xs mt-1" />
                            <Icon icon="akar-icons:lock" />
                        </div>

                        <div className="flex flex-wrap items-center gap-4 justify-between mt-4">
                            <div className="flex items-center">
                                <Field id="remember-me" name="remember-me" type="checkbox" className="shrink-0 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded-md" />
                                <label htmlFor="remember-me" className="ml-3 block text-sm text-gray-800">
                                    Remember me
                                </label>
                            </div>
                            <div className="text-sm">
                                {/* <a href="javascript:void(0);" className="text-blue-600 font-semibold hover:underline">
                                    Forgot your password?
                                </a> */}
                            </div>
                        </div>

                        <div className="mt-8">
                            <button type="submit" className={`w-full shadow-xl py-3 px-6 text-sm tracking-wide font-semibold rounded-md text-white ${loading || isNeedVerification ? 'bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'} focus:outline-none`} disabled={loading || isNeedVerification}>
                                {loading ? 'Loading...' : 'Log in'}
                            </button>
                        </div>
                        <div className="mt-4 flex justify-between">
                            <p className="text-sm text-center text-gray-800">Don't have an account? 
                                <Link to="/register" className="text-blue-600 font-semibold tracking-wide hover:underline ml-1">Register here</Link>
                            </p>
                        </div>
                    </Form>
                </Formik>
            </div>
            </Modal>
    )
}

export default ModalLogin