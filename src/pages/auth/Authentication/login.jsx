import React, { useEffect, useState } from "react";
import { Icon } from "@iconify/react";
import { useNavigate, Link } from "react-router-dom";
import img4 from "@/assets/images/auth/img4.png";
import img2 from "@/assets/images/auth/img2.png";
import img3 from "@/assets/images/auth/img3.png";
import shape1 from "@/assets/images/auth/shape1.png";
import EmailOrPhone from "./EmailOrPhone";
import VerifyOTP from "./VerifyOTP";
import Register from "./register";
import MailAndPassword from "./MailAndPassword";
import ForgotPass from "../common/forgot-pass";
import ResetPass from "../common/reset-pass";

const Login = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [currentStep, setCurrentStep] = useState(1); // Start from step 1
  const [otpData, setOtpData] = useState(null);
  const [loginMail, setLoginMail] = useState(null);
  const [userId, setUserId] = useState(null);
  const images = [img2, img3, img4];

  useEffect(() => {
    const intervalId = setInterval(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
        setIsTransitioning(false);
      }, 500);
    }, 3000);

    return () => clearInterval(intervalId);
  }, [images.length]);

  let initialPaths = "1";
  const [paths, setPaths] = useState(initialPaths);

  const handleNextStep = (page, data) => {
    let nextStep;

    if (page === "mail_or_phone") {
      setLoginMail(data.phone_or_email);
      if (data.data.is_user) {
        setUserId(data.data.user_id);
        nextStep = 4;
      } else {
        setOtpData(data.data);
        nextStep = 2;
      }
    } else if (page === "verify_otp") {
      nextStep = 2;
      setOtpData(data);
    } else if (page === "register") {
      nextStep = 3;
    } else if (page === "mail_and_password") {
      nextStep = 4;
    } else if (page === "forget-password") {
      nextStep = 5;
    } else if (page === "reset-password") {
      setOtpData(data);
      nextStep = 6;
    }

    if (nextStep) {
      setCurrentStep(nextStep);
      setPaths((prevPaths) => `${prevPaths}/${nextStep}`);
    }
  };

  const handleBack = () => {
    const pathArray = paths.split("/");

    if (pathArray.length > 1) {
      pathArray.pop(); // Remove the last step
      const previousStep = pathArray[pathArray.length - 1];

      setPaths(pathArray.join("/"));
      setCurrentStep(Number(previousStep));
    } else {
      navigate(-1); // If no more steps, go back to previous page
    }
  };

  return (
    <div className="flex flex-col lg:flex-row items-center justify-center h-screen">
      {/* Left section with sliding images */}
      <div
        className={`flex-1 h-full relative bg-sky-100 max-sm:rounded-b-3xl md:rounded-r-3xl hidden lg:block`}
      >
        <div className="w-[85%] mx-auto h-full flex items-center overflow-hidden">
          <div
            className={`flex items-center transition-transform duration-500 ease-in-out`}
            style={{ transform: `translateX(-${currentImageIndex * 100}%)` }}
          >
            {images.map((img, index) => (
              <div key={index} className="w-full flex-shrink-0">
                <img
                  className="h-[450px] w-full object-contain"
                  src={img}
                  alt={`Slide ${index}`}
                />
              </div>
            ))}
          </div>
        </div>
        <img
          className="absolute left-0 bottom-0 max-sm:rounded-b-3xl md:rounded-r-xl z-0"
          src={shape1}
          alt=""
        />
      </div>

      {/* Right section with form steps */}
      <div className="flex-1 relative h-full w-full flex items-center justify-center lg:justify-start">
        <div>
          {/* Back button */}
          <button
            onClick={handleBack}
            className="flex items-center gap-2 cursor-pointer font-semibold absolute top-6 left-6 z-20 bg-white px-3 py-1.5 rounded"
          >
            <Icon icon="ion:arrow-back" className="text-2xl" /> Go Back
          </button>

          <div className="md:mx-16">
            {currentStep === 1 && (
              <EmailOrPhone handleNextStep={handleNextStep} />
            )}
            {currentStep === 2 && (
              <VerifyOTP
                handleNextStep={handleNextStep}
                otpData={otpData}
                loginMail={loginMail}
              />
            )}
            {currentStep === 3 && (
              <Register
                handleNextStep={handleNextStep}
                loginMail={loginMail}
                otpId={otpData?.otp_id}
              />
            )}
            {currentStep === 4 && (
              <MailAndPassword
                handleNextStep={handleNextStep}
                loginMail={loginMail}
                userId={userId}
              />
            )}
            {currentStep === 5 && (
              <ForgotPass
                handleNextStep={handleNextStep}
                loginMail={loginMail}
                userId={userId}
              />
            )}
            {currentStep === 6 && (
              <ResetPass
                handleNextStep={handleNextStep}
                loginMail={loginMail}
                otpData={otpData}
              />
            )}
          </div>

          {/* Privacy & Terms */}
          <div className="flex items-center gap-2 absolute right-6 bottom-6">
            <Link to="#" className="text-sky-600">
              Privacy Policy.
            </Link>
            <Link to="#" className="text-sky-600">
              Terms & Conditions
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
