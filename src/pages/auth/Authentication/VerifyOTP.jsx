import { Form, Formik } from "formik";
import React, { useEffect, useState } from "react";
import * as Yup from "yup";
import api from "@/server/api";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { useQuery } from "@tanstack/react-query";

const VerifyOTP = ({ handleNextStep, otpData, loginMail }) => {
  const [loading, setLoading] = useState(false);
  const [expireTime, setExpireTime] = useState(null); // Use state to store expireTime
  const [timeLeft, setTimeLeft] = useState(0);
  const navigate = useNavigate();

  const { data = [] } = useQuery({
    queryKey: ["organization_details"],
    queryFn: async () => {
      const { data } = await api.get(
        import.meta.env.VITE_BASE_URL + "/api/website/organization-details"
      );
      return data;
    },
  });

  const organization_id = data.data?.id;

  const formatAddress = (mailOrPhone) => {
    if (mailOrPhone.includes("@")) {
      return `${mailOrPhone[0]}${"*".repeat(
        mailOrPhone.split("@")[0].length - 1
      )}@${mailOrPhone.split("@")[1]}`;
    } else if (/^\d+$/.test(mailOrPhone)) {
      const visibleDigit = mailOrPhone.slice(-3);
      const hiddenDigit = "*".repeat(mailOrPhone.length - 3);
      return `${hiddenDigit}${visibleDigit}`;
    }
    return "";
  };

  const [isMail, setIsMail] = useState(loginMail.includes("@"));
  const [formatted, setFormatted] = useState(formatAddress(loginMail));

  const initialValues = {
    first_number: "",
    second_number: "",
    third_number: "",
    fourth_number: "",
  };

  // Validation schema to check if the entire OTP is filled
  const validationSchema = Yup.object().shape({
    first_number: Yup.string().required("Please provide full OTP").length(1),
    second_number: Yup.string().required("Please provide full OTP").length(1),
    third_number: Yup.string().required("Please provide full OTP").length(1),
    fourth_number: Yup.string().required("Please provide full OTP").length(1),
  });

  // const formattedEmail = `${loginMail[0]}${"*".repeat(
  //   loginMail.split("@")[0].length - 1
  // )}@${loginMail.split("@")[1]}`;

  const handleOTPsent = async () => {
    try {
      setLoading(true);
      const response = await api.post(
        import.meta.env.VITE_BASE_URL + "/api/login-with-mobile",
        {
          phone_or_email: loginMail,
          organization_id,
        }
      );
      const newExpireTime = response.data.data.expired_at;
      setExpireTime(newExpireTime); // Update expireTime state
      console.log("New Expire Time:", newExpireTime);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const calculateTimeLeft = (expireTime) => {
    const now = new Date();
    const expireDate = new Date(expireTime);
    const timeDiff = expireDate - now;

    // If the time is expired
    if (timeDiff <= 0) {
      return 0;
    }

    return Math.floor(timeDiff / 1000); // Return time left in seconds
  };

  useEffect(() => {
    if (!expireTime) return; // Avoid running the effect if expireTime is not set

    const initialTimeLeft = calculateTimeLeft(expireTime);
    setTimeLeft(initialTimeLeft); // Set initial time based on expireTime

    if (initialTimeLeft > 0) {
      const timerId = setInterval(() => {
        setTimeLeft((prevTime) => {
          if (prevTime <= 1) {
            clearInterval(timerId); // Stop the timer when time is up
            return 0; // OTP expired
          }
          return prevTime - 1; // Decrease time by 1 second
        });
      }, 1000); // Update every second

      // Cleanup the interval on component unmount or expireTime change
      return () => clearInterval(timerId);
    }
  }, [expireTime]); // React to changes in expireTime

  const formatTime = (totalSeconds) => {
    const minutes = Math.floor(totalSeconds / 60); // Get minutes
    const seconds = totalSeconds % 60; // Get remaining seconds
    return `${minutes} min ${seconds < 10 ? `0${seconds}` : seconds} sec`; // Format as '1 min 05 sec'
  };

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    const otp = Object.values(values).join("");

    // Check if all inputs are filled before proceeding
    if (
      !values.first_number ||
      !values.second_number ||
      !values.third_number ||
      !values.fourth_number
    ) {
      setFieldError("first_number", "Please provide the OTP"); // Set error on the first input
      return;
    }

    try {
      if (
        (values.first_number == 1,
        values.second_number == 2,
        values.third_number == 3,
        values.fourth_number == 4)
      ) {
        const response = await api.post(
          import.meta.env.VITE_BASE_URL + "/api/verify-otp",
          { otp, otp_id: otpData?.otp_id }
        );
        const otp_id = response.data.data.otp_id;
        if (response.data.data.used_for === "forgot_password") {
          handleNextStep("reset-password", otp_id);
        } else if (response.data.data.used_for === "register") {
          handleNextStep("register", otp_id);
        }
        // console.log(response?.data.status)
      }
    } catch (error) {
      toast.error(error.response.data.message);
      console.log(error);
    }

    // If all values are provided, proceed with submission
    // console.log(values); // Handle submission
    setSubmitting(false); // Reset submitting state if needed
  };

  return (
    <div className="space-y-6 max-sm:space-y-4 max-w-[355px] max-sm:py-16">
      <h2 className="text-2xl text-gray-500">
        Verify Your {isMail ? "Mail" : "Mobile"} Address
      </h2>
      <p className="text-sky-600">
        Enter the 4-digit code that was sent to the {isMail ? "mail" : "number"}{" "}
        you provided ({formatted})
      </p>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, handleChange, setFieldValue, errors, touched }) => (
          <Form>
            <div className="flex md:gap-3 items-center">
              {[
                "first_number",
                "second_number",
                "third_number",
                "fourth_number",
              ].map((name, index) => (
                <input
                  key={name}
                  name={name}
                  className={`border-2 border-gray-300 p-5 max-sm:w-14 mx-auto max-sm:p-2 text-center rounded-lg focus:outline-none focus:ring-0 focus:border-sky-500 shadow-lg w-20 text-3xl no-spinner ${
                    touched[name] && errors[name] ? "border-red-400" : ""
                  }`}
                  maxLength={1}
                  value={values[name]}
                  onChange={(e) => {
                    const value = e.target.value.slice(0, 1); // Ensure only one character is taken
                    setFieldValue(name, value); // Update value in Formik state

                    if (value) {
                      // Move to the next input if a value is entered
                      if (index < 3) {
                        document
                          .querySelector(
                            `input[name="${
                              [
                                "second_number",
                                "third_number",
                                "fourth_number",
                              ][index]
                            }"]`
                          )
                          .focus();
                      }
                    }
                  }}
                  onKeyDown={(e) => {
                    // Handle backspace to clear previous input
                    if (e.key === "Backspace" && !values[name]) {
                      if (index > 0) {
                        const prevInput = document.querySelector(
                          `input[name="${
                            ["first_number", "second_number", "third_number"][
                              index - 1
                            ]
                          }"]`
                        );
                        setFieldValue(
                          ["first_number", "second_number", "third_number"][
                            index - 1
                          ],
                          ""
                        ); // Clear previous input value
                        prevInput.focus(); // Move focus to the previous input
                      }
                    }
                  }}
                  onPaste={(e) => {
                    const pastedData = e.clipboardData.getData("Text"); // Get pasted text
                    if (/^\d{1,4}$/.test(pastedData)) {
                      // Check if pasted data is a valid 1-4 digit number
                      const valuesArray = pastedData.split("").slice(0, 4); // Take the first 4 digits
                      valuesArray.forEach((digit, idx) => {
                        setFieldValue(
                          [
                            "first_number",
                            "second_number",
                            "third_number",
                            "fourth_number",
                          ][idx],
                          digit
                        ); // Fill the inputs
                      });
                    }
                    e.preventDefault(); // Prevent default paste behavior
                  }}
                  type="number" // Change to text to handle single digit inputs
                />
              ))}
            </div>

            {/* Display a single error message for the entire form */}
            {Object.keys(touched).length > 0 && (
              <p className="text-red-600 text-center mt-2">
                {errors.first_number ||
                  errors.second_number ||
                  errors.third_number ||
                  errors.fourth_number}
              </p>
            )}

            <div className="text-sky-600 text-end mt-4">
              {timeLeft > 0 ? (
                `Send OTP in ${formatTime(timeLeft)}`
              ) : (
                <p>
                  OTP expired{" "}
                  <span
                    onClick={() => handleOTPsent()}
                    className="hover:underline cursor-pointer"
                  >
                    Sent Again
                  </span>
                </p>
              )}
            </div>

            <button
              type="submit"
              className={`w-full bg-[#1B69B3] text-xl text-white py-2.5 rounded-md shadow-lg mt-7 max-sm:mt-5 ${
                loading ? "bg-gray-400" : "hover:bg-sky-700"
              } focus:outline-none`}
              disabled={loading}
            >
              {loading ? "Loading..." : "Submit"}
            </button>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default VerifyOTP;
