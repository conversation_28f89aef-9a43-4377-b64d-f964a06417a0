import React, { useState, useEffect } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { Icon } from '@iconify/react';
import api from '@/server/api';
import { useDispatch } from 'react-redux';
import { setShortName } from "@/pages/auth/StepForNewLMS/store";

const One = ({ template, dataToSubmit, setPercent, createData }) => {
  const dispatch = useDispatch();
  const [isAvailable, setIsAvailable] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [lmsTitle, setLmsTitle] = useState(dataToSubmit?.name || '');
  const [subDomain, setSubDomain] = useState(dataToSubmit?.short_name || '');
  const [typingTimeout, setTypingTimeout] = useState(null);

  useEffect(() => {
    setLmsTitle(dataToSubmit?.name || '');
    setSubDomain(dataToSubmit?.short_name || '');
  }, [dataToSubmit]);

  const initialValues = {
    name: dataToSubmit ? dataToSubmit.name : '',
    short_name: dataToSubmit ? dataToSubmit.short_name : '',
    host_url: dataToSubmit ? dataToSubmit.host_url : '',
  };

  const validationSchema = Yup.object().shape({
    name: Yup.string().required('LMS Title is required'),
    short_name: Yup.string()
      .min(4, 'Please enter at least 4 characters')
      .required('Short Name is required'),
  });

  const checkSubDomain = async (subDomain) => {
    setSubDomain(subDomain);
    setIsChecked(false);
    try {
      if (subDomain.length > 3) {
        const response = await api.domainTest('/short-name', { short_name: subDomain });
        setIsAvailable(true);
        setIsChecked(true);
        dispatch(setShortName(subDomain));
      }
    } catch (error) {
      setIsChecked(true);
      setIsAvailable(false);
    }
  };

  const handleInputChange = (e) => {
    setSubDomain(e);
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    setTypingTimeout(
      setTimeout(() => {
        checkSubDomain(e);
      }, 1000) // Delay of 1000ms (1 second)
    );
  };

  const onSubmit = async (values, { setSubmitting }) => {
    createData(values);
    setPercent(50);
  };

  return (
    <div className="w-full h-full p-4 relative">
      {/* Background Shapes */}
      <div className="absolute top-0 left-0 w-full h-full z-0">
        {/* Curved Line 1 */}
        <div className="absolute top-1/4 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-transparent transform rotate-45 origin-left opacity-30"></div>

        {/* Curved Line 2 */}
        <div className="absolute bottom-1/4 right-0 w-full h-1 bg-gradient-to-l from-green-500 to-transparent transform rotate-45 origin-right opacity-30"></div>

        {/* Curved Line 3 */}
        <div className="absolute top-1/2 left-1/4 w-40 h-40 bg-gradient-to-b from-purple-500 to-transparent transform rotate-45 origin-bottom-left opacity-20"></div>

        {/* Curved Line 4 */}
        <div className="absolute top-3/4 left-3/4 w-48 h-48 bg-gradient-to-tl from-yellow-400 to-transparent transform rotate-45 origin-bottom-right opacity-25"></div>
      </div>

      {/* Main content */}
      <div className="relative z-10">
        <div className="flex justify-between items-start mb-4">
          <div className="flex flex-col">
            <h1 className="text-2xl font-bold text-gray-800">{lmsTitle || 'Title Of LMS'}</h1>
            <p className="text-gray-500">Set up your LMS and start managing your courses and students.</p>
            <p className="text-gray-500">You have chosen the <b>{template?.title}</b> template</p>
          </div>
        </div>
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={onSubmit}
        >
          {({ values, setFieldValue }) => (
            <Form className="flex flex-col justify-center items-center">
              <div className="w-full flex flex-col space-y-8">
                {/* LMS Title */}
                <div className="relative w-full md:w-1/2 lg:w-1/2 mx-auto">
                  <Field
                    id="name"
                    name="name"
                    type="text"
                    value={lmsTitle}
                    className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${values.name ? 'outline-none ring-2 ring-blue-500 border-blue-500' : ''}
                      px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`
                    } 
                    
                    placeholder=" "
                    onChange={(e) => {
                      setFieldValue('name', e.target.value);
                      setLmsTitle(e.target.value);
                    }}
                  />
                  <label
                    htmlFor="name"
                    className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                  >
                    LMS Title
                  </label>
                  <ErrorMessage name="name">
                    {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                  </ErrorMessage>
                  <Icon icon="akar-icons:user" className="w-4 h-4 absolute right-4 top-1/2 transform -translate-y-1/2" />
                </div>

                {/* Short Name */}
                <div className="relative w-full md:w-1/2 lg:w-1/2 mx-auto mt-10">
                  <Field
                    id="short_name"
                    name="short_name"
                    type="text"
                    value={subDomain}
                    className={`peer w-full text-sm text-gray-800 bg-transparent border-b border-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      ${values.short_name ? 'outline-none ring-2 ring-blue-500 border-blue-500' : ''}
                      px-4 py-3.5 rounded-md shadow-sm focus:text-blue-500`
                    }
                    placeholder=" "
                    onChange={(e) => {
                      setFieldValue('short_name', e.target.value);
                      handleInputChange(e.target.value);
                    }}
                  />
                  <label
                    htmlFor="short_name"
                    className="px-2 bg-white absolute text-sm text-gray-500 left-4 top-0 transform -translate-y-1/2 transition-all duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-gray-500 peer-focus:top-0 peer-focus:text-blue-500 peer-focus:text-xs"
                  >
                    Short Name <small className="text-gray-500">(This name will be used as subdomain)</small>
                  </label>
                  <ErrorMessage name="short_name">
                    {(msg) => <div className="text-red-500 text-sm">{msg}</div>}
                  </ErrorMessage>
                  <Icon icon="akar-icons:user" className="w-4 h-4 absolute right-4 top-1/2 transform -translate-y-1/2" />

                  {subDomain.length > 3 && isChecked && (
                    isAvailable ? (
                      <p className="text-green-600 text-sm mt-2">
                        Subdomain is available! Your domain will be{' '}
                        <span className="font-bold">https://{subDomain}.edupackbd.com</span>
                      </p>
                    ) : (
                      <p className="text-red-600 text-sm mt-2">Sorry! Subdomain is not available</p>
                    )
                  )}
                </div>

                {/* Submit Button */}
                <div className="mt-4 w-full md:w-1/2 lg:w-1/2 mx-auto flex justify-end">
                  <button
                    type="submit"
                    className="flex items-center px-4 py-2 text-white rounded-lg bg-blue-500 hover:bg-blue-600"
                  >
                    Next
                    <Icon icon="akar-icons:chevron-right" className="ml-2" />
                  </button>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default One;

