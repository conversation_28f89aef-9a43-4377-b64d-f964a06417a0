import React, { useState } from "react";
import useFetch from "@/hooks/useFetch";
import Modal from "@/components/ui/Modal";
import Payment from "./Payment";

const PaymentList = () => {

  const [paymentAmounts, setPaymentAmounts] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState({});

  const {
    data: response,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `my-payments`,
    endPoint: `my-payments`,
    params: {},
  });


  const payments = response?.data?.payments;

  const handlePaymentChange = (paymentId, amount) => {
    setPaymentAmounts({
      ...paymentAmounts,
      [paymentId]: amount,
    });
  };

  const handlePayNow = (payment) => {
    console.log(payment);
    setSelectedPayment(payment);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  return (
    <div className="container py-5 xl:py-10">
      <h1 className="text-2xl font-bold mb-5">Payment List</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {payments && payments.map((payment) => (
          <div
            key={payment.id}
            className="p-4 border rounded-lg shadow-sm bg-white flex flex-col sm:flex-row sm:justify-between sm:items-center"
          >
            {/* Payment Details */}
            <div className="flex-1 space-y-2">
              <h3 className="text-xl font-medium text-gray-800">
                {payment.course_title}
              </h3>
              <p className="text-sm text-gray-600">
                <span className="font-semibold">Paid Amount:</span> {payment.paid_amount}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-semibold">Payable Amount:</span> {payment.payable_amount}
              </p>
              <p
                className={`text-sm font-medium ${payment.is_verified_payment == false
                  ? "text-yellow-500"
                  : payment.payable_amount - payment.paid_amount > 0
                    ? "text-red-500"
                    : "text-green-500"
                  }`}
              >
               { payment.is_verified_payment == false ? "Pending" :
                payment.payable_amount - payment.paid_amount > 0
                  ? `Due: ${payment.payable_amount - payment.paid_amount}`
                  : "Paid"}
              </p>
            </div>

            {/* Pay Now Button */}
            {payment.payable_amount - payment.paid_amount > 0 && (
              <button
                className="text-white bg-blue-500 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 py-2 px-4 rounded"
                onClick={() => handlePayNow(payment)}
              >
                Pay Now
              </button>
            )}
          </div>
        ))}
      </div>

      {/* Modal */}
   
        {showModal && (
            <Modal 
              title={selectedPayment.course_title}
             activeModal={showModal} 
             onClose={() => setShowModal(false)}
             className="max-w-5xl"
             >
                <div className="space-y-4"> 
                    <Payment payment={selectedPayment} closeModal={() => setShowModal(false)} />
                </div>
            </Modal>
        )}

    </div>
  );
};

export default PaymentList;

