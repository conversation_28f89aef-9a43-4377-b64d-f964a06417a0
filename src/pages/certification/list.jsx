import React, { useState } from "react";
import useFetch from "@/hooks/useFetch";
import { ASSET_URL } from "@/config";
import { useNavigate } from "react-router-dom";
import GoBack from "@/components/ui/GoBack";
import Icon from "@/components/ui/Icon";
import certificateLoadingImg from "@/assets/images/all-img/certificateLoading.png";
import CertificateDetails from "./certificate-details";

const CertificateList = () => {
  const [selectedCertificate, setSelectedCertificate] = useState(null);
  const navigate = useNavigate();
  const {
    data: response,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `certificates`,
    endPoint: `certificates`,
  });
  const certificates = response?.data;

  // const handleSelectedCertificate = (data) => {
  //   setSelectedCertificate(data);
  //   navigate(`/my-certificates/${data?.id}`)
  //   return <CertificateDetails details={data} />
  // }

  if (isLoading) {
    return (
      <div className="min-h-[450px] flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-purple-500"></div>
        <p>Loading...</p>
      </div>
    );
  }

  if (isError || !certificates) {
    return (
      <div className="min-h-[450px] flex flex-col items-center justify-center">
        <p className="text-red-500">You do not have enrolled any course</p>
      </div>
    );
  }

  return (
    <div className="container py-5 xl:py-10">
      {!selectedCertificate ? (
        <div className="flex flex-col gap-10 mt-6">
          <GoBack title={"My Certificates"} />
          {certificates.map((certificate, idx) => (
            <div
              key={idx}
              className="xl:h-36 shadow-lg border rounded-lg p-3 flex flex-col md:flex-row gap-5 bg-[#E8F0F7] justify-between items-center"
            >
              <div className="flex flex-col gap-2 w-full md:w-2/3">
                <span className="bg-white text-sky-600 w-32 text-center p-2 px-4 text-sm rounded-full">
                  Certificate
                </span>
                <h2 className="text-xl text-sky-700">
                  {certificate?.course_name}
                </h2>
                <p className="text-black">{/* {course?.description} */}</p>

                {/* Progress Bar */}

                <div className="w-full mt-3">
                  <div className="w-full bg-gray-300 rounded-full h-3">
                    <div
                      className="bg-sky-600 h-3 rounded-full"
                      style={{ width: `${certificate?.progress}%` }} // Replace `progress` with the actual value
                    ></div>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {certificate.progress.toFixed(1)}% Complete
                  </p>
                </div>
              </div>

              <div>
                <img
                  className="cursor-pointer"
                  src={certificateLoadingImg}
                  alt="Certificate Loading"
                  onClick={() => setSelectedCertificate(certificate)}
                />
              </div>
            </div>
          ))}
        </div>
      ) : (
        <CertificateDetails details={selectedCertificate} />
      )}
    </div>
  );
};

export default CertificateList;
