/* Custom Swiper Navigation Styles */
/* Custom Swiper Navigation Styles */
.swiper-button-next,
.swiper-button-prev {
  width: 50px;
  height: 50px;
  background-color: #d9d9d9;
  border: 2px solid #d9d9d9;
  border-radius: 50%;
  color: #5c6777;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  position: absolute;
  /* top: 50%; */
  transform: translateY(50%);
}

.swiper-button-next {
  right: -40px;
}

.swiper-button-prev {
  left: -40px;
}

.swiper-button-next::after,
.swiper-button-prev::after {
  font-size: 16px;
  font-weight: bold;
  color: #5c6777;
}
/* --------client------- */
.swiperNext,
.swiperPrev {
  width: 50px;
  height: 50px;
  background-color: #d9d9d9;
  border: 2px solid #d9d9d9;
  border-radius: 50%;
  color: #5c6777;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  position: absolute;
  top: 50%;
  /* transform: translateY(50%); */
}

.swiperNext {
  right: -30px;
}

.swiperPrev {
  left: -30px;
}

.swiperNext::after,
.swiperPrev::after {
  font-size: 16px;
  color: #5c6777;
}
