import React, { useEffect, useRef, useState } from "react";
import PropTypes from "prop-types";
import { Icon } from "@iconify/react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper";
import "swiper/swiper-bundle.css";
import img from "@/assets/images/all-img/thumb-5.png";
import img1 from "@/assets/images/all-img/man.png";
import img2 from "@/assets/images/all-img/thumb-4.png";
import img3 from "@/assets/images/all-img/thumb-6.png";
import img4 from "@/assets/images/all-img/c1.png";
import img5 from "@/assets/images/all-img/c2.png";
import img6 from "@/assets/images/all-img/c3.png";
import img7 from "@/assets/images/all-img/cus-1.png";
import emoji from "@/assets/images/svg/emoji.svg";
import emoji2 from "@/assets/images/svg/emoji2.svg";

// SwiperCore.use([Autoplay, Navigation]);

const Testimonial = ({ clientData }) => {
  const swiperRef = useRef(null);
  const [currentIndex, setCurrentIndex] = useState(0); // State to track the current index
  const [smileEmoji, setSmileEmoji] = useState(false);

  const handleSlideChange = (swiper) => {
    setCurrentIndex(swiper.activeIndex); // Update the current index on slide change
  };

  const imagePool = [
    img,
    img1,
    img2,
    img3,
    img4,
    img5,
    img6,
    img7,
    // Add as many images as you'd like here
  ];

  const [currentImages, setCurrentImages] = useState(
    Array(8).fill(imagePool[0])
  );
  const [opacities, setOpacities] = useState(Array(8).fill(1)); // Opacity state for each image

  useEffect(() => {
    const interval = setInterval(() => {
      // Fade out images
      setOpacities(Array(8).fill(0));

      setTimeout(() => {
        // Update images randomly after fade-out
        setCurrentImages((prevImages) =>
          prevImages.map(() => {
            const randomIndex = Math.floor(Math.random() * imagePool.length);
            return imagePool[randomIndex];
          })
        );

        // Fade back in images
        setOpacities(Array(8).fill(1));

        // Toggle smileEmoji state
        setSmileEmoji((prev) => !prev);
      }, 500); // Duration of the fade-out effect
    }, 1000); // Change images every second

    return () => clearInterval(interval); // Cleanup on component unmount
  }, []);

  return (
    <div className="bg-[#FFFAE5]">
   
      <div className="container relative overflow-hidden">
        <div className="py-16 space-y-6">
          <div className="space-y-5">
            <span className="bg-sky-100 text-sky-600 p-2 px-4 text-lg rounded-full">
              Testimonials
            </span>
            <div className="md:flex items-center gap-3">
              <h5 className="text-4xl font-semibold text-sky-600 w-full">
                Trusted by more than 50k users
              </h5>
              <div className="border border-gray-400 w-full max-sm:hidden"></div>

              {/* Custom Navigation Buttons */}
              <div className="flex justify-end md:justify-between gap-2">
                <button
                  className={`bg-white border border-gray-500 text-sky-600 p-2 rounded-full ${
                    currentIndex === 0 ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  onClick={() => swiperRef.current.swiper.slidePrev()}
                  disabled={currentIndex === 0}
                >
                  <Icon icon="mingcute:left-line" className="text-2xl" />
                </button>
                <button
                  className={`bg-white border border-gray-500 text-sky-600 p-2 rounded-full ${
                    currentIndex === clientData.length - 1
                      ? "opacity-50 cursor-not-allowed"
                      : ""
                  }`}
                  onClick={() => swiperRef.current.swiper.slideNext()}
                  disabled={currentIndex === clientData.length - 1}
                >
                  <Icon icon="mingcute:right-line" className="text-2xl" />
                </button>
              </div>
            </div>
          </div>

          <div className="grid max-sm:gap-10 gap-28 xl:gap-32 grid-cols-1 md:grid-cols-3 items-center">
            <div className="md:col-span-1 relative">
              <div className="grid grid-cols-3 gap-5">
                <div className="rounded-xl w-full md:w-[55px] lg:w-[80px] xl:w-full">
                  <img
                    src={currentImages[0]}
                    alt=""
                    className="rounded-xl h-[100px] w-full object-cover"
                  />
                </div>
                <div className="rounded-r-full rounded-t-full w-full md:w-[55px] lg:w-[80px] xl:w-full">
                  <img
                    src={currentImages[1]}
                    alt=""
                    className="rounded-r-full rounded-t-full h-[100px] w-full object-cover"
                  />
                </div>
                <div className="rounded-tl-[60px] rounded-xl w-full md:w-[55px] lg:w-[80px] xl:w-full">
                  <img
                    src={currentImages[2]}
                    alt=""
                    className="rounded-tl-[60px] rounded-xl h-[100px] w-full object-cover"
                  />
                </div>
                <div className="rounded-full w-full md:w-[55px] lg:w-[80px] xl:w-full">
                  <img
                    src={currentImages[3]}
                    alt=""
                    className="rounded-full h-[100px] w-full object-cover"
                  />
                </div>
                <div className="rounded-r-full rounded-t-full rounded-xl w-full md:w-[55px] lg:w-[80px] xl:w-full">
                  <img
                    src={currentImages[4]}
                    alt=""
                    className="rounded-r-full rounded-t-full rounded-xl h-[100px] w-full object-cover"
                  />
                </div>
                <h3 className="h-20 my-auto flex items-center justify-center text-2xl bg-yellow-300 w-40 xl:w-52 rounded-full sticky text-center">
                  Happy User{" "}
                  <img
                    className="h-10 w-10"
                    src={smileEmoji ? emoji : emoji2}
                    alt=""
                  />
                </h3>
                <div className="rounded-r-full rounded-t-full w-full md:w-[55px] lg:w-[80px] xl:w-full">
                  <img
                    src={currentImages[5]}
                    alt=""
                    className="rounded-r-full rounded-t-full h-[100px] w-full object-cover"
                  />
                </div>
                <div className="rounded-full rounded-xl w-full md:w-[55px] lg:w-[80px] xl:w-full">
                  <img
                    src={currentImages[6]}
                    alt=""
                    className="rounded-full rounded-xl h-[100px] w-full object-cover"
                  />
                </div>
                <div className="rounded-bl-[60px] rounded-xl w-full md:w-[55px] lg:w-[80px] xl:w-full">
                  <img
                    src={currentImages[7]}
                    alt=""
                    className="rounded-bl-[60px] rounded-xl h-[100px] w-full object-cover"
                  />
                </div>
              </div>
            </div>

            <div className="md:col-span-2">
              <Swiper
                ref={swiperRef} // Attach the ref to the Swiper
                modules={[Navigation]}
                spaceBetween={30}
                slidesPerView={1}
                autoplay={{ delay: 5000 }}
                onSlideChange={handleSlideChange} // Trigger handleSlideChange on slide change
                navigation={false} // Disable default navigation
                breakpoints={{
                  640: {
                    slidesPerView: 1,
                  },
                  768: {
                    slidesPerView: 1,
                  },
                  1024: {
                    slidesPerView: 2,
                  },
                }}
                className=""
              >
                {clientData?.map((client, index) => (
                  <SwiperSlide key={index} className={`relative `}>
                    <div className="flex items-center gap-3">
                      <img
                        className="h-20 w-20 rounded-full"
                        src={client.image}
                        alt=""
                      />
                      <h3 className="text-xl text-sky-500">{client.title}</h3>
                    </div>

                    <div
                      className={`p-5 rounded-lg relative shadow-md mb-5 mt-10 ${
                        currentIndex === index ? "bg-blue-100" : "bg-[#FEF2E7]"
                      }`}
                    >
                      <div
                        className={`h-12 w-12 rotate-45 border-t border-l border-gray-200 absolute -top-6 z-0 ${
                          currentIndex === index
                            ? "bg-blue-100"
                            : "bg-[#FEF2E7]"
                        }`}
                      ></div>
                      <p
                        className={`text-lg relative z-10 ${
                          currentIndex === index
                            ? "bg-blue-100"
                            : "bg-[#FEF2E7]"
                        }`}
                      >
                        <Icon
                          icon="ri:double-quotes-l"
                          className="text-2xl inline mr-1 text-sky-600"
                        />
                        {client.testimonial}
                        <Icon
                          icon="ri:double-quotes-r"
                          className="text-2xl inline ml-1 text-sky-600"
                        />
                      </p>
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>

              <p className="mt-4">
                Showing {currentIndex + 1} of {clientData.length}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Testimonial;
