import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.css";
import SwiperCore, { Navigation, Pagination } from "swiper";
import { useEffect, useRef, useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import itIcon from "@/assets/images/svg/it.svg";
import { ASSET_URL } from "@/config";
import { useNavigate } from "react-router-dom";

SwiperCore.use([Navigation, Pagination]);

const Categories = ({ categories }) => {
  const swiperRef = useRef(null);
  const [isPrevDisabled, setPrevDisabled] = useState(true);
  const [isNextDisabled, setNextDisabled] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const swiper = swiperRef.current?.swiper;
    const updateButtonStates = () => {
      const currentIndex = swiper?.activeIndex;
      const totalSlides = swiper?.slides.length;
      const slidesPerView = swiper?.params.slidesPerView;
      setPrevDisabled(currentIndex === 0);
      setNextDisabled(currentIndex >= totalSlides - slidesPerView);
    };
    swiper?.on("slideChange", updateButtonStates);
    updateButtonStates();
    return () => {
      swiper?.off("slideChange", updateButtonStates);
    };
  }, []);

  return (
    <div className="container space-y-5 relative py-12">
      {/* <span className="bg-sky-100 text-sky-600 p-2 px-4 text-lg rounded-full">Categories</span> */}
      <h2 className="text-4xl pb-5 text-sky-700">Explore Course Category</h2>
      <div className="relative">
        <Swiper
          ref={swiperRef}
          slidesPerView={8}
          spaceBetween={20}
          navigation={false}
          breakpoints={{
            320: { slidesPerView: 2 },
            640: { slidesPerView: 3 },
            768: { slidesPerView: 3 },
            1024: { slidesPerView: 4 },
            1200: { slidesPerView: 6 },
          }}
        >
          {categories?.map((item) => (
            <SwiperSlide key={item?.id} className="flex flex-col items-center py-5 relative z-0">
              <div
                className="rounded-xl bg-white hover:bg-[#F5FAFF] group text-center transition-colors duration-300 cursor-pointer w-[185px] h-[125px] sm:w-[160px] sm:h-[115px] max-sm:w-[140px] max-sm:h-[110px] border flex flex-col justify-center items-center px-2 text-center"
                style={{ borderColor: "#9AADE399" }}
                onClick={() => navigate(`/courses/${item?.id}`)}
              >
                <img
                  src={item?.icon ? ASSET_URL + item.icon : itIcon}
                  alt=""
                  className="object-contain h-[30px] sm:h-[28px] max-sm:h-[26px] mb-3"
                />
                <p className="text-xs sm:text-sm font-medium text-[#2B2F32] group-hover:text-[#1570EF] leading-tight overflow-hidden text-ellipsis"
                  style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                  }}
                >
                  {item?.name}
                </p>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        <div
          className={`absolute top-14 max-sm:top-[70px] container ${categories?.length < 7 ? "lg:hidden" : ""
            } ${categories?.length < 3 ? "max-sm:hidden" : ""}`}
        >
          <button
            className={`absolute -left-8 sm:-left-10 border border-[#D0D5DD] bg-white text-sky-600 rounded-full shadow-md z-10 w-10 h-10 flex items-center justify-center transition ${isPrevDisabled ? "opacity-50 cursor-not-allowed" : ""
              }`}
            onClick={() => {
              if (!isPrevDisabled) {
                swiperRef.current?.swiper.slidePrev();
              }
            }}
            disabled={isPrevDisabled}
          >
            <Icon icon="ep:arrow-left-bold" className="text-xl" />
          </button>
          <button
            className={`absolute -right-8 sm:-right-10 border border-[#D0D5DD] bg-white text-sky-600 rounded-full shadow-md z-10 w-10 h-10 flex items-center justify-center transition ${isNextDisabled ? "opacity-50 cursor-not-allowed" : ""
              }`}
            onClick={() => {
              if (!isNextDisabled) {
                swiperRef.current?.swiper.slideNext();
              }
            }}
            disabled={isNextDisabled}
          >
            <Icon icon="ep:arrow-right-bold" className="text-xl" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Categories;
