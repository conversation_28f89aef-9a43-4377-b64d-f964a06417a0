import React, { useEffect, useState } from "react";
import one from "@/assets/sass-lms/1.png";
import two from "@/assets/sass-lms/2.png";
import three from "@/assets/sass-lms/3.png";
import four from "@/assets/sass-lms/4.png";
import { Link, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
// import heroSape1 from "@/assets/images/svg/heroBg1.svg";
import heroSape1 from "@/assets/images/all-img/shapeWithShadow1.png";
// import heroSape2 from "@/assets/images/svg/heroBg2.svg";
import heroSape2 from "@/assets/images/all-img/shapeWithShadow2.png";
// import mentorImg from "@/assets/images/all-img/mentorDemoImage.jpg";
import mentorImg from "@/assets/images/all-img/mentorDemoImage2.jpg";
import thumbImg from "@/assets/images/all-img/mentor.png";
import thumbImg2 from "@/assets/images/all-img/thumb-4.png";
import worldGif from "@/assets/images/all-img/world.gif";
import card from "@/assets/images/all-img/card-6.png";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import "swiper/swiper.min.css";
import SwiperCore, { Autoplay } from "swiper";
import heroVideo from "@/assets/heroVideo.mp4";
import ReactPlayer from "react-player";
import { ASSET_URL } from "@/config";

SwiperCore.use([Autoplay]);

const HeroSection = ({ organization }) => {
  const { isAuth } = useSelector((state) => state.auth);
  const navigate = useNavigate();
  const handleClick = () => {
    navigate("/try-new-lms");
  };
  const [count, setCount] = useState(0);

  // Fallback for invalid or undefined studentsCount
  const targetCount = Number(organization.students_count) || 0; // Ensure it's a number, default to 0
  const steps = 10; // Number of steps
  const incrementValue = Math.ceil(targetCount / steps); // Calculate increment value

  useEffect(() => {
    if (targetCount === 0) return; // Skip the counter if the target is 0 or invalid

    const interval = setInterval(() => {
      setCount((prev) => {
        if (prev + incrementValue >= targetCount) {
          clearInterval(interval); // Stop when reaching or exceeding the target
          return targetCount;
        }
        return prev + incrementValue; // Increase count by increment value
      });
    }, 100); // Adjust timing for desired animation speed

    return () => clearInterval(interval); // Cleanup interval on component unmount
  }, [incrementValue, targetCount]);

  // console.log(organization);

  return (
    <div>
      <div className="py-10  relative overflow-hidden">
        {/* Background Images */}
        <img
          src={heroSape1}
          className="max-sm:opacity-25 absolute left-0 top-0 z-0"
          alt=""
        />
        <img
          src={heroSape2}
          className="max-sm:opacity-50 absolute right-0 bottom-0 z-0"
          alt=""
        />

        {/* Content Container */}
        <div className="flex items-center">
          <div className="container mx-auto relative flex flex-col lg:flex-row items-center gap-5 z-10 py-10">
            {/* Left Column */}
            <div className="flex-1 space-y-5 lg:text-left">
              <h2 className="text-2xl md:text-4xl text-[#1B69B3]">
                {organization.headline}
              </h2>
              <p className="text-sm md:text-base">{organization.details}</p>
              <div className="flex items-center gap-5 max-sm:gap-2">
                <Link
                  to="/courses"
                  className="sm:w-52 px-3 max-sm:py-2 py-3 text-center text-sky-600 block rounded-full border border-sky-600 flex items-center justify-center gap-2 hover:gap-5 hover:bg-sky-700 hover:text-white duration-300"
                >
                  Explore Courses
                  <Icon icon="line-md:arrow-right" className="text-lg" />
                </Link>

                {/* { isAuth || organization?.id != 1 ? '' :
              <Link
                to="/try-new-lms"
                className="sm:w-52 px-3 max-sm:py-2 py-3 text-center text-white block rounded-full bg-sky-600 flex items-center justify-center gap-2 hover:gap-5 hover:bg-sky-700 hover:text-white duration-300"
              >
                Try Your Own LMS
                <Icon icon="line-md:arrow-right" className="text-lg" />
              </Link>
              } */}
              </div>
            </div>

            {/* Right Column */}
            {/* <div className="flex-1 flex justify-center items-center gap-4 max-sm:gap-2 h-60 md:h-[40vh] xl:h-[60vh] max-w-2xl mx-auto">
              <div className="flex flex-col gap-4 max-sm:gap-2 w-[45%] max-sm:w-[40vw] h-full">
                <div className="">
                  {organization.feature_mentors?.length > 0 ? <Swiper
                    spaceBetween={50}
                    slidesPerView={1}
                    loop
                    autoplay={{ delay: 2000 }}
                    className="h-[350px] max-sm:h-[230px] rounded-lg w-full"
                  >
                    {organization.feature_mentors?.map((mentor, index) => (
                      <SwiperSlide key={index} className="h-full w-full">
                        <div className="relative h-full w-full">
                          <img
                            src={
                              mentor.image ? ASSET_URL + mentor.image : thumbImg
                            }
                            alt="Slide 1"
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute top-4 left-0 bg-white bg-opacity-75 p-2 max-w-[150px] rounded-r-2xl">
                            <p className="font-bold text-indigo-500">Top Mentors</p>
                            <p className="text-gray-800 font-semibold">
                              {mentor.name}
                            </p>
                            <p className="text-gray-600 text-sm">
                              {mentor.education
                                ? mentor.education
                                : mentor.institute}
                            </p>
                          </div>
                        </div>
                      </SwiperSlide>
                    ))}
                  </Swiper> : <img className="h-[350px] max-sm:h-[230px] rounded-lg w-full object-cover object-right" src={mentorImg} />}
                </div>
                <div className="rounded-lg w-full h-[20vh] max-sm:h-[120px] border shadow-lg">
                  <img
                    src={organization?.banner ? ASSET_URL + organization.banner : card}
                    alt="Trophy"
                    className="w-full h-[20vh] max-sm:h-[120px] object-cover rounded-lg"
                  />
                </div>
              </div>
              <div className="flex flex-col justify-end gap-5 max-sm:gap-2 w-[55%] max-sm:w-[50vw] h-full mt-11">
                <div className="w-full h-[350px] max-sm:h-[230px] relative flex items-center justify-center border shadow-lg w-full  rounded-lg border-2 border-sky-50 hover:border-red-600 group cursor-pointer">
                  <div className="w-full h-full rounded-lg overflow-hidden">
                    <ReactPlayer
                      url={organization?.promotional_video || 'https://www.youtube.com/watch?v=88jH_04zmIw'} // Your video URL
                      playing={true} // Auto-play
                      muted={true} // Mute audio
                      controls={false} // Hide controls
                      width="100%"
                      height="100%"
                    />
                  </div>
                </div>
                <div className="w-full h-[150px] max-sm:h-[80px] rounded-lg relative border shadow-lg">
                  <img
                    src={worldGif}
                    alt="Rotating Globe"
                    className="w-full object-cover rounded-lg h-full z-0"
                  />
                  <div className="max-sm:text-xs text-lg font-semibold text-gray-500 rounded-r-lg h-full w-[50%] absolute top-0 right-0 flex items-center justify-center">
                    <div className="absolute inset-0 bg-gray-200 opacity-75 rounded-r-lg"></div>
                    <span className="relative z-10 text-center">
                      <span className="text-2xl sm:text-4xl text-sky-700">
                        {count.toLocaleString()}+
                      </span>
                      <br />
                      Worldwide User
                    </span>
                  </div>
                </div>
              </div>
            </div> */}
            <div className="flex-1 grid grid-cols-12 gap-5">
              <div className="col-span-6 flex flex-col">
                <div className="flex-grow-[1] "></div>
                <div className="mb-5 w-full flex-grow-[3] flex-shrink-0 basis-auto">
                  {organization.feature_mentors?.length > 0 ? (
                    <Swiper
                      spaceBetween={50}
                      slidesPerView={1}
                      loop
                      autoplay={{ delay: 2000 }}
                      className="w-full rounded-lg h-full max-h-[350px]" // Set height constraint on the Swiper container
                    >
                      {organization.feature_mentors?.map((mentor, index) => (
                        <SwiperSlide key={index} className="w-full h-full">
                          <div className="relative w-full h-full">
                            <img
                              src={
                                mentor.image
                                  ? ASSET_URL + mentor.image
                                  : thumbImg
                              }
                              alt="Slide 1"
                              className="w-full h-full object-cover" // Full height and width with object-cover
                            />
                            <div className="absolute top-4 left-0 bg-white bg-opacity-75 p-2 max-w-[150px] rounded-r-2xl">
                              <p className="font-bold text-indigo-500">
                                Top Mentors
                              </p>
                              <p className="text-gray-800 font-semibold">
                                {mentor.name}
                              </p>
                              <p className="text-gray-600 text-sm">
                                {mentor.education
                                  ? mentor.education
                                  : mentor.institute}
                              </p>
                            </div>
                          </div>
                        </SwiperSlide>
                      ))}
                    </Swiper>
                  ) : (
                    <img
                      className="rounded-lg w-full h-full object-cover"
                      src={mentorImg}
                    />
                  )}
                </div>

                {/* Second item with 1/3 height */}
                <div className=" w-full rounded-lg relative border shadow-lg">
                  <img
                    src={
                      organization?.banner
                        ? ASSET_URL + organization.banner
                        : card
                    }
                    alt="Trophy"
                    className="w-full object-cover rounded-lg"
                  />
                </div>
              </div>

              <div className="col-span-6 flex flex-col h-full">
                {/* First item with space at the top */}
                <div className="flex-grow-[2]"></div>
                <div className="flex-grow-[3] flex-shrink-0 basis-auto w-full relative flex items-center justify-center border shadow-lg rounded-lg border-2 border-sky-50 hover:border-red-600 group cursor-pointer mb-5">
                  <div className="w-full h-full rounded-lg overflow-hidden">
                    <ReactPlayer
                      url={
                        organization?.promotional_video ||
                        "https://www.youtube.com/watch?v=88jH_04zmIw"
                      }
                      playing={true}
                      muted={true}
                      controls={false}
                      loop={true}
                      width="100%"
                      height="100%"
                    />
                  </div>
                </div>

                {/* Second item */}
                <div className="flex-grow-[0.1] flex-shrink-0 basis-auto w-full rounded-lg relative border shadow-lg">
                  <img
                    src={worldGif}
                    alt="Rotating Globe"
                    className="w-full object-cover rounded-lg h-full z-0"
                  />
                  <div className="max-sm:text-xs text-lg font-semibold text-gray-500 rounded-r-lg h-full absolute top-0 right-0 flex items-center justify-center">
                    <div className="absolute inset-0 bg-gray-200 opacity-75 rounded-r-lg"></div>
                    <span className="relative z-10 text-center px-3">
                      <span className="text-2xl sm:text-4xl text-sky-700">
                        {count.toLocaleString()}+
                      </span>
                      <br />
                      Worldwide User
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
