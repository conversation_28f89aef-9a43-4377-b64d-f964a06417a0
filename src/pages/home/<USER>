import React, { useState, useRef, useEffect } from "react";
import Testimonial from "./Testimonial";
// import CallToAction from "./CallToAction";
// import Courses from "./Courses";
import HeroSection from "./HeroSection";
import Experts from "./Experts";
import Loading from "@/components/Loading";
import { useSelector } from "react-redux";
import Course from "./Course";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.min.css";
import SwiperCore, { Autoplay, Navigation } from "swiper";
import "./swiper.css";
import avatar from "@/assets/images/avatar/av-1.svg";
import avatar2 from "@/assets/images/avatar/av-3.svg";
import bgShape1 from "@/assets/images/svg/netVector.svg";
import bgShape2 from "@/assets/images/svg/shape2.svg";
import bgShape3 from "@/assets/images/svg/shape3.svg";
import bgShape4 from "@/assets/images/svg/shape4.svg";
import Categories from "@/pages/home/<USER>";
import Courses from "./Courses";
import schoolIcon from "@/assets/images/svg/school.svg";
import { Icon } from "@iconify/react";
import MobileApp from "./MobileApp";
import useFetch from "@/hooks/useFetch";
import catBg from '@/assets/images/all-img/cat.png';
import HighlightSection from "../../templates/TemplateOne/pages/home/<USER>";

SwiperCore.use([Autoplay, Navigation]);

const Home = () => {
  const { organization } = useSelector((state) => state.commonSlice);
  const { data: landingPageData = [], isLoading } = useFetch({
    queryKey: "homeDetails",
    endPoint: `landing-page-information?id=${organization.id}`,
  });
  const prevRef = useRef(null);
  const nextRef = useRef(null);
  // console.log(organization)


  //Client Say Data

  const clientData = [
    {
      id: 1,
      name: "Mynul Islam",
      title: "Chief Technical Officer",
      companyName: "BacBon Ltd.",
      testimonial:
        "This product has greatly improved our workflow and efficiency. Highly recommended! The customer service is outstanding and the product itself is top-notch.",
      image: avatar2,
    },
    {
      id: 2,
      name: "Faysal Ahmed",
      title: "Marketing Manager",
      companyName: "Apsys Ltd.",
      testimonial:
        "The customer service is outstanding and the product itself is top-notch.The customer service is outstanding and the product itself is top-notch.",
      image: avatar,
    },
    {
      id: 3,
      name: "Hosne Mobarak Rubai",
      title: "System Architect",
      companyName: "Systech Unimax",
      testimonial:
        "This product has greatly improved our workflow and efficiency. Highly recommended!The customer service is outstanding and the product itself is top-notch.",
      image: avatar2,
    },
  ];


  if(isLoading){
    return <Loading />
  }

  return (
    <div>
      <div className="mx-auto">
        {/* <HeroSection organization={organization} /> */}

        {/* <div className="my-5 mt-20">
          <div className="mb-6 mx-6">
            <h5 className="text-2xl font-bold text-primary-500 tracking-widest">
              O&nbsp;u&nbsp;r&nbsp;&nbsp;C&nbsp;o&nbsp;u&nbsp;r&nbsp;s&nbsp;e&nbsp;s
            </h5>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">

            {organization?.feature_courses.map((course, index) => (
              <Course key={index} course={course} index={index} />
            ))}
          </div>
        </div> */}
      </div>

      {/* categories section  */}
      <div className="relative mb-28 xl:mt-8">
        <img src={catBg} alt="" className="absolute z-0 hidden xl:block" />
        <div className="relative z-10">
          <Categories categories={landingPageData?.data?.categories} />
          <div className="relative">
            {landingPageData?.data &&
              landingPageData?.data?.category_with_courses?.map((data, idx) => (
                <div key={idx}>
                  {idx % 2 == 0 ? <img src={bgShape1} alt="" className="absolute z-0 -left-20" /> : <img src={bgShape3} alt="" className="absolute z-0 lg:-right-20 right-0" />}
                  <Courses
                    category={data}
                    coursesCategory={data?.sub_categories}
                  />
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* section  */}
      {/* <div className="relative mb-28">
        <img
          src={bgShape2}
          className="absolute z-0 -left-20 -bottom-[30%]"
          alt=""
        />
        <img
          src={bgShape3}
          className="absolute z-0 right-0 -top-[60%]"
          alt=""
        />
        <div className="container w-full text-center">
          <span className="bg-sky-100 text-sky-600 p-2 px-4 text-lg rounded-full">
            Courses
          </span>
          <h2 className="text-4xl text-center text-sky-600 my-5 mb-8">
            School
          </h2>

          <Courses courses={courses} coursesCategory={schoolCourses} categoryShow={true} sectionTitle={"S.S.C Model Test"} />
        </div>
      </div>

      <div className="relative container mb-28">
        <span className="bg-sky-100 text-sky-600 p-2 px-4 text-lg rounded-full">
          Courses
        </span>
        <Courses courses={courses} sectionTitle={"IT Courses"} />
      </div>

      <div className="relative mb-28">
        <img
          src={bgShape4}
          className="absolute z-0 left-0 bottom-[15%]"
          alt=""
        />
        <div className="container w-full text-center">
          <span className="bg-sky-100 text-sky-600 p-2 px-4 text-lg rounded-full">
            Courses
          </span>
          <h2 className="text-4xl text-center text-sky-600 my-5 mb-8">
            Job Preparetion
          </h2>

          <Courses courses={courses} coursesCategory={jobPreparetions} categoryShow={true} sectionTitle={"Bank Job"} />
        </div>
      </div> */}

      {/* Expert List */}
      {/* <div className="bg-primary-500 mx-auto">
        <div className="container relative mx-auto p-5">
          <div className="my-20">
            <div className="mb-6 mx-6">
              <h5 className="text-2xl font-bold text-white">
                Featured Mentors
              </h5>
            </div>

            <Swiper
              modules={[Navigation]}
              spaceBetween={30}
              slidesPerView={1}
              pagination={{ clickable: false }}
              autoplay={{ delay: 2000 }}
              scrollbar={{ draggable: false }}
              navigation={{
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
              }}
              breakpoints={{
                640: {
                  slidesPerView: 1,
                },
                768: {
                  slidesPerView: 2,
                },
                1024: {
                  slidesPerView: 3,
                },
              }}
              className="swiper-container relative"
            >
              {expertsData.map((expert, index) => (
                <SwiperSlide key={index} className="">
                  <Experts className="" expert={expert} />
                </SwiperSlide>
              ))}
            </Swiper>

            <div className="swiper-button-next"></div>
            <div className="swiper-button-prev"></div>
          </div>
        </div>
      </div> */}

      {/* client say section */}
      <div className="relative z-10">
        <Testimonial clientData={clientData} />
      </div>
      
      <HighlightSection />

      {/* mobile app section  */}
      <MobileApp organization={organization} />
    </div>
  );
};

export default Home;
