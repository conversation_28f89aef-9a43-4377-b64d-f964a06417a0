import React from "react";
import mobileImg from "@/assets/images/all-img/mobileApp.png";
import googlePlay from "@/components/partials/footer/googleplay.svg";
import appstore from "@/components/partials/footer/appstore.svg";

const MobileApp = ({ organization }) => {
//   console.log(organization);
  return (
    <div className="container">
      <div className="md:flex items-center justify-center gap-8 py-7">
        <div className="flex-1">
          <img src={mobileImg} alt="" />
        </div>
        <div className="flex-1 space-y-6">
          <h2 className="text-4xl max-sm:text-3xl font-semibold">
            Learn whenever and wherever you choose.
          </h2>
          <p className="text-gray-600">
          Learn at your own pace, wherever you are.
Flexibility to fit learning into your schedule.
Empower yourself with knowledge anytime, anywhere.
          </p>
          <p className="flex items-center gap-3">
            <span className="text-4xl text-black font-semibold">2220+</span>{" "}
            active user
          </p>

          <div className="col-span-1 flex flex-col items-start">
            <h5 className="font-medium mb-2 text-xl text-gray-700">
              Mobile App
            </h5>
            <p>From App Store or Google Play</p>
            <div className="flex space-x-2 mt-4">
              <a href="#">
                <img src={appstore} alt="App Store" className="w-32" />
              </a>
              <a href="#">
                <img src={googlePlay} alt="Google Play" className="w-32" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileApp;
