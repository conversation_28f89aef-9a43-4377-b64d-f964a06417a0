import React from "react";
import { Icon } from "@iconify/react";
import { ASSET_URL } from "@/config";
import DemoCourseImage from "@/assets/course_demo.jpg";
import { useNavigate } from "react-router-dom";
import Tooltip from "@/components/ui/Tooltip";
import Rating from "@/components/ui/Rating";
const Course = ({ course, index, purchased = false }) => {
  const navigate = useNavigate();
  // console.log(course);
  return (
    // <Tooltip content={course.name} placement="top">

    <div
      onClick={() => navigate( course.slug ? `/course/${course.slug}` : `/course-details/${course.id}`)}
      className="card border border-gray-300 rounded-lg shadow-lg relative pb-10 bg-white group cursor-pointer"
      // onClick={() => navigate(`/course-details/${course.id}`)}
    >
      <img
        src={course.thumbnail ? ASSET_URL + course.thumbnail : DemoCourseImage}
        alt={course.title}
        className="card-img-top rounded-t-lg w-full h-36 object-cover"
      />
      <div className="card-body p-4">
        <div className="flex items-center justify-between">
          <Rating rating={course.rating} />
          {course.isEnrolled ? (
            <p className="text-green-400 font-semibold text-sm">Enrolled</p>
          ) : (
            <p className="text-red-400 font-semibold text-sm">Enroll Now</p>
          )}
        </div>
        <h2 className="card-title text-sm text-sky-700 truncate text-left">
          {course.title}
        </h2>
        <div className="card-text text-gray-600 flex items-center text-right justify-between text-xs">
          {course?.mentors.length > 0 && (
            <span className="">
              {course?.mentors[0]?.name +
                ` and ${course?.mentors?.length - 1} others`}
            </span>
          )}
          {!purchased && (
            <div
              className={`text-sm px-3 py-1 font-semibold rounded ${
                course.is_free
                  ? "bg-white text-sky-600"
                  : "bg-white text-sky-600"
              }`}
            >
              {course.is_free ? (
                "Free"
              ) : course?.monthly_amount > 0 &&
                course?.installment_type === "Monthly" ? (
                <span>&#2547; {course.monthly_amount}/Month</span>
              ) : course?.sale_price && course?.regular_price ? (
                <p className="flex justify-end items-center font-semibold gap-3 text-red-500">
                  &#2547; {course.sale_price}
                  <span className="text-gray-500 line-through">
                    &#2547; {course.regular_price}
                  </span>
                </p>
              ) : null}
            </div>
          )}
          {/* {course.description.slice(0, 50)} {course.description.length > 50 && "..."}  */}
        </div>
      </div>
      <button
        // href={`/course-details/${course.id}`}
        className="w-full py-3 rounded-b-lg text-sky-600 hover:bg-sky-100 group-hover:bg-sky-100
          border-t-2 block flex items-center justify-center gap-2 font-semibold hover:gap-4 transition-color duration-300 absolute bottom-0 right-0"
      >
        See Details{" "}
        <Icon
          icon="line-md:arrow-right"
          className="text-lg transition-all duration-300 transform"
        />
      </button>
    </div>
    // </Tooltip>
  );
};

export default Course;
