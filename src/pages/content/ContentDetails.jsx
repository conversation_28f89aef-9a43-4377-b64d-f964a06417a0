import React, { useState, useEffect } from "react";
import Loading from "@/components/Loading";
import { useParams, useNavigate } from "react-router-dom";
import useFetch from "@/hooks/useFetch";
import { Icon } from "@iconify/react";
import ReactPlayer from "react-player";
import GoBack from "@/components/ui/GoBack";
import { ASSET_URL } from "@/config";
import ModuleAccordion from "@/components/ui/ModuleAccordion";
import { useSelector } from "react-redux";
import api from "@/server/api";

const ContentDetails = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isPlayingVideo, setIsPlayingVideo] = useState(false);
  const [isViewingScript, setIsViewingScript] = useState(false);
  const { id } = useParams();
  const navigate = useNavigate();
  const {
    data: contentDetails,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `content-details/${id}`,
    endPoint: `get-content-details?id=${id}`,
    dependencies: [id],
  });



  // /mark-video-completed?content_id=1

  const [isScriptLoading, setScriptIsLoading] = useState(true);

  const content = contentDetails?.data;

  const { isAuth } = useSelector((state) => state.auth);


  useEffect(() => {
    if (isAuth) {    
      if (isPlayingVideo) {
        const timer = setTimeout(async () => {
          api.get(`mark-video-completed?content_id=${id}`).then((res) => {
            console.log('video watched');
          })
        }, 5000);
    
        return () => clearTimeout(timer);
      } else if (isViewingScript) {
        const timer = setTimeout(async () => {
          api.get(`mark-script-completed?content_id=${id}`).then((res) => {
            console.log('script watched');
          })
        }, 5000);
    
        return () => clearTimeout(timer);
      }

    }

  }, [isPlayingVideo, isAuth, contentDetails, id]);


  if (isLoading) return <Loading />;
  if (isError) return <div>Error fetching data</div>;
  return (
    <div className=" pb-10">
      {content && (
        <div className="container pt-1 lg:pt-5 space-y-3">
          <GoBack title={`Back`} />
          <div className="h-[450px] max-sm:h-[220px] rounded-lg overflow-hidden">
            {content?.type == "video" && (
              // <h1>Video </h1>
              <ReactPlayer
                url={content?.video?.youtube_url || content?.video?.s3_url || content?.video?.raw_url} 
                playing={isPlayingVideo} 
                muted={true} 
                controls={true} 
                width="100%"
                height="100%"
                onPlay={() => setIsPlayingVideo(true)}
                onPause={() => setIsPlayingVideo(false)}
              />
            )}
            {content?.type == "script" && (
              <div className="h-full">
                <iframe
                  className="w-full h-full"
                  src={content?.script?.raw_url}
                  frameBorder="0"
                  allowFullScreen
                  onLoad={() => {
                    setScriptIsLoading(false);
                    setIsViewingScript(true);
                  }}
                ></iframe>
              </div>
            )}
          </div>
          <div className="space-y-3 pt-5">
            <h2 className="text-xl text-sky-700">{content?.video?.title}</h2>
            <p className="text-sm">
              {isExpanded
                ? content?.video?.description
                : content?.video?.description?.slice(0, 300)}
              {content?.video?.description?.length > 300 && (
                <span
                  className="text-sky-600 cursor-pointer"
                  onClick={() => setIsExpanded(!isExpanded)}
                >
                  {isExpanded ? " Show less" : " ...See more"}
                </span>
              )}
            </p>

            {/* {content.all_contents?.map((module, idx) => (
              <ModuleAccordion key={idx} module={module} />
            ))} */}
            <ModuleAccordion
              module={content?.outline}
              isOpen={true}
              selectedItem={id}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ContentDetails;

{
  /* <div className="container mx-auto px-4">
                <h4 className="mb-4">Content Details</h4>
                <div className="mb-8 bg-gradient-to-r from-blue-500 to-blue-900 p-4 rounded">
                    <h3 className="text-3xl font-semibold mb-4 text-white">{contentDetails.data.title}</h3>
                    <p className="text-sm text-white">{contentDetails.data.description}</p>
                </div>
                <h4 className='mb-4'>Subject list</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {contentDetails.data.subjects.map((subject) => (
                        <div key={subject.id} className="border p-4 rounded-lg shadow bg-white cursor-pointer" onClick={() => navigate(`/content-subject-details/${subject.id}`)}>
                            <Icon icon="heroicons:book-open" className="w-6 h-6 text-gray-500" />
                            <h3 className="text-lg font-semibold">{subject.subject_name}</h3>
                            <p className="text-sm text-gray-600">{subject.class_name}</p>
                        </div>
                    ))}
                </div>
            </div> */
}
