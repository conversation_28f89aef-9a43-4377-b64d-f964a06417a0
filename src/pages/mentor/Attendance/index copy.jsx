// import React, { useState, useEffect } from "react";
// import Select from "react-select";
// import api from "@/server/api";
// import useFetch from "@/hooks/useFetch";
// import GoBack from "@/components/ui/GoBack";
// import { ASSET_URL } from "../@/config";
// import DatePicker2 from "@/components/form/DatePicker2";
// import BatchList from "./BatchList";

// const AttendanceList = () => {
//   const [courseId, setCourseId] = useState(null);
//   const [batchId, setBatchId] = useState(null);
//   const [attendance, setAttendance] = useState({});
//   const [attendanceDate, setAttendanceDate] = useState(new Date());
//   const [loading, setLoading] = useState(false);

//   const { data, isLoading: batchLoading } = useFetch({
//     queryKey: "batch-list",
//     endPoint: `mentor/batch-list?pagination=false${
//       courseId ? `&course_id=${courseId.value}` : ""
//     }`,
//   });
//   const batchList = data?.data;

//   const {
//     data: studentData,
//     isLoading: studentsLoading,
//     error,
//   } = useFetch({
//     queryKey: ["student-list-for-attendance", batchId, attendanceDate],
//     endPoint: batchId
//       ? `mentor/student-list-for-attendance?date=${
//           new Date(attendanceDate).toISOString().split("T")[0]
//         }&batch_id=${batchId.value}`
//       : "",
//   });
//   const students = studentData?.data;

//   useEffect(() => {
//     if (students) {
//       const initialAttendance = students?.reduce((acc, student) => {
//         acc[student.id] = student.attendance_id !== null && student.is_present;
//         return acc;
//       }, {});
//       setAttendance(initialAttendance);
//     }
//   }, [students]);

//   const handleAttendanceChange = (studentId) => {
//     setAttendance((prevAttendance) => ({
//       ...prevAttendance,
//       [studentId]: !prevAttendance[studentId],
//     }));
//   };

//   const handleSubmit = async () => {
//     try {
//       setLoading(true);
//       const attendanceData = {
//         course_id: courseId?.value,
//         batch_id: batchId?.value,
//         attendance_date: new Date(attendanceDate).toISOString().split("T")[0],
//         students: students.map((student) => ({
//           id: student.id,
//           is_present: !!attendance[student.id],
//         })),
//       };

//       const response = await api.post("mentor/save-attendance", attendanceData);
//       if (response?.data?.status) {
//         setLoading(false);
//       }
//     } catch (error) {
//       console.log(error);
//     }
//   };

//   const batchOptions = batchList?.map((batch) => ({
//     value: batch.id,
//     label: batch.name,
//   }));

//   return (
//     <div className="px-4 max-w-7xl mx-auto">
//       <GoBack title={"Attendance List"} />
//       {batchId || (batchList?.length === 1) ? (
//         <div className="bg-white shadow-sm border rounded-lg p-6">
//           <div className=" sm:flex items-center gap-5">
//             <div className="max-sm:mb-4 sm:mt-1.5 w-full">
//               <label className="block text-sm font-medium mb-2">
//                 Select Batch
//               </label>
//               <Select
//                 options={batchOptions}
//                 isLoading={batchLoading}
//                 onChange={(selectedOption) => setBatchId(selectedOption)}
//                 value={batchOptions?.length === 1 ? batchOptions[0] : batchId}
//                 placeholder="Select a batch"
//                 isClearable
//               />
//             </div>

//             <div className="w-full">
//               <DatePicker2
//                 label="Attendance Date"
//                 placeholder="YYYY-MM-DD"
//                 format="YYYY-MM-DD"
//                 name="attendance_date"
//                 value={attendanceDate}
//                 onChange={(date) =>
//                   setAttendanceDate(
//                     new Date(date.getTime() + 1000 * 60 * 60 * 24)
//                       .toISOString()
//                       .split("T")[0]
//                   )
//                 }
//               />
//             </div>
//           </div>

//           {batchId && (
//             <div>
//               <h2 className="text-xl font-semibold my-4">Students</h2>

//               {studentsLoading ? (
//                 <p>Loading students...</p>
//               ) : students?.length > 0 ? (
//                 <div className="grid grid-cols-1 gap-2">
//                   {students.map((student) => (
//                     <div
//                       key={student.id}
//                       className="border rounded-lg p-2 bg-gray-50 shadow-sm cursor-pointer"
//                       onClick={() => handleAttendanceChange(student.id)}
//                     >
//                       <div className="flex items-center space-x-4">
//                         {student.image ? (
//                           <img
//                             src={ASSET_URL + student.image}
//                             alt={student.name}
//                             className="w-12 h-12 rounded-full border object-cover"
//                           />
//                         ) : (
//                           <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-gray-500 font-bold">
//                             {student.name[0]}
//                           </div>
//                         )}
//                         <div className="flex-1">
//                           <span className="text-md font-medium text-gray-800">
//                             {student.name}
//                           </span>
//                           <p className="text-sm text-gray-500">
//                             {student.student_id || " "}
//                           </p>
//                         </div>
//                         <div>
//                           <label className="flex items-center space-x-2">
//                             <input
//                               type="checkbox"
//                               className="form-checkbox bg-blue-500 rounded-full h-6 w-6 border-2 border-transparent focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
//                               checked={!!attendance[student.id]}
//                               readOnly
//                             />
//                             <span className="text-md font-medium text-blue-500">
//                               Present
//                             </span>
//                           </label>
//                         </div>
//                       </div>
//                     </div>
//                   ))}
//                 </div>
//               ) : (
//                 <p className="text-gray-500">No students found.</p>
//               )}

//               <div className="flex justify-end">
//                 <button
//                   onClick={handleSubmit}
//                   className="mt-4 bg-blue-500 text-white px-4 py-2 rounded-md"
//                 >
//                   {loading ? "Submitting" : "Submit"}
//                 </button>
//               </div>
//             </div>
//           )}
//         </div>
//       ) : (
//         <BatchList setBatchId={setBatchId} />
//       )}
//     </div>
//   );
// };

// export default AttendanceList;

import React, { useState, useMemo, useEffect } from "react";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "react-big-calendar/lib/css/react-big-calendar.css";
import CreateClass from "./CreateClass";
import { useDispatch, useSelector } from "react-redux";
import Attendance from "./Attendance";
import { setShowModal } from "@/store/assignmentStore";
import api from "@/server/api";
const localizer = momentLocalizer(moment);

const AttendanceList = () => {
  const dispatch = useDispatch();
  const [isAttendancePage, setIsAttendancePage] = useState(false);
  const { showModal } = useSelector((state) => state.assignmentSlice);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [eventData, setEventData] = useState(null);
  const [contextMenu, setContextMenu] = useState(null); // Manage context menu state

  // Fetch data using RTK Query
  const {
    data: res,
    isLoading,
    isError,
    error,
  } = api.get("mentor/live-class-list");

  console.log(res);
  // Prepare events for the calendar
  const events = useMemo(() => {
    return res?.map((schedule) => ({
      id: schedule.id,
      title: `${schedule.batch_name} - ${schedule.title}`,
      start: new Date(schedule.schedule_datetime), // Start time from `schedule_datetime`
      end: new Date(
        new Date(schedule.schedule_datetime).getTime() +
          schedule.duration * 60000
      ), // End time calculated using `duration`
      resource: schedule,
    }));
  }, [res]);

  // Slot selection handler (when clicking on an empty slot)
  const handleSlotSelect = (slotInfo) => {
    setSelectedDate(slotInfo.start); // Set the selected date
    dispatch(setShowModal(true)); // Open the modal
  };

  const closeContextMenu = () => {
    setContextMenu(null); // Hide the context menu
  };

  // Handle "Create Class" button click
  const handleCreateClass = () => {
    dispatch(setShowModal(true)); // Open the modal
    closeContextMenu(); // Close the context menu
  };

  // Event selection handler
  const handleEventSelect = (event) => {
    setEventData(res.find((item) => item.id === event.id));
    setIsAttendancePage(true);
  };


  return (
    <div className="max-w-7xl mx-auto">
      {isAttendancePage ? (
        <Attendance
          eventData={eventData}
          setIsAttendancePage={setIsAttendancePage}
        />
      ) : (
        <div style={{ margin: "20px" }}>
          <h2 className="text-2xl font-semibold mb-4">Class Routine</h2>
          {isLoading && <div>Loading...</div>}
          {isError && <div>Error: {error.message}</div>}
          {!isLoading && !isError && (
            <div style={{ height: "800px" }} onClick={closeContextMenu}>
              <Calendar
                localizer={localizer}
                events={events} // Events populated from API
                startAccessor="start" // Start time for events
                endAccessor="end" // End time for events
                style={{ height: 800 }}
                selectable // Enable slot selection
                onSelectSlot={handleSlotSelect} // Handler for selecting an empty slot
                onSelectEvent={handleEventSelect} // Handler for selecting an event
                views={["month", "week", "day"]} // Calendar views
                defaultView="month" // Default view when calendar loads
                defaultDate={new Date()} // Focus on today's date
                dayLayoutAlgorithm="no-overlap" // Prevent overlapping events in the day
              />
            </div>
          )}

          {/* Pass selectedDate as a prop to CreateClass */}
          {showModal && <CreateClass selectedDate={selectedDate} showModal={showModal} setShowModal={setShowModal} />}
        </div>
      )}
    </div>
  );
};

export default AttendanceList;
