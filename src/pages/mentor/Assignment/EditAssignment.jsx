import React, { useEffect, useState } from "react";
import Modal from "@/components/ui/Modal";
import { Formik, Form, ErrorMessage } from "formik";
import Button from "@/components/ui/Button";
import InputField from "@/components/ui/InputField";
import Textarea from "@/components/ui/Textarea";
import Select from "react-select";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import Fileinput from "@/components/ui/Fileinput";
import MultiSelectComponent from "@/components/ui/MultiSelectComponent";
import useFetch from "@/hooks/useFetch";
import * as Yup from "yup";
import api from "@/server/api";
import { useDispatch } from "react-redux";
import { useQueryClient } from "@tanstack/react-query";

const EditAssignment = ({ editData, showEditModal, setShowEditModal }) => {
  const [courseId, setCourseId] = useState(editData?.course_id ? editData.course_id : "");
  const [isLoading, setIsLoading] = useState(false);
  const [batchList, setBatchList] = useState([]);
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  // console.log(editData);
  const { data: courseStudentList } = useFetch({
    queryKey: `mentor/all-students-assignment?assignment_id=${editData?.id}`,
    endPoint: `mentor/all-students-assignment?assignment_id=${editData?.id}`,
  });


  const { data: courseList } = useFetch({
    queryKey: `mentor-course-list-for-filter`,
    endPoint: `mentor-course-list-for-filter`,
  });

  const { data: studentList } = useFetch({
    queryKey: `mentor-student-list=${courseId? courseId : editData.course_id}`,
    endPoint: `/mentor/student-list?course_id=${courseId? courseId : editData.course_id}`,
  });

  const { data: batches, isLoading: isBatchLoading } = useFetch(
    courseId
      ? {
          queryKey: `/mentor/batch-list?course_id=${courseId}`,
          endPoint: `/mentor/batch-list?course_id=${courseId}`,
        }
      : {
          queryKey: ``,
          endPoint: ``,
        }
  );

  useEffect(() => {
    if (batches?.data) {
      setBatchList(batches.data);
    }
  }, [batches]);

  const validationSchema = Yup.object({
    title: Yup.string()
      .required("Assignment title is required")
      .max(100, "Title cannot exceed 100 characters"),
    course_id: Yup.string().required("Please select a course"),
    description: Yup.string()
      .required("Description is required")
      .max(500, "Description cannot exceed 500 characters"),
    publish_date: Yup.date().required("Publish date is required"),
    deadline: Yup.date()
      .required("Deadline is required")
      .min(Yup.ref("publish_date"), "Deadline must be after the publish date"),
    mark: Yup.number()
      .required("Mark is required")
      .min(1, "Mark must be at least 1"),
    instructions: Yup.string()
      .required("Instructions are required")
      .max(300, "Instructions cannot exceed 300 characters"),
    student: Yup.array()
      .min(1, "Select at least one student")
      .required("Students selection is required"),
      batch_id: Yup.string().required("Batch are required")
  });

  const handleSubmit = async (values) => {
    try {
      setIsLoading(true);
      const studentIds = values.student?.map((student) => student.value).join(",");
      const formData = new FormData();
      formData.append("title", values.title);
      formData.append("course_id", values.course_id);
      formData.append("publish_date", new Date(values.publish_date).toISOString().split("T")[0]);
      formData.append("deadline", new Date(values.deadline).toISOString().split("T")[0]);
      formData.append("mark", values.mark);

      if (values.supporting_doc && values.supporting_doc.type === "application/pdf") {
        formData.append("supporting_doc", values.supporting_doc);
      }
      formData.append("description", values.description);
      formData.append("instructions", values.instructions);
      formData.append("student_ids", `[${studentIds}]`);
      formData.append("batch_id", values.batch_id);

      const response = await api.fileput(
        `mentor/update-assignment/${editData.id}`,
        formData
      );
      console.log("Edit response:", response);
      if(response?.data?.status){
        queryClient.invalidateQueries("mentor/assignment-list");
        dispatch(setShowEditModal(false));
      }
    } catch (error) {
      console.error("Edit error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setShowEditModal(false))}
      title="Edit Assignment"
      className="max-w-4xl"
      footer={
        <Button
          text="Close"
          btnClass="btn-primary"
          onClick={() => dispatch(setShowEditModal(false))}
        />
      }
    >
      <Formik
        initialValues={editData}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ values, setFieldValue }) => (
          <Form>
            <div className="space-y-3">
              <div className="lg:flex justify-between gap-4 w-full">
                <div className="w-full">
                  <InputField
                    label="Assignment Title"
                    name="title"
                    type="text"
                    placeholder="Assignment Name"
                    required
                  />
                </div>
                <div className="w-full">
                  <label className="block text-gray-600 text-sm font-medium mb-2">
                    Select Course
                    <span className="text-red-500">*</span>
                  </label>
                  <Select
                    placeholder="Select Course"
                    options={courseList?.data?.map((course) => ({
                      value: course.id,
                      label: course.title,
                    }))}
                    name="course_id"
                    value={
                      courseList?.data
                        ?.map((course) => ({ value: course.id, label: course.title }))
                        .find((option) => option.value === values.course_id) || null
                    }
                    onChange={(selectedOption) => {
                      console.log(selectedOption);
                      setCourseId(selectedOption.value);
                      setFieldValue("course_id", selectedOption.value);
                    }}
                  />
                    {/* value={courseList?.data?.find((c) => c.id === values.course_id)} */}
                  <ErrorMessage name="course_id">
                    {(msg) => <div className="text-red-500 text-sm mt-1">{msg}</div>}
                  </ErrorMessage>
                </div>

                <div className="w-full">
                  <label className="block text-gray-600 text-sm font-medium mb-2">
                    Select Batch
                    <span className="text-red-500">*</span>
                  </label>
                  <Select
                    placeholder="Select Batch"
                    options={batchList?.map((batch) => ({
                      value: batch.id,
                      label: batch.name,
                    }))}
                    name="batch_id"
                    value={
                      batchList.length > 0 && batchList?.map((batch) => ({ value: batch.id, label: batch.name }))
                        .find((option) => option.value === values.batch_id) || null
                    }
                    onChange={(selectedOption) => {
                      console.log(selectedOption);
                      setCourseId(selectedOption.value);
                      setFieldValue("batch_id", selectedOption.value);
                    }}
                  />
                    {/* value={courseList?.data?.find((c) => c.id === values.course_id)} */}
                  <ErrorMessage name="batch_id">
                    {(msg) => <div className="text-red-500 text-sm mt-1">{msg}</div>}
                  </ErrorMessage>
                </div>
              </div>

              <div className="gap-4">
                <label htmlFor="description" className="block text-gray-600 text-sm font-medium mb-2">
                  Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="description"
                  name="description"
                  placeholder="Assignment Description"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  defaultValue={editData.description}
                  onChange={(e) => setFieldValue("description", e.target.value)}
                />
                <ErrorMessage name="description">
                  {(msg) => <div className="text-red-500 text-sm mt-1">{msg}</div>}
                </ErrorMessage>
              </div>
              <div className="lg:flex justify-between gap-4">
                <div className="w-full">
                  <DatePicker
                    label="Publish Date"
                    placeholder="YYYY-MM-DD"
                    format="YYYY/MM/DD"
                    defaultValue={editData.publish_date}
                    name="publish_date"
                    required
                    onChange={(e) => {
                      setFieldValue("publish_date", e);
                    }}
                  />
                  <ErrorMessage name="publish_date">
                    {(msg) => <div className="text-red-500 text-sm mt-1">{msg}</div>}
                  </ErrorMessage>
                </div>
                <div className="w-full">
                  <DatePicker
                    label="Deadline"
                    placeholder="YYYY-MM-DD"
                    format="YYYY/MM/DD"
                    name="deadline"
                    defaultValue={editData.deadline}
                    required
                    onChange={(e) => {
                      setFieldValue("deadline", e);
                    }}
                  />
                  <ErrorMessage name="deadline">
                    {(msg) => <div className="text-red-500 text-sm mt-1">{msg}</div>}
                  </ErrorMessage>
                </div>
                <div className="w-full">
                  <InputField
                    label="Total Mark"
                    name="mark"
                    type="number"
                    min="0"
                    placeholder="Enter Assignment Mark"
                    required
                  />
                </div>
              </div>
              <div className=""> 
                <Fileinput
                    name="supporting_doc"
                    accept=".pdf"
                    type="file"
                    placeholder="Select Document"
                    title="Question (Document)"
                    selectedFile={values.supporting_doc}
                    onChange={(e) => {
                      setFieldValue("supporting_doc", e.target.files[0]);
                    }}
                  />

          

                <div className="mt-3">
                  <label className="block text-gray-600 text-sm font-medium mb-2">
                    Select Students
                    <span className="text-red-500"> *</span>
                  </label>
                  <MultiSelectComponent
                    name="student"
                    placeholder="Select Students"
                    options={courseStudentList?.data?.map((student) => ({
                      label: student.name,
                      value: student.id,
                    }))}
                    defaultValue={courseStudentList?.data
                      ?.filter((student) => student.is_selected) // Filter only the selected students
                      ?.map((student) => ({
                        label: student.name,
                        value: student.id,
                      }))}
                    valueKey="value"
                    labelKey="label"
                    onChange={(selected) => setFieldValue("student", selected)}
                  />
                  {/* 
                  <MultiSelectComponent
                    name="student"
                    placeholder="Select Students"
                    options={courseStudentList?.data?.map((student) => ({
                      label: student.name,
                      value: student.id,
                    }))}
                    value={courseStudentList?.data
                      ?.filter((student) => student.is_selected) // Filter only the selected students
                      ?.map((student) => ({
                        label: student.name,
                        value: student.id,
                      }))}
                    onChange={(selected) => setFieldValue("student", selected)}
                  /> */}

                  <ErrorMessage name="student">
                    {(msg) => <div className="text-red-500 text-sm mt-1">{msg}</div>}
                  </ErrorMessage>
                </div>
                <div className="gap-4 mt-3">

                <label htmlFor="instructions" className="block text-gray-600 text-sm font-medium mb-2">
                  Instructions 
                </label>
                <textarea
                  id="instructions"
                  name="instructions"
                  placeholder="Instructions"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  defaultValue={editData.instructions}
                  onChange={(e) => setFieldValue("instructions", e.target.value)}
                />
                <ErrorMessage name="instructions">
                  {(msg) => <div className="text-red-500 text-sm mt-1">{msg}</div>}
                </ErrorMessage>
              </div>
              </div>
            </div>
            <div className="flex justify-end mt-4">
              <Button text="Submit" type="submit" btnClass="btn-primary" isLoading={isLoading} />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditAssignment;

