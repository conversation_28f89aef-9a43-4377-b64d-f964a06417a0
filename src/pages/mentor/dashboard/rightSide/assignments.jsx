import { Icon } from "@iconify/react/dist/iconify.js";
import React from "react";
import { Link } from "react-router-dom";

const Assignments = ({ assignments }) => {
  // console.log(assignments);
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          <Icon icon="mingcute:task-line" className="w-6 h-6 text-orange-500" />
          <h3 className="text-lg font-semibold text-downriver-950">
            Assignments
          </h3>
        </div>
        <Link
          to="/assignment-list"
          className="text-orange-500 font-medium underline"
        >
          See All
        </Link>
      </div>

      <div>
        {assignments?.map((assignment, idx) => (
          <div key={idx} className="border rounded-lg p-3 space-y-3 mb-3 relative">
            <p className="font-semibold flex items-center gap-2 pr-20">
              <Icon icon="iconoir:voice-ok" className="text-lg" />
              {assignment.title}
            </p>
            <span className="bg-yellow-100 px-2 py-1 text-sm font-semibold rounded-l-lg absolute right-0 -top-3">
              {assignment.status}
            </span>
            <div className="flex justify-between items-center">
              <span className="flex items-center gap-2 bg-gray-100 max-w-32 justify-center py-1 px-2 rounded-lg">
                <Icon icon="uiw:date" className=" text-blue-500" />
                {assignment.deadline.slice(0, 10)}
              </span>
              <Link
                to={`/assignment-details/${assignment.id}`}
                className="flex items-center gap-2 bg-orange-100 text-orange-600 px-4 py-1 rounded-md hover:bg-orange-200"
              >
                <span>See Details</span>
                <Icon icon="mdi:eye-outline" className="w-3 h-3" />
              </Link>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Assignments;
