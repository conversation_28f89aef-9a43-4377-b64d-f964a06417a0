import React from "react";
import Icon from "@/components/ui/Icon";
import CourseImage from "@/assets/MentorDashboard/myCOURSE.svg";
import useFetch from "@/hooks/useFetch";
import { ASSET_URL } from "@/config";
import { Link } from "react-router-dom";

const MyCourses = () => {
  const {
    data: mentorDashboard,
    isLoading,
    isError,
  } = useFetch({
    queryKey: "mentorDashboard",
    endPoint: "mentor-dashboard",
  });

  // Extract data from the mentorDashboard response
  const courseData = mentorDashboard?.data?.courses || [];

  // Handle loading and error states appropriately
  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error fetching data</div>;

  return (
    <div className="p-3 rounded-lg shadow-md">
      {/* Header Section */}
      <div className="flex items-center my-auto justify-between pb-3">
        <h3 className="text-xl font-semibold text-downriver-950">My Courses</h3>
        {/* <Icon icon="lineicons:menu-cheesburger" className="w-6 h-6" /> */}
        <Link
          to={"/my-courses"}
          className="bg-orange-100 border border-orange-200 text-orange-600 px-4 py-1 font-semibold rounded-md shadow-sm hover:bg-orange-200"
        >
          See All
        </Link>
      </div>

      {/* Courses List */}
      <div className="space-y-4">
        {/* Check if courseData is an array and has courses */}
        {courseData.length > 0 ? (
          courseData.map((course, index) => (
            <div
              key={index}
              className="flex items-center bg-white p-4 rounded-lg shadow-sm space-x-4 border border-slate-200"
            >
              {/* Course Image */}
              <img
                src={ASSET_URL + course?.thumbnail}
                alt="Course"
                className="w-24 h-16 object-cover rounded-lg"
              />

              <div className="flex flex-wrap items-center xl:justify-between w-full">
                {/* Course Info */}
                <div className="flex-1">
                  <h4 className="text-lg font-semibold text-downriver-950">
                    {course.title || "Course Name Not Available"}
                  </h4>

                  {/* Progress Bar */}
                  {/* <div className="flex gap-4 items-center my-auto">
                  <p className="text-sm text-gray-600 mb-2">50% Progress</p>

                  <div className="w-40 bg-gray-200 h-3 rounded-full">
                    <div
                      className="bg-green-700 h-3 rounded-full"
                      style={{ width: `${course.progress}%` }}
                    ></div>
                  </div>
                </div> */}

                  {/* Routine Link */}
                  <div className="flex items-center mt-2">
                    <Icon
                      icon="mdi:calendar-outline"
                      className="w-5 h-5 text-blue-600 mr-1"
                    />
                    <a href="#" className="text-green-600 text-sm font-medium">
                      See Routine
                    </a>
                  </div>
                </div>

                {/* See Details Button */}
                <Link
                  to={`/course-details/${course.id}`}
                  className="hover:bg-orange-500 transition-all duration-200 hover:text-white bg-white border border-slate-300 mt-3 h-10 lg:mt-0 text-black px-4 py-2 rounded-md flex items-center"
                >
                  See Details
                  <Icon icon="mdi:arrow-right" className="ml-2 w-4 h-4" />
                </Link>
              </div>
            </div>
          ))
        ) : (
          <div className="text-gray-600">
            No courses available at the moment.
          </div>
        )}
      </div>
    </div>
  );
};

export default MyCourses;
