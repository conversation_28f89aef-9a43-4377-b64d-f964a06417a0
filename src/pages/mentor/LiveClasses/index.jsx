import React from "react";
import useFetch from "@/hooks/useFetch";
import Loading from "@/components/Loading";
import BasicTablePage from "@/components/partials/common-table/table-basic";
import Breadcrumbs from "@/components/ui/Breadcrumbs";
import bgShape from "@/assets/images/svg/shape2.svg";
import bgShape2 from "@/assets/images/svg/shape3.svg";
import { useDispatch } from "react-redux";
import {
  setDeleteData,
  setDeleteModalShow,
  setEditData,
  setShowEditModal,
  setShowModal,
} from "@/store/assignmentStore";
import AddLiveClass from "./AddLiveClass";
import EditLiveClass from "./EditLiveClass";
import DeleteLiveClass from "./DeleteLiveClass";
import Badge from "@/components/ui/Badge";
import { useSelector } from "react-redux";
import api from "@/server/api";
import { Link } from "react-router-dom";
import ClassDetailsModal from "../dashboard/rightSide/ClassDetailsModal";
import { useQueryClient } from "@tanstack/react-query";

const LiveClassList = () => {
  const { showDeleteModal, deleteData, showEditModal, showModal, editData } =
    useSelector((state) => state.assignmentSlice);
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  //   const [showModal, setShowModal] = useState(false);
  //   const [apiParam, setApiParam] = useState("");

  const {
    data: liveClasses,
    isLoading,
    isError,
  } = useFetch({
    queryKey: `live-class-list`,
    endPoint: `/mentor/live-class-list`,
  });

  //   const res = useGetMentorListQuery(apiParam);
  //   const changePage = (val) => {
  //     setApiParam(val);
  //   };

  const handleStart = async (id) => {
    const { data } = await api.post("/mentor/start-live-class", {
      schedule_id: id,
    });
    if (data.status) {
      window.open(data.data.class_url, "_blank");
    }
  };

  const handleEndClass = async (id) => {
    const { data } = await api.post("mentor/end-live-class", {
      schedule_id: id,
    });
    console.log(data)
    if (data.status) {
      queryClient.invalidateQueries("/mentor/live-class-list");
    }
  };

  const data = liveClasses?.data;
  const columns = [
    {
      label: "#",
      field: "serial",
    },
    {
      label: "Live Class Title",
      field: "title",
    },
    {
      label: "Course Name",
      field: "course",
    },
    {
      label: "Date & Time",
      field: "schedule_datetime",
    },
    // {
    //   label: "Platform no",
    //   field: "status",
    // },
    // {
    //   label: "Student no",
    //   field: "student_assignments_count",
    // },
    {
      label: "Status",
      field: "has_started",
    },
    {
      label: "Action",
      field: "",
    },
  ];

  const tableData = data?.map((item, index) => {
    return {
      serial: <p className="font-semibold">{index + 1}</p>,
      title: <p>{item.title}</p>,
      course: <p>{item.course}</p>,
      schedule_datetime: <p>{`${item.schedule_datetime.split('T')[0]} ${item.schedule_datetime.split('T')[1].replace('Z', '')}`}</p>,
      status: (
        <p className="lowercase">{item.status || "https://meet.google.com"}</p>
      ),
      has_started: (
        <span className="space-x-2">
          {item.has_started && !item?.has_completed && (
            <button
              onClick={() => handleStart(item?.id)}
              className="px-3 py-1 bg-sky-500 rounded font-semibold text-white"
            >
              Join Now
            </button>
          )}

          {item?.has_started ||
            (!item?.has_completed && item?.needs_to_start && (
              <button
                className="px-3 py-1 bg-green-400 rounded font-semibold text-white"
                onClick={() => handleStart(item?.id)}
              >
                Start Now
              </button>
            ))}

          {!item?.needs_to_start && (
            <button className="px-3 py-1 bg-orange-400 rounded font-semibold text-white">
              Upcoming
            </button>
          )}

          {!item?.has_completed && item.has_started && (
            <button
              onClick={() => handleEndClass(item?.id)}
              className="px-3 py-1 bg-rose-400 rounded font-semibold text-white"
            >
              End Now
            </button>
          )}

          {item?.has_completed && (
            <button
              disabled
              className="px-3 py-1 bg-gray-600 rounded font-semibold text-white"
            >
              Completed
            </button>
          )}
        </span>
      ),
    };
  });

  const actions = [
    {
      name: "details",
      icon: "carbon:view",
      onClick: (val) => {
        dispatch(setShowModal(true));
        dispatch(setEditData(data[val]));
      },
    },
    {
      name: "edit",
      icon: "heroicons:pencil-square",
      onClick: (val) => {
        console.log(val);
        dispatch(setEditData(data[val]));
        dispatch(setShowEditModal(true));
      },
    },
    {
      name: "delete",
      icon: "heroicons-outline:trash",
      onClick: (val) => {
        console.log(val);
        dispatch(setDeleteData(data[val]));
        dispatch(setDeleteModalShow(true));
      },
    },
  ];

  const handleSubmit = () => {
    setShowModal(false);
  };

  const createPage = <AddLiveClass />;
  const editPage = <EditLiveClass />;

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div className="container mt-5">
      {/* <img className="absolute left-0 z-0" src={bgShape} alt="" />
      <img className="absolute right-0 z-0" src={bgShape2} alt="" /> */}
      <Breadcrumbs />
      <div className="relative text-center">
        <BasicTablePage
          title="Live Class List"
          createButton="Add Live Class"
          editPage={editPage}
          actions={actions}
          columns={columns}
          data={tableData}
          openCreateModal={() => dispatch(setShowModal(true))}
          // changePage={changePage}
          currentPage={data?.current_page}
          submitForm={handleSubmit}
          totalPages={Math.ceil(data?.total / data?.per_page)}
          // filter={filter}
          // setFilter={setApiParam}
        />

        <DeleteLiveClass
          showDeleteModal={showDeleteModal}
          setShowDeleteModal={setDeleteModalShow}
          data={deleteData}
        />
        {showModal && !editData?.id && (
          <AddLiveClass showModal={showModal} setShowModal={setShowModal} />
        )}
        {showModal && editData?.id && (
          <ClassDetailsModal
            showModal={showModal}
            setShowModal={setShowModal}
            setEditData={setEditData}
          />
        )}
        <EditLiveClass
          editData={editData}
          showEditModal={showEditModal}
          setShowEditModal={setShowEditModal}
          setEditData={setEditData}
        />
      </div>
    </div>
  );
};

export default LiveClassList;
