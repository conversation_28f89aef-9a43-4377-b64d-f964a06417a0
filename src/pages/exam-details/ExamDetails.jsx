import React, { useEffect, useState } from "react";
import { Icon } from "@iconify/react";
import useFetch from "@/hooks/useFetch";
import Loading from "@/components/Loading";
import { useParams } from "react-router-dom";
import GoBack from "@/components/ui/GoBack";
import examIcon from "@/assets/images/svg/exam.svg";
import { ASSET_URL } from "@/config";
import { WEITTEN_ASSET_URL } from "@/config";

const ExamDetails = () => {
  const { id } = useParams();
  const { data, isLoading, isError } = useFetch({
    queryKey: "examDetails",
    endPoint: `student-quiz-result-details-by-id/${id}`,
  });
  const [examDetails, setExamDetails] = useState(null);

  useEffect(() => {
    if (data) {
      setExamDetails(data.data.questions);
    }
  }, [data]);

  // Prevent back navigation after quiz submission
  useEffect(() => {
    const preventBack = () => {
      window.history.pushState(null, "", window.location.href);
    };

    // Push a new state to prevent back navigation
    preventBack();

    // Listen for popstate event (back button)
    const handlePopState = () => {
      preventBack();
    };

    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, []);

  const examResultInfo = data?.data;
  const negativeMark = examResultInfo?.negative_mark;
  const positiveMark = examResultInfo?.positive_mark;
  // console.log(examResultInfo);
  const [modalImage, setModalImage] = useState(null); // State for modal image
  // Handle modal opening

  const openModal = (imagePath) => {
    setModalImage(imagePath); // Set the image path in state to display in modal
  };

  // Handle modal closing
  const closeModal = () => {
    setModalImage(null); // Close the modal by clearing the state
  };

  if (isLoading) return <Loading />;
  if (isError) return <div>Error fetching data</div>;

  return (
    <div className="container">
      <GoBack title={examResultInfo?.title} />
      <div className="bg-[#FEF2E7] p-7 rounded mb-2">
        <div className="md:flex items-center justify-between w-full">
          <h4 className="text-gray-600 flex items-center gap-2">
            <img src={examIcon} alt="" />
            {examResultInfo?.title}
          </h4>
          <span className="flex items-center gap-3">
            <p className="text-xl font-semibold text-black">
              Duration: {examResultInfo.duration} Minutes
            </p>
          </span>
        </div>

        <div className="lg:flex justify-between items-center mt-4">
          <div className="rounded-lg w-full">
            {/* <span className="text-lg text-sky-700 my-12">Exam Result Summary</span> */}

            <div className="flex max-sm:flex-wrap gap-10 items-center text-gray-700 text-sm mb-2">
              <div className="flex items-center gap-3">
                <Icon
                  icon="bi:question-circle-fill"
                  className="text-blue-600 text-2xl mb-1"
                />
                <span>
                  <p className=" text-gray-500">No. of Questions</p>
                  <span className="font-semibold">
                    {examResultInfo?.number_of_question}
                  </span>
                </span>
              </div>

              <div className="flex items-center gap-3">
                <Icon
                  icon="icon-park-outline:positive-dynamics"
                  className="text-green-600 text-2xl mb-1"
                />
                <span>
                  <p className=" text-gray-500">Positive Mark</p>
                  <span className="font-semibold">
                    {examResultInfo?.positive_mark}
                  </span>
                </span>
              </div>

              <div className="flex items-center gap-3">
                <Icon
                  icon="mingcute:arrows-down-line"
                  className="text-red-400 text-2xl mb-1"
                />
                <span>
                  <p className=" text-gray-500">Negative Mark</p>
                  <span className="font-semibold">
                    {examResultInfo?.negative_mark}
                  </span>
                </span>
              </div>

              <div className="flex items-center gap-3">
                <Icon
                  icon="mdi:star-circle"
                  className="text-yellow-500 text-2xl mb-1"
                />
                <span>
                  <p className=" text-gray-500">Total Marks</p>
                  <span className="font-semibold">{examResultInfo?.mark}</span>
                </span>
              </div>

              <div className="flex items-center gap-3">
                <Icon
                  icon="material-symbols:timer-outline"
                  className="text-orange-600 text-2xl mb-1"
                />
                <span>
                  <p className=" text-gray-500">Total Time</p>
                  <span className="font-semibold">
                    {examResultInfo?.duration} Minutes
                  </span>
                </span>
              </div>
            </div>
          </div>
          <div className="w-44 text-center">
            <span className="text-lg text-green-500 px-4 py-2 border rounded shadow-md bg-white">
              {/* { examResultInfo?.mark} / {examResultInfo?.number_of_question} */}
              {examDetails?.filter((exam) => exam?.is_correct)?.length}/
              {examDetails?.length}
            </span>
          </div>
        </div>
      </div>

      {examResultInfo?.written_answers.length > 0 && (
        <div className="my-6">
          <h3 className="text-lg font-semibold">Written Answers:</h3>
          <div className="flex gap-4 mt-4">
            {examResultInfo?.written_answers.map((attachment, index) => (
              <div key={attachment.id} className="relative">
                <img
                  src={WEITTEN_ASSET_URL + attachment.attachment_url}
                  alt={`Submitted ${index}`}
                  className="w-auto h-32 object-cover rounded-md cursor-pointer"
                  onClick={() =>
                    openModal(WEITTEN_ASSET_URL + attachment.attachment_url)
                  }
                />
              </div>
            ))}
          </div>
        </div>
      )}
      {/* Modal to display the image */}
      {modalImage && (
        <div
          className="fixed inset-1 bg-black bg-opacity-50 flex justify-center items-center z-50"
          onClick={closeModal}
        >
          <div className="relative bg-white p-4 rounded-md">
            <button
              onClick={closeModal}
              className="absolute top-2 right-2 text-white bg-red-600 p-2 rounded-full m-8"
            >
              <Icon icon="mdi:close" className="text-xl" />
            </button>
            <img
              src={modalImage}
              alt="Modal Image"
              className="p-10 max-w-full max-h-screen object-contain"
            />
          </div>
        </div>
      )}

      {/* [
    {
        "id": 46,
        "organization_id": 1,
        "chapter_quiz_result_id": 116,
        "chapter_quiz_id": 6,
        "user_id": 25,
        "attachment_url": "uploads/written_answer/written_answer_6_0_1732005188.png",
        "deleted_at": null,
        "created_by": 25,
        "created_at": "2024-11-19T08:33:08.000000Z",
        "updated_at": "2024-11-19T08:33:08.000000Z"
    },
    {
        "id": 47,
        "organization_id": 1,
        "chapter_quiz_result_id": 116,
        "chapter_quiz_id": 6,
        "user_id": 25,
        "attachment_url": "uploads/written_answer/written_answer_6_1_1732005188.png",
        "deleted_at": null,
        "created_by": 25,
        "created_at": "2024-11-19T08:33:08.000000Z",
        "updated_at": "2024-11-19T08:33:08.000000Z"
    }
] */}

      <div className="shadow-md rounded-lg flex flex-col gap-2 overflow-hidden">
        {examDetails &&
          examDetails.map((question, index) => (
            <div
              key={index}
              className={`p-4 border-b border-gray-200  ${
                !question.answer1 &&
                !question.answer2 &&
                !question.answer3 &&
                !question.answer4
                  ? "bg-white"
                  : question.correct_answer1 == question.answer1 &&
                    question.correct_answer2 == question.answer2 &&
                    question.correct_answer3 == question.answer3 &&
                    question.correct_answer4 == question.answer4
                  ? "bg-green-50"
                  : "bg-red-50"
              }`}
            >
              {" "}
              {/* {console.log(question)} */}
              <div className="mb-4 sm:flex items-center justify-between">
                <b>
                  <span className="text-sky-600">Question:</span>{" "}
                  {question.question_text}
                </b>
                <span
                  className={`px-4 py-1.5 rounded-full max-sm:block max-sm:mt-2 max-w-32 text-center ${
                    question?.is_correct ? "bg-green-100" : "bg-red-100"
                  }`}
                >
                  {!question.answer1 &&
                  !question.answer2 &&
                  !question.answer3 &&
                  !question.answer4
                    ? "0 point"
                    : question?.is_correct
                    ? `${positiveMark} point${positiveMark > 1 ? "s" : ""}`
                    : `- ${negativeMark} point${negativeMark > 1 ? "s" : ""}`}
                </span>
              </div>
              <div className="mt-2 flex flex-wrap items-center justify-between">
                <div
                  style={{
                    color: question.answer1
                      ? question.correct_answer1
                        ? "green"
                        : "red"
                      : "black",
                  }}
                  className="flex flex-col space-y-2 max-md:w-1/2 p-3"
                >
                  <label className="flex items-center space-x-2">
                    {/* {console.log(examResultInfo)} */}
                    <input
                      type="checkbox"
                      checked={question.answer1}
                      disabled
                    />
                    {question?.option1_image ? (
                      <span className="flex items-center gap-2">
                        <img
                          src={ASSET_URL + question?.option1_image}
                          className="lg:max-w-[200px] sm:w-[150px] max-sm:w-[100px]"
                          alt=""
                        />{" "}
                        <span>{question.option1}</span>
                      </span>
                    ) : (
                      <span className="text-sm">{question.option1}</span>
                    )}
                    {/* <p className="ml-2"> {question.option1}</p> */}
                  </label>
                </div>
                <div
                  style={{
                    color: question.answer2
                      ? question.correct_answer2
                        ? "green"
                        : "red"
                      : "black",
                  }}
                  className="flex flex-col space-y-2 max-sm:w-1/2 p-3"
                >
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={question.answer2}
                      disabled
                    />
                    {question?.option2_image ? (
                      <span className="flex items-center gap-2">
                        <img
                          src={ASSET_URL + question?.option2_image}
                          className="lg:max-w-[200px] sm:w-[150px] max-sm:w-[100px]"
                          alt=""
                        />{" "}
                        <span>{question.option2}</span>
                      </span>
                    ) : (
                      <span>{question.option2}</span>
                    )}
                    {/* <p className="ml-2"> {question.option2}</p> */}
                  </label>
                </div>
                <div
                  style={{
                    color: question.answer3
                      ? question.correct_answer3
                        ? "green"
                        : "red"
                      : "black",
                  }}
                  className="flex flex-col space-y-2 max-sm:w-1/2 p-3"
                >
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={question.answer3}
                      disabled
                    />
                    {question?.option3_image ? (
                      <span className="flex items-center gap-2">
                        <img
                          src={ASSET_URL + question?.option3_image}
                          className="lg:max-w-[200px] sm:w-[150px] max-sm:w-[100px]"
                          alt=""
                        />{" "}
                        <span>{question.option3}</span>
                      </span>
                    ) : (
                      <span>{question.option3}</span>
                    )}
                    {/* <p className="ml-2"> {question.option2}</p> */}
                  </label>
                </div>
                <div
                  style={{
                    color: question.answer4
                      ? question.correct_answer4
                        ? "green"
                        : "red"
                      : "black",
                  }}
                  className="flex flex-col space-y-2 max-sm:w-1/2 p-3"
                >
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={question.answer4}
                      disabled
                    />
                    {question?.option4_image ? (
                      <span className="flex items-center gap-2">
                        <img
                          src={ASSET_URL + question?.option4_image}
                          className="lg:max-w-[200px] sm:w-[150px] max-sm:w-[100px]"
                          alt=""
                        />{" "}
                        <span>{question.option4}</span>
                      </span>
                    ) : (
                      <span>{question.option4}</span>
                    )}
                    {/* <p className="ml-2"> {question.option2}</p> */}
                  </label>
                </div>
              </div>
              <div className="">
                {!question.answer1 &&
                !question.answer2 &&
                !question.answer3 &&
                !question.answer4 ? (
                  <p className="text-red-500 font-semibold text-base">
                    Answer Not Given
                  </p>
                ) : (
                  <>
                    <p className="text-green-500 font-semibold text-base">
                      Correct Answer:{" "}
                      {[1, 2, 3, 4]
                        .filter((index) => question[`correct_answer${index}`]) // Filter only correct answers
                        .map((index) => {
                          const optionKey = `option${index}`;
                          const imageKey = `option${index}_image`;

                          return question[imageKey] ? (
                            <img
                              key={index}
                              className="inline-block lg:max-w-[100px] sm:w-[75px] max-sm:w-[50px] mx-2"
                              src={ASSET_URL + question[imageKey]}
                              alt={`Option ${index}`}
                            />
                          ) : (
                            question[optionKey]
                          );
                        })
                        .filter(Boolean) // Ensure no empty values
                        .reduce((prev, curr) => [prev, ", ", curr])}{" "}
                      {/* Join with commas */}
                    </p>

                    {/* Explanation Section */}
                    <div className="pt-2">
                      {question?.explanation_image ? (
                        <img
                          className="lg:max-w-[200px] sm:w-[150px] max-sm:w-[100px]"
                          src={ASSET_URL + question?.explanation_image}
                          alt="Explanation"
                        />
                      ) : (
                        <p className="text-gray-700">
                          {question?.explanation_text}
                        </p>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
          ))}
      </div>
      {/* <div className="rounded-lg shadow-lg p-5 mt-5 border">
        <div className="border-2 border-dashed border-gray-300 rounded-xl p-3">
          <div className="flex items-center gap-2">
            <h2 className="text-xl text-sky-600 flex items-center gap-2">
              <Icon
                icon="fluent:megaphone-loud-32-filled"
                className="text-xl"
              />{" "}
              Written Exam
            </h2>
            <span className="text-orange-400 px-3 py-1.5 font-semibold bg-orange-100 rounded">Review Panding</span>
          </div>
          <p className=" text-gray-500">
            Wait for your teacher to review your submitted exam paper
          </p>
        </div>
      </div> */}
    </div>
  );
};

export default ExamDetails;
