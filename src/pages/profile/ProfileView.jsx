// import React, { useState } from "react";
// import Card from "@/components/ui/Card";
// import user from "@/assets/images/course/Admin.png";
// import bar from "@/assets/images/course/bar.svg";
// import LeftCard from "./LeftCard";
// import RightCard from "./RightCard";
// import Icon from "@/components/ui/Icon";
// import useFetch from "@/hooks/useFetch";
// import { BASE_URL } from "@/config";
// import { useNavigate } from "react-router-dom";
// import GoBack from "@/components/ui/GoBack";

// export const categories = [
//   {
//     name: "Recent",
//     posts: [
//       {
//         id: 1,
//         title: "Does drinking coffee make you smarter?",
//         date: "5h ago",
//         commentCount: 5,
//         shareCount: 2,
//       },
//       {
//         id: 2,
//         title: "So you've bought coffee... now what?",
//         date: "2h ago",
//         commentCount: 3,
//         shareCount: 2,
//       },
//     ],
//   },
//   {
//     name: "Popular",
//     posts: [
//       {
//         id: 1,
//         title: "Is tech making coffee better or worse?",
//         date: "Jan 7",
//         commentCount: 29,
//         shareCount: 16,
//       },
//       {
//         id: 2,
//         title: "The most innovative things happening in coffee",
//         date: "Mar 19",
//         commentCount: 24,
//         shareCount: 12,
//       },
//     ],
//   },
//   {
//     name: "Trending",
//     posts: [
//       {
//         id: 1,
//         title: "Ask Me Anything: 10 answers to your questions about coffee",
//         date: "2d ago",
//         commentCount: 9,
//         shareCount: 5,
//       },
//       {
//         id: 2,
//         title: "The worst advice we've ever heard about coffee",
//         date: "4d ago",
//         commentCount: 1,
//         shareCount: 2,
//       },
//     ],
//   },
// ];

// const ProfileView = () => {
//   const [activeTab, setActiveTab] = useState(0);
//   const navigate = useNavigate();
//   //   const { data: userData, error } = useFetch({
//   //     queryKey: "user-data",
//   //     endPoint: `${BASE_URL}/user-service/trainee/100310052`,
//   //   });
//   //   if (error) {
//   //     console.error("Error fetching user data:", error);
//   //   }

//   return (
//     <div className="container ">
//       <GoBack title={"Profile"} />
//       <div className="flex flex-col gap-7">
//         <div className="grid grid-cols-6 border shadow-md bg-blue-50 rounded-lg">
//           <div className="col-span-3 relative flex items-center gap-7 p-9">
//             <div>
//               <img
//                 src={user}
//                 alt="user"
//                 className="w-40 h-40 rounded-full mx-auto border-4 border-sky-600 p-2"
//               />
//               <div className="absolute ml-[120px] mt-[-50px] bg-white p-2 rounded-full border overflow-visible z-10">
//                 <Icon
//                   icon="ic:outline-edit"
//                   className="text-2xl cursor-pointer"
//                 />
//               </div>
//             </div>
//             <div className="space-y-2">
//               <p className="text-sky-600 text-lg">Name</p>
//               <h6 className="text-gray-600 text-xl font-semibold">
//                 {/* {userData?.data?.emisUserJson?.Name}  */}
//                 Miss Nipun Lia
//               </h6>
//               <p className="text-sky-600 cursor-pointer text-lg flex items-center gap-2">
//                 Change name <Icon icon="fa-regular:edit" className="" />
//               </p>
//             </div>
//           </div>
//           {/* <div className="col-span-1 flex items-center justify-center">
//             <div>
//               <img src={bar} alt="bar" className="" />
//             </div>
//           </div> */}
//           <div className="col-span-3 flex items-center">
//             <div className="">
//               <div className="flex items-center gap-2 mb-3">
//                 <div>
//                   <Icon icon="bi:phone" className="text-primary-600 text-2xl" />
//                 </div>
//                 <div>
//                   <p className="">মোবাইল নম্বর</p>
//                   <p className="text-primary-600">
//                     {/* {userData?.data?.emisUserJson?.MobileNo || "Not found"} */}
//                   </p>
//                 </div>
//               </div>
//               <div className="flex items-center gap-3">
//                 <div>
//                   <Icon
//                     icon="bi:envelope"
//                     className="text-primary-600 text-2xl"
//                   />
//                 </div>
//                 <div>
//                   <p>ইমেইল এড্রেস</p>
//                   <p className="text-primary-600">
//                     {/* {userData?.data?.emisUserJson?.Email} */}
//                   </p>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>
//         {/* <div className="grid grid-cols-7 h-full w-full">
//           <div className="col-span-3 pl-4 pt-4 pb-4 h-full">
//             <Card className="h-full border border-primary-200">
//               <LeftCard data={userData?.data?.emisUserJson} />
//             </Card>
//           </div>
//           <div className="col-span-4 pr-4 pt-4 pb-4 pl-4 h-full">
//             <Card className="h-full border border-primary-200">
//               <RightCard data={userData?.data?.emisUserJson} />
//             </Card>
//           </div>
//         </div> */}

//         <div className="flex h-full w-full justify-center border border-gray-300 rounded-lg shadow-md">
//           <div className="w-full">
//             {/* Tab List */}
//             <div className="flex gap-4 mb-3 w-full">
//               {categories.map((category, index) => (
//                 <div
//                   className={`flex gap-4 py-5 w-full ${
//                     activeTab === index
//                       ? "text-sky-700 border-b-2 border-sky-600"
//                       : "text-gray-600"
//                   }
//                     hover:bg-white/5 focus:outline-none`}
//                 >
//                   <button
//                     key={category.name}
//                     onClick={() => setActiveTab(index)}
//                     className={` px-3 w-full text-lg flex items-center border-r-2 border-gray-300 justify-center gap-2 font-semibold`}
//                   >
//                     <Icon icon="basil:user-outline" className="text-2xl" /> {category.name}
//                   </button>
//                 </div>
//               ))}
//             </div>

//             {/* Tab Panels */}
//             <div className="rounded-xl bg-white p-3">
//               <ul>
//                 {categories[activeTab].posts.map((post) => (
//                   <li
//                     key={post.id}
//                     className="relative rounded-md p-3 text-lg transition hover:bg-white/5"
//                   >
//                     <a href="#" className="font-semibold text-gray-800">
//                       <span className="absolute inset-0" />
//                       {post.title}
//                     </a>
//                     <ul className="flex gap-2 text-gray-600" aria-hidden="true">
//                       <li>{post.date}</li>
//                       <li aria-hidden="true">&middot;</li>
//                       <li>{post.commentCount} comments</li>
//                       <li aria-hidden="true">&middot;</li>
//                       <li>{post.shareCount} shares</li>
//                     </ul>
//                   </li>
//                 ))}
//               </ul>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default ProfileView;
import React, { useRef, useState } from "react";
import Card from "@/components/ui/Card";
import user from "@/assets/images/course/Admin.png";
import bar from "@/assets/images/course/bar.svg";
import LeftCard from "./LeftCard";
import RightCard from "./RightCard";
import Icon from "@/components/ui/Icon";
import useFetch from "@/hooks/useFetch";
import { ASSET_URL, BASE_URL } from "@/config";
import { useNavigate } from "react-router-dom";
import GoBack from "@/components/ui/GoBack";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import Textinput from "@/components/ui/Textinput";
import Button from "@/components/ui/Button";
import { Form, Formik } from "formik";
import InputField from "@/components/ui/InputField";
import * as Yup from "yup";
import api from "@/server/api";
import Loading from "@/components/Loading";
import { toast } from "react-toastify";

export const ProfileInfoItem = ({
  title,
  data,
  icon,
  inputName,
  inputType,
  inputPlaceholder,
  textStyle,
  options,
}) => {
  const [edit, setEdit] = useState(false);
  const [inputValue, setInputValue] = useState(data ? data : "");
  const [submitLoading, setSubmitLoading] = useState(false);
  const [error, setError] = useState(""); // State for validation errors
  const queryClient = useQueryClient();

  const handleClose = () => {
    setEdit(false);
    setInputValue(data);
    setError(""); // Reset error
  };

  const handleInputChange = (e) => {
    const currentValue = e.target.value;
    setInputValue(currentValue);
    setError(""); // Clear error on input change
  };

  const validateInput = () => {
    if (!inputValue) {
      return `${title} is required.`;
    }
    if (inputType === "email" && !/^\S+@\S+\.\S+$/.test(inputValue)) {
      return "Invalid email address.";
    }
    if (inputType === "number" && isNaN(inputValue)) {
      return `${title} must be a number.`;
    }
    return ""; // No errors
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationError = validateInput();
    if (validationError) {
      setError(validationError);
      return;
    }

    setSubmitLoading(true);

    const userData = { [inputName]: inputValue };

    try {
      await api.post("/mentor/update-profile", userData).then((response) => {
        const updatedData = response?.data;
        if (updatedData?.data) {
          setInputValue(updatedData.data[inputName]);
          setEdit(false);
          setSubmitLoading(false);
          queryClient.invalidateQueries("/profile");
        }
      });
    } catch (error) {
      console.log(error);
      setSubmitLoading(false);
    }
  };

  return (
    <div className="flex items-start gap-4">
      <Icon icon={icon} className="text-2xl text-sky-600" />
      <div>
        <p className="text-lg font-semibold text-gray-600">{title}</p>
        <div>
          {edit ? (
            <form
              onSubmit={handleSubmit}
              className="flex gap-1 mt-2 justify-center items-end"
            >
              {inputType === "textarea" || inputType === "select" || (
                <input
                  onChange={handleInputChange}
                  value={inputValue}
                  name={inputName}
                  type={inputType}
                  placeholder={inputPlaceholder}
                  className="p-2 border-2 rounded-lg min-w-52"
                />
              )}
              {inputType === "textarea" && (
                <textarea
                  name={inputName}
                  className="p-2 border-2 rounded-lg h-20 min-w-52"
                  onChange={handleInputChange}
                  value={inputValue}
                  placeholder={inputPlaceholder}
                ></textarea>
              )}
              {inputType === "select" && (
                <select
                  name={inputName}
                  value={inputValue}
                  onChange={handleInputChange}
                  className="p-2 border-2 rounded-lg min-w-48"
                >
                  <option value="" disabled>
                    Select {title}
                  </option>
                  {options?.map((item, idx) => (
                    <option key={idx} value={item.value}>
                      {item.label}
                    </option>
                  ))}
                </select>
              )}
              <button
                type="submit"
                className="p-2.5 rounded-full border bg-sky-50"
                disabled={submitLoading}
              >
                {submitLoading ? (
                  <Icon icon="svg-spinners:3-dots-bounce" className="text-lg" />
                ) : (
                  <Icon icon="mdi:success" className="text-xl" />
                )}
              </button>
              <button
                onClick={handleClose}
                className="p-3 rounded-full border bg-red-50"
              >
                <Icon icon="akar-icons:cross" />
              </button>
            </form>
          ) : (
            <p className={textStyle ? textStyle : "text-lg"}>{inputValue}</p>
          )}
          {edit ? (
            error && <p className="text-red-500 text-sm">{error}</p> // Show validation error
          ) : (
            <p
              onClick={() => setEdit(true)}
              className="text-sky-600 flex items-center gap-2 cursor-pointer"
            >
              {inputValue ? "Edit" : "Add"} {title}{" "}
              <Icon icon="ic:outline-edit" className="text-xl" />
            </p>
          )}
        </div>
      </div>
    </div>
  );
};


const ProfileView = () => {
  const [activeTab, setActiveTab] = useState(0);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [showName, setShowName] = useState({});
  const [showImagePreview, setShowImagePreview] = useState(null); // Preview state for the image
  const [imageFile, setImageFile] = useState(null); // Actual image file
  const [isEditing, setIsEditing] = useState(false); // Toggle between edit and save mode
  const fileInputRef = useRef();

  const categories = [
    "Basic Information",
    "Address",
    "Education",
    "Others Info",
  ];

  const handleClearFile = () => {
    setShowName("");
    setShowImagePreview(null);
    fileInputRef.current.value = "";
    setIsEditing(false);
  };

  const { data, error, isLoading } = useFetch({
    queryKey: "user-data",
    endPoint: `/profile`,
  });

  if (error) {
    console.error("Error fetching user data:", error);
  }

  const userData = data?.data;

  if (isLoading) {
    return <Loading />;
  }

  // Handle file change (select image)
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);
      setShowImagePreview(URL.createObjectURL(file)); // Preview the selected image
      setIsEditing(true); // Set to editing mode
    }
  };

  // Handle file picker click (trigger file input)
  const handleEditClick = () => {
    fileInputRef.current.click();
  };

  // Handle save (submit the image via API)
  const handleSaveImage = async () => {
    if (imageFile) {
      try {
        setLoading(true);
        const formData = new FormData();
        formData.append("image", imageFile);

        // Make the API call to update the image
        const response = await api.filepost(`/mentor/update-profile`, formData);

        if (response.status === 200) {
          setIsEditing(false);
        }
      } catch (error) {
        console.error("Error uploading image:", error);
        toast.error("Something went wrong");
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle cancel (clear image and reset to previous state)
  const handleCancel = () => {
    setShowImagePreview(null);
    setImageFile(null);
    setIsEditing(false); // Exit editing mode
  };

  return (
    <div className="container">
      <GoBack title={"Profile"} />
      <div className="flex flex-col gap-7">
        <div className="grid grid-cols-2 md:grid-cols-6 border shadow-md bg-orange-50 rounded-lg relative">
          <div className="col-span-3 relative flex items-center gap-7 p-9">
            <div>
              <div>
                {/* Show profile image or preview */}
                <img
                  src={
                    showImagePreview ||
                    (userData?.image ? ASSET_URL + userData?.image : user)
                  }
                  alt="user"
                  className="w-40 h-40 max-sm:w-28 max-sm:h-28 rounded-full object-cover mx-auto border-4 border-sky-600 p-2"
                />
              </div>
              <div className="absolute ml-[90px] md:ml-[120px] mt-[-50px] bg-white p-2 max-sm:p-1 rounded-full border overflow-visible z-10">
                <input
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  className="hidden"
                  type="file"
                />
                {/* Edit or save button depending on state */}
                {isEditing ? (
                  <div className="flex gap-2">
                    <span onClick={handleSaveImage}>
                      {loading ? (
                        <Icon
                          icon="svg-spinners:3-dots-bounce"
                          className="text-lg"
                        />
                      ) : (
                        <Icon
                          icon="ic:outline-check"
                          className="text-2xl cursor-pointer"
                        />
                      )}
                    </span>
                    <span onClick={handleCancel}>
                      <Icon
                        icon="ic:outline-clear"
                        className="text-2xl cursor-pointer"
                      />
                    </span>
                  </div>
                ) : (
                  <span onClick={handleEditClick}>
                    <Icon
                      // icon="ic:outline-edit"
                      icon="prime:camera"
                      className="text-2xl cursor-pointer text-sky-600"
                    />
                  </span>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <ProfileInfoItem
                title={"Name"}
                icon={""}
                data={userData?.name}
                textStyle={"text-3xl font-semibold"}
                inputName="name"
                inputType="text"
                inputPlaceholder="Enter updated Name"
              />
            </div>
          </div>

          <div className="col-span-3 flex items-center max-sm:text-center max-sm:justify-center max-sm:pb-5 text-lg">
            <div className="space-y-2">
              <p className="text-xl font-semibold text-gray-500">
                Specialization
              </p>
              <p>{userData?.profession}</p>
              <p>{userData?.institute}</p>
            </div>

            <Icon
              icon="tabler:dots-vertical"
              className="text-2xl text-sky-600 absolute right-4 top-4 cursor-pointer"
            />
          </div>
        </div>

        <div className="flex h-full w-full justify-center border border-gray-300 rounded-lg shadow-md">
          <div className="w-full">
            {/* Tab List */}
            <div className="grid md:grid-cols-3 lg:grid-cols-4 mb-3 w-full">
              {categories.map((category, index) => (
                <div
                  key={index}
                  className={` py-5 w-full ${
                    activeTab === index
                      ? "text-sky-700 border-b border-sky-600"
                      : "text-gray-600"
                  }
                    hover:bg-white/5 focus:outline-none`}
                >
                  <button
                    key={category}
                    onClick={() => setActiveTab(index)}
                    className={` px-3 w-full text-lg flex items-center ${
                      index !== categories.length - 1 && "sm:border-r-2"
                    } border-gray-300 justify-center gap-2 font-semibold`}
                  >
                    <Icon icon="basil:user-outline" className="text-2xl" />{" "}
                    {category}
                  </button>
                </div>
              ))}
            </div>

            {/* Tab Panels */}
            <div className="rounded-xl grid sm:grid-cols-2 bg-white p-8 gap-8">
              {categories[activeTab] === "Basic Information" && (
                <>
                  <ProfileInfoItem
                    title={"Email"}
                    icon={"tabler:mail"}
                    data={userData && userData?.email}
                    inputName="email"
                    inputType="email"
                    inputPlaceholder="Enter your mail"
                  />
                  <ProfileInfoItem
                    title={"Contact"}
                    icon={"mdi:call"}
                    data={userData?.contact_no}
                    inputName="contact_no"
                    inputType="number"
                    inputPlaceholder="Enter your number"
                  />
                  <ProfileInfoItem
                    title={"NID No"}
                    icon={"teenyicons:id-outline"}
                    data={userData?.nid_no}
                    inputName="nid_no"
                    inputType="number"
                    inputPlaceholder="Enter your NID no."
                  />
                  <ProfileInfoItem
                    title={"Date of Birth"}
                    icon={"uiw:date"}
                    data={userData?.date_of_birth}
                    inputName="date_of_birth"
                    inputType="date"
                    inputPlaceholder="Enter Date of Birth"
                  />
                  <ProfileInfoItem
                    title={"Birth Certificate No"}
                    icon={"uiw:date"}
                    data={userData?.birth_certificate_no}
                    inputName="birth_certificate_no"
                    inputType="number"
                    inputPlaceholder="Enter Birth Certificate no"
                  />
                  <ProfileInfoItem
                    title={"Passport No"}
                    icon={"fontisto:passport-alt"}
                    data={userData?.passport_no}
                    inputName="passport_no"
                    inputType="number"
                    inputPlaceholder="Enter passport no"
                  />
                  <ProfileInfoItem
                    title={"Gender"}
                    icon={"bi:gender-male"}
                    data={userData?.gender}
                    options={[
                      { label: "Male", value: "Male" },
                      { label: "Female", value: "Female" },
                    ]}
                    inputName="gender"
                    inputType="select"
                    inputPlaceholder="Enter gender"
                  />
                  <ProfileInfoItem
                    title={"Blood Group"}
                    icon={"hugeicons:blood"}
                    data={userData?.blood_group}
                    options={[
                      { label: "A+", value: "A+" },
                      { label: "A-", value: "A-" },
                      { label: "B+", value: "B+" },
                      { label: "B-", value: "B-" },
                      { label: "AB+", value: "AB+" },
                      { label: "AB-", value: "AB-" },
                      { label: "O+", value: "O+" },
                      { label: "O-", value: "O-" },
                    ]}
                    inputName="blood_group"
                    inputType="select"
                    inputPlaceholder="Enter Blood group"
                  />
                  <ProfileInfoItem
                    title={"Bio"}
                    icon={"hugeicons:blood"}
                    data={userData?.bio}
                    inputName="bio"
                    inputType="textarea"
                    inputPlaceholder="Enter Blood group"
                  />
                </>
              )}

              {categories[activeTab] === "Address" && (
                <>
                  <ProfileInfoItem
                    title={"Current Address"}
                    icon={"tabler:mail"}
                    data={userData?.current_address}
                    inputName="current_address"
                    inputType="text"
                    inputPlaceholder="Enter current address"
                  />
                  <ProfileInfoItem
                    title={"Permanent Address"}
                    icon={"tabler:mail"}
                    data={userData?.permanent_address}
                    inputName="permanent_address"
                    inputType="text"
                    inputPlaceholder="Enter permanent address"
                  />
                </>
              )}

              {categories[activeTab] === "Education" && (
                <>
                  <ProfileInfoItem
                    title={"Education"}
                    icon={"tabler:mail"}
                    data={userData?.education}
                    inputName="education"
                    inputType="text"
                    inputPlaceholder="Enter your education"
                  />
                  <ProfileInfoItem
                    title={"Institute"}
                    icon={"tabler:mail"}
                    data={userData?.institute}
                    inputName="institute"
                    inputType="text"
                    inputPlaceholder="Enter institute name"
                  />
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileView;
