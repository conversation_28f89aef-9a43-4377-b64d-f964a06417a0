/**
 * DateTime utility functions for handling timezone conversions and OTP expiration
 */

/**
 * Convert UTC datetime string to local datetime
 * @param {string} utcDateString - UTC datetime string from server
 * @returns {Date} Local Date object
 */
export const convertUTCToLocal = (utcDateString) => {
  if (!utcDateString) return null;
  
  // Create Date object from UTC string
  const utcDate = new Date(utcDateString);
  
  // The Date constructor automatically converts to local timezone
  return utcDate;
};

/**
 * Calculate time remaining until expiration
 * @param {string|Date} expireTime - Expiration time (UTC string or Date object)
 * @returns {number} Seconds remaining (0 if expired)
 */
export const calculateTimeLeft = (expireTime) => {
  if (!expireTime) return 0;
  
  const now = new Date();
  const expireDate = expireTime instanceof Date ? expireTime : new Date(expireTime);
  
  const timeDiff = expireDate - now;
  return timeDiff > 0 ? Math.floor(timeDiff / 1000) : 0;
};

/**
 * Format seconds into MM:SS format
 * @param {number} totalSeconds - Total seconds
 * @returns {string} Formatted time string
 */
export const formatTime = (totalSeconds) => {
  if (totalSeconds <= 0) return "00:00";
  
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

/**
 * Format seconds into human readable format (e.g., "2 min 30 sec")
 * @param {number} totalSeconds - Total seconds
 * @returns {string} Human readable time string
 */
export const formatTimeHuman = (totalSeconds) => {
  if (totalSeconds <= 0) return "0 sec";
  
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  
  if (minutes > 0) {
    return `${minutes} min ${seconds < 10 ? `0${seconds}` : seconds} sec`;
  } else {
    return `${seconds} sec`;
  }
};

/**
 * Check if a datetime is expired
 * @param {string|Date} expireTime - Expiration time
 * @returns {boolean} True if expired
 */
export const isExpired = (expireTime) => {
  return calculateTimeLeft(expireTime) <= 0;
};

/**
 * Get timezone offset in hours
 * @returns {number} Timezone offset in hours
 */
export const getTimezoneOffset = () => {
  return -new Date().getTimezoneOffset() / 60;
};

/**
 * Debug logging for datetime operations
 * @param {string} label - Label for the log
 * @param {string|Date} utcTime - UTC time
 * @param {Date} localTime - Local time
 */
export const debugDateTime = (label, utcTime, localTime = null) => {
  console.group(`🕒 DateTime Debug: ${label}`);
  console.log("UTC Time:", utcTime);
  console.log("Local Time:", localTime || new Date(utcTime));
  console.log("Current Local Time:", new Date().toLocaleString());
  console.log("Timezone Offset:", getTimezoneOffset(), "hours");
  console.log("Time Left:", calculateTimeLeft(localTime || utcTime), "seconds");
  console.groupEnd();
};

/**
 * Handle OTP expiration time from server response
 * @param {Object} serverResponse - Server response containing expired_at
 * @returns {Object} Processed response with localized time
 */
export const processOTPResponse = (serverResponse) => {
  if (!serverResponse?.data?.expired_at) {
    return serverResponse;
  }
  
  const expiredAtUTC = serverResponse.data.expired_at;
  const expiredAtLocal = convertUTCToLocal(expiredAtUTC);
  
  // Debug logging
  debugDateTime("OTP Response Processing", expiredAtUTC, expiredAtLocal);
  
  return {
    ...serverResponse,
    data: {
      ...serverResponse.data,
      expired_at: expiredAtLocal.toISOString(),
      expired_at_local: expiredAtLocal,
      expired_at_utc: expiredAtUTC
    }
  };
};

/**
 * Create a countdown timer that updates every second
 * @param {string|Date} expireTime - Expiration time
 * @param {Function} onUpdate - Callback function called every second with remaining time
 * @param {Function} onExpire - Callback function called when timer expires
 * @returns {Function} Cleanup function to clear the timer
 */
export const createCountdownTimer = (expireTime, onUpdate, onExpire) => {
  if (!expireTime) return () => {};
  
  const updateTimer = () => {
    const timeLeft = calculateTimeLeft(expireTime);
    onUpdate(timeLeft);
    
    if (timeLeft <= 0 && onExpire) {
      onExpire();
    }
  };
  
  // Initial update
  updateTimer();
  
  // Set up interval
  const intervalId = setInterval(updateTimer, 1000);
  
  // Return cleanup function
  return () => clearInterval(intervalId);
};

export default {
  convertUTCToLocal,
  calculateTimeLeft,
  formatTime,
  formatTimeHuman,
  isExpired,
  getTimezoneOffset,
  debugDateTime,
  processOTPResponse,
  createCountdownTimer
};
