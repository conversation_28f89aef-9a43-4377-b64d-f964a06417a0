import React from 'react';
import { Icon } from "@iconify/react";

/**
 * Format notification time to a human-readable format
 * @param {string} timeString - ISO timestamp string
 * @returns {string} Formatted time string
 */
export const formatTime = (timeString) => {
  if (!timeString) return '';
  
  const date = new Date(timeString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    const hours = Math.floor(diffTime / (1000 * 60 * 60));
    if (hours === 0) {
      const minutes = Math.floor(diffTime / (1000 * 60));
      return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    }
    return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
  } else if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays < 7) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
};

/**
 * Get notification icon based on notification type
 * @param {string} type - Notification type
 * @returns {JSX.Element} Icon component
 */
export const getNotificationIcon = (type) => {
  const iconMap = {
    'message': <Icon icon="mdi:message" className="h-5 w-5 text-blue-500" />,
    'alert': <Icon icon="mdi:alert-circle" className="h-5 w-5 text-red-500" />,
    'update': <Icon icon="mdi:update" className="h-5 w-5 text-green-500" />,
    'course': <Icon icon="mdi:book-open-variant" className="h-5 w-5 text-purple-500" />,
    'assignment': <Icon icon="mdi:clipboard-text" className="h-5 w-5 text-orange-500" />,
    'default': <Icon icon="mdi:bell" className="h-5 w-5 text-gray-500" />
  };

  return iconMap[type] || iconMap.default;
};
