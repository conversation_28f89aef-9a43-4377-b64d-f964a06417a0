import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { useSelector } from "react-redux";
import { ASSET_URL } from "@/config";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/Textarea";
import CustomInput from "./CustomInput";
import avatar from "@/assets/images/avatar/av-1.svg";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import api from "@/server/api";
import { toast } from "react-toastify";
import { useQueryClient } from "@tanstack/react-query";
import useFetch from "@/hooks/useFetch";
import Modal from "@/components/ui/Modal";

const DiscussionForum = ({ courseId, templateStyle = "template-one" }) => {
  const { data: fetchedDiscussions, isLoading: loading } = useFetch({
    queryKey: "course-discussions",
    endPoint: "discussions",
    params: { course_id: courseId },
    enabled: !!courseId,
  });

  const discussions = Array.isArray(fetchedDiscussions?.data?.data) ? fetchedDiscussions.data.data : [];

  const [activeTab, setActiveTab] = useState("recent");
  const [editorContent, setEditorContent] = useState("");
  const [discussionTitle, setDiscussionTitle] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportReason, setReportReason] = useState("");
  const [customReportReason, setCustomReportReason] = useState("");
  const [reportType, setReportType] = useState({ discussionId: null, commentId: null });
  const [reportSubmitting, setReportSubmitting] = useState(false);
  const { user } = useSelector((state) => state.auth);
  const queryClient = useQueryClient();

  const filteredDiscussions = () => {
    if (!Array.isArray(discussions)) return [];
    if (activeTab === "recent") return [...discussions].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    if (activeTab === "popular") return [...discussions].sort((a, b) => (b.likes_count || 0) - (a.likes_count || 0));
    if (activeTab === "unanswered") return discussions.filter((discussion) => !discussion.comments || discussion.comments.length === 0);
    return discussions;
  };

  const formatDate = (dateString) => {
    const options = { year: "numeric", month: "short", day: "numeric", hour: "2-digit", minute: "2-digit" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const invalidateDiscussions = () => {
    queryClient.invalidateQueries({ queryKey: ["course-discussions", { course_id: courseId }] });
  };

  const handlePostSubmit = async () => {
    if (!editorContent.trim() || !discussionTitle.trim()) {
      toast.error("Please provide both a title and content for your discussion");
      return;
    }
    setSubmitting(true);
    try {
      await api.post("discussions", {
        course_id: parseInt(courseId),
        title: discussionTitle,
        content: editorContent,
      });
      invalidateDiscussions();
      setEditorContent("");
      setDiscussionTitle("");
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to post discussion. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  const handleReply = async (discussionId, replyContent) => {
    if (!replyContent.trim()) return;
    try {
      await api.post("comments", {
        discussion_id: discussionId,
        parent_id: null,
        content: replyContent,
      });
      invalidateDiscussions();
    } catch {
      toast.error("Failed to post comment. Please try again.");
    }
  };

  const handleLike = async (discussionId, replyId = null) => {
    try {
      await api.post("like", {
        discussion_id: replyId === null ? discussionId : null,
        comment_id: replyId !== null ? replyId : null,
      });
      invalidateDiscussions();
    } catch {
      toast.error("Failed to like. Please try again.");
    }
  };

  const openReportModal = (discussionId, commentId = null) => {
    const targetDiscussion = discussions.find((d) => d.id === discussionId);
    if (!targetDiscussion) return;
    if (commentId === null && targetDiscussion.reported_by_me) {
      toast.info("You have already reported this discussion");
      return;
    }
    if (commentId !== null) {
      const targetComment = targetDiscussion.comments?.find((c) => c.id === commentId);
      if (targetComment?.reported_by_me) {
        toast.info("You have already reported this comment");
        return;
      }
    }
    setReportType({ discussionId, commentId });
    setReportReason("");
    setCustomReportReason("");
    setShowReportModal(true);
  };

  const handleReport = async () => {
    if (!reportReason) {
      toast.error("Please select a reason");
      return;
    }
    if (reportReason === "Other" && !customReportReason.trim()) {
      toast.error("Please provide details");
      return;
    }
    setReportSubmitting(true);
    try {
      const payload =
        reportType.commentId === null
          ? { discussion_id: reportType.discussionId, reason: reportReason === "Other" ? customReportReason.trim() : reportReason }
          : { comment_id: reportType.commentId, discussion_id: reportType.discussionId, reason: reportReason === "Other" ? customReportReason.trim() : reportReason };
      await api.post("report", payload);
      setShowReportModal(false);
      invalidateDiscussions();
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to report. Please try again.");
    } finally {
      setReportSubmitting(false);
    }
  };

  const classes = getTemplateClasses(templateStyle);

  return (
    <section className={classes.container}>
      <Modal
        activeModal={showReportModal}
        onClose={() => setShowReportModal(false)}
        title="Report Content"
        centered
        footerContent={
          <div className="flex justify-end space-x-3">
            <Button text="Cancel" className="border border-gray-300" onClick={() => setShowReportModal(false)} />
            <Button text={reportSubmitting ? "Submitting..." : "Submit"} onClick={handleReport} disabled={reportSubmitting || !reportReason} />
          </div>
        }
      >
        <div className="space-y-3 p-5">
          {["Inappropriate language", "Harassment or bullying", "Spam or misleading", "Off-topic content", "Other"].map((reason) => (
            <label key={reason} className="flex items-center space-x-2">
              <input type="radio" name="report-reason" value={reason} checked={reportReason === reason} onChange={() => setReportReason(reason)} />
              <span>{reason}</span>
            </label>
          ))}
          {reportReason === "Other" && (
            <Textarea value={customReportReason} onChange={(e) => setCustomReportReason(e.target.value)} placeholder="Provide details..." row={4} className="w-full" />
          )}
        </div>
      </Modal>

      <div className={classes.header}>
        <h2 className="text-2xl font-bold text-gray-800">Course Discussions</h2>
        <p className="text-gray-600 mt-1">Ask questions, share insights, and connect with fellow students</p>
      </div>

      <div className={classes.tabs}>
        {["recent", "popular", "unanswered"].map((tab) => (
          <button
            key={tab}
            className={`px-4 py-2 font-medium text-sm transition-colors ${activeTab === tab ? classes.activeTab : classes.inactiveTab}`}
            onClick={() => setActiveTab(tab)}
          >
            <div className="flex items-center gap-2">
              <Icon icon={tab === "recent" ? "mdi:clock-outline" : tab === "popular" ? "mdi:trending-up" : "mdi:help-circle-outline"} className="h-4 w-4" />
              <span>{tab.charAt(0).toUpperCase() + tab.slice(1)}</span>
            </div>
          </button>
        ))}
      </div>

      <div className="mb-8">
        <div className="mb-4">
          <div className="flex items-start space-x-3">
            <img src={user?.avatar ? ASSET_URL + user.avatar : avatar} alt={user?.name || "User"} className="h-10 w-10 rounded-full object-cover" />
            <div className="flex-1">
              <div className="flex items-center">
                <h4 className="font-medium text-gray-800">{user?.name || "You"}</h4>
                <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">{user?.role || "Student"}</span>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-3 mb-4">
          <CustomInput label="Discussion Title" name="discussion_title" placeholder="Enter a title" value={discussionTitle} onChange={(e) => setDiscussionTitle(e.target.value)} className="w-full" />
        </div>
        <div className={classes.editorContainer}>
          <ReactQuill
            value={editorContent}
            onChange={setEditorContent}
            placeholder="Share your thoughts or questions..."
            className="bg-white rounded-lg"
            modules={{
              toolbar: [
                ["bold", "italic", "underline", "strike"],
                [{ list: "ordered" }, { list: "bullet" }],
                ["link", "image"],
                ["clean"],
              ],
            }}
          />
        </div>
        <div className="mt-3 flex justify-end">
          <Button text={submitting ? "Posting..." : "Post Discussion"} icon="mdi:send" onClick={handlePostSubmit} className={`${classes.postButton} rounded-md px-6`} disabled={!editorContent.trim() || !discussionTitle.trim() || submitting} />
        </div>
      </div>

      {loading ? (
        <div className="flex flex-col items-center justify-center py-16">
          <div className="relative w-20 h-20">
            <div className="absolute top-0 left-0 w-full h-full border-4 border-blue-200 rounded-full animate-ping opacity-75"></div>
            <div className="relative w-full h-full border-4 border-t-blue-600 border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
          </div>
          <p className="mt-4 text-gray-600 font-medium">Loading discussions...</p>
        </div>
      ) : filteredDiscussions().length === 0 ? (
        <div className={classes.emptyState}>
          <Icon icon="mdi:forum-outline" className="mx-auto h-20 w-20 text-gray-300" />
          <h3 className="mt-4 text-xl font-medium text-gray-800">No discussions yet</h3>
          <p className="mt-2 text-gray-500 max-w-md mx-auto">Be the first to start a discussion!</p>
        </div>
      ) : (
        <div className="space-y-6">
          {filteredDiscussions().map((discussion) => (
            <DiscussionPost key={discussion.id} discussion={discussion} onLike={handleLike} onReply={handleReply} onReport={openReportModal} formatDate={formatDate} templateStyle={templateStyle} />
          ))}
        </div>
      )}
    </section>
  );
};

const DiscussionPost = ({ discussion, onLike, onReply, onReport, formatDate, templateStyle }) => {
  const [replyContent, setReplyContent] = useState("");
  const [isReplying, setIsReplying] = useState(false);
  const [showReplies, setShowReplies] = useState(true);
  const { user } = useSelector((state) => state.auth);
  const isLiked = discussion.liked_by_me;

  const classes = getTemplateClasses(templateStyle);

  const handleSubmitReply = () => {
    if (!replyContent.trim()) return;
    onReply(discussion.id, replyContent);
    setReplyContent("");
    setIsReplying(false);
  };

  return (
    <Card className={classes.card}>
      <div className="flex items-start space-x-3">
        <img src={discussion.user.avatar ? ASSET_URL + discussion.user.avatar : avatar} alt={discussion.user.name} className="h-10 w-10 rounded-full object-cover" />
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h4 className="font-medium text-gray-800">{discussion.user.name}</h4>
              <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">{discussion.user.role || "User"}</span>
              <span className="ml-2 text-xs text-gray-500">• {formatDate(discussion.created_at)}</span>
            </div>
            <button className="text-gray-400 hover:text-red-500" onClick={() => onReport(discussion.id)}>
              <Icon icon={discussion.reported_by_me ? "mdi:flag" : "mdi:flag-outline"} className="h-5 w-5" />
            </button>
          </div>
          <div className="mt-2 text-gray-700" dangerouslySetInnerHTML={{ __html: discussion.content }} />
          <div className="mt-4 flex items-center space-x-4">
            <button className={`flex items-center space-x-1 ${classes.likeButton}`} onClick={() => onLike(discussion.id)}>
              <Icon icon={isLiked ? "mdi:heart" : "mdi:heart-outline"} className="h-5 w-5" />
              <span>{discussion.likes_count}</span>
            </button>
            <button className={`flex items-center space-x-1.5 ${classes.replyButton}`} onClick={() => setIsReplying(!isReplying)}>
              <Icon icon="mdi:message-reply-outline" className="h-5 w-5" />
              <span>Reply</span>
            </button>
            {discussion.comments.length > 0 && (
              <button className={`flex items-center space-x-1.5 ${classes.toggleButton}`} onClick={() => setShowReplies(!showReplies)}>
                <Icon icon={showReplies ? "mdi:chevron-up" : "mdi:chevron-down"} className="h-5 w-5" />
                <span>{discussion.comments.length} {discussion.comments.length === 1 ? "reply" : "replies"}</span>
              </button>
            )}
          </div>

          {isReplying && (
            <div className="mt-4 space-y-3">
              <Textarea value={replyContent} onChange={(e) => setReplyContent(e.target.value)} placeholder="Write your reply..." row={3} className="w-full" />
              <div className="flex justify-end space-x-2">
                <Button text="Cancel" onClick={() => { setIsReplying(false); setReplyContent(""); }} className="border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 transition-colors" />
                <Button text="Submit Reply" onClick={handleSubmitReply} className="bg-primary-600 text-white px-4 rounded-md" disabled={!replyContent.trim()} />
              </div>
            </div>
          )}

          {showReplies && discussion.comments.length > 0 && (
            <div className="mt-4 ml-6 space-y-4 border-l pl-4 border-gray-200">
              {discussion.comments.map((reply) => (
                <div key={reply.id} className={classes.replyContainer}>
                  <div className="flex items-start space-x-3">
                    <img src={reply.user.avatar ? ASSET_URL + reply.user.avatar : avatar} alt={reply.user.name} className="h-8 w-8 rounded-full object-cover" />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <h5 className="font-medium text-gray-800">{reply.user.name}</h5>
                          <span className="ml-2 text-xs text-gray-500">• {formatDate(reply.created_at)}</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <button className={classes.likeButton} onClick={() => onLike(discussion.id, reply.id)}>
                            <Icon icon={reply.liked_by_me ? "mdi:heart" : "mdi:heart-outline"} className="h-4 w-4" />
                            <span className="text-xs">{reply.likes_count}</span>
                          </button>
                          <button className="text-gray-400 hover:text-red-500" onClick={() => onReport(discussion.id, reply.id)}>
                            <Icon icon={reply.reported_by_me ? "mdi:flag" : "mdi:flag-outline"} className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      <p className="text-gray-700 mt-1">{reply.content}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

const getTemplateClasses = (templateStyle) => {
  if (templateStyle === "template-two") {
    return {
      container: "bg-white rounded-xl shadow-sm p-6",
      header: "mb-6",
      tabs: "flex space-x-1 border-b border-gray-200 mb-6",
      activeTab: "text-sky-600 border-b-2 border-sky-600",
      inactiveTab: "text-gray-500 hover:text-gray-700",
      postButton: "bg-gradient-to-r from-sky-600 to-blue-700 text-white",
      card: "bg-white hover:bg-gray-50",
      editorContainer: "border rounded-lg shadow-sm overflow-hidden",
      emptyState: "text-center py-16 bg-gray-50 rounded-xl border border-gray-200",
      replyContainer: "bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors",
      likeButton: "text-gray-500 hover:text-red-500",
      replyButton: "text-gray-500 hover:text-sky-600",
      toggleButton: "text-gray-500 hover:text-sky-600",
    };
  }
  return {
    container: "bg-white rounded-lg shadow-lg p-8",
    header: "mb-8 border-b border-gray-200 pb-4",
    tabs: "flex space-x-4 mb-8 bg-gray-50 p-2 rounded-lg",
    activeTab: "text-primary-600 bg-white shadow-md rounded-md font-semibold",
    inactiveTab: "text-gray-600 hover:bg-white/60 hover:text-primary-500",
    postButton: "bg-gradient-to-r from-primary-600 to-primary-500 text-white hover:from-primary-700 hover:to-primary-600",
    card: "bg-white hover:bg-gray-50/80 rounded-lg shadow-md p-6 border border-gray-100 transition-all duration-200 hover:shadow-lg",
    editorContainer: "border-2 border-gray-200 rounded-lg shadow-md overflow-hidden focus-within:border-primary-400 transition-all duration-200",
    emptyState: "text-center py-16 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200 shadow-inner",
    replyContainer: "bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors",
    likeButton: "text-gray-500 hover:text-red-500",
    replyButton: "text-gray-500 hover:text-primary-600",
    toggleButton: "text-gray-500 hover:text-primary-600",
  };
};

export default DiscussionForum;
