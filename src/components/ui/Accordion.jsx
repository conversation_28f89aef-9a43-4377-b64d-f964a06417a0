import { useState } from "react";
import Icon from "@/components/ui/Icon";

const Accordion = ({ title, content, isOpen = false }) => {
  const [open, setOpen] = useState(isOpen);

  const toggleAccordion = () => setOpen(!open);

  return (
    <div className="space-y-5">
      <div
        className={`accordion shadow-md dark:shadow-none rounded-md ${
          open
            ? "bg-slate-50 dark:bg-slate-700 dark:bg-opacity-60 rounded-t-md"
            : "bg-slate-50 dark:bg-slate-700 rounded-md"
        }`}
      >
        <div
          className="flex justify-between cursor-pointer transition duration-150 font-medium w-full text-start text-base text-slate-600 dark:text-slate-300 px-6 py-4"
          onClick={toggleAccordion}
        >
          <span
            className={`${
              open
                ? "text-gray-600 text-md"
                : "text-primary-500 text-xl"
            }`}
          >
            {title}
          </span>
          <span
            className={`text-slate-900 dark:text-white text-[22px] transition-transform duration-300 h-5 ${
              open ? "rotate-180 transform" : ""
            }`}
          >
            <Icon icon="heroicons-outline:chevron-down" />
          </span>
        </div>

        <div
          className={`transition-all duration-500 ease-in-out overflow-hidden ${
            open ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0"
          }`}
          style={{
            transition: "max-height 0.5s ease, opacity 0.3s ease",
          }}
        >
          <div className="text-sm text-slate-600 font-normal bg-white dark:bg-slate-900 dark:text-slate-300 rounded-b-md px-4 py-4 pt-2 text-lg">
            <span className="text-sky-600 font-semibold">Answer: </span>
            {content}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Accordion;
