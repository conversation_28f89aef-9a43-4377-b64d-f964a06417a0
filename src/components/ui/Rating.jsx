import { Icon } from "@iconify/react";

const Rating = ({ rating }) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  return (
    <div className="flex items-center">
      {Array.from({ length: fullStars }).map((_, index) => (
        <Icon
          key={`full-${index}`}
          icon="ic:round-star"
          className="text-yellow-300 text-lg"
        />
      ))}
      {hasHalfStar && (
        <Icon icon="ic:round-star-half" className="text-yellow-300 text-lg" />
      )}
      {Array.from({ length: emptyStars }).map((_, index) => (
        <Icon
          key={`empty-${index}`}
          icon="ic:round-star-outline"
          className="text-yellow-300 text-lg"
        />
      ))}
      <span className="ml-2 text-md text-gray-700 font-semibold">
        {rating?.toFixed(1)} {/* Show the rating with one decimal place */}
      </span>
    </div>
  );
};

export default Rating;

