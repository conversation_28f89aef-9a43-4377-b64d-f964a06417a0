import { useState } from "react";
import Icon from "@/components/ui/Icon";
import moduleIcon from "@/assets/images/svg/module icon.svg";
import clockIcon from "@/assets/images/all-img/clock.png";
import mediaIcon from "@/assets/images/all-img/media.png";
import examIcon from "@/assets/images/all-img/exam.png";
import documentIcon from "@/assets/images/all-img/document.png";
import { Link } from "react-router-dom";
import QuizModalContent from "../../pages/course/QuizModalContent";

const ModuleAccordion = ({ module, isOpen = false, selectedItem }) => {
  const [open, setOpen] = useState(isOpen);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [quizData, setQuizData] = useState({});
  // console.log(module);

  function formatDuration(durationInMinutes) {
    // Calculate hours, minutes, and seconds
    const hours = Math.floor(durationInMinutes / 60);
    const minutes = Math.floor(durationInMinutes % 60);
    const seconds = Math.floor((durationInMinutes * 60) % 60);

    // Return formatted time
    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }

  const openQuiz = async (content) => {
    // if (isAuth) {
    //   try {
    //     setLoading(true);
    //     const response = await api.post("purchase-course", { course_id: id });
    //     console.log(response.data);
    //   } catch (error) {
    //     console.error(error);
    //   } finally {
    //     setLoading(false);
    //   }
    // } else {
    setQuizData(content);
    setShowLoginModal(true);
    // }
  };

  return (
    <div className="mb-1 bg-white">
      {/* {content?.course_outlines?.map((data, idx) => ( */}
      <div
        className={`accordion shadow-md dark:shadow-none bg-white rounded-sm border ${
          open ? "rounded-t-md" : "rounded-md"
        }`}
      >
        <div onClick={() => setOpen(!open)} className="cursor-pointer">
          <div className="flex justify-between transition duration-150 font-medium w-full text-start text-base text-slate-600 dark:text-slate-300 px-8 max-sm:px-4 py-4 pb-1">
            <div className="flex gap-3">
              <img src={moduleIcon} className="" alt="" />
              <span className="font-semibold text-primary-500 text-md">
                {module?.name}
              </span>
            </div>
            <span
              className={`text-slate-900 dark:text-white text-[22px] transition-transform duration-300 h-5 ${
                open ? "rotate-180 transform" : ""
              }`}
            >
              <Icon icon="heroicons-outline:chevron-down" className="text-[18px] text-gray-600" />
            </span>
          </div>

          {[
            module?.scripts_number,
            module?.videos_number,
            module?.quizs_number,
          ].filter((value) => value == 0).length > 1 ? (
            <div className="pb-2"></div>
          ) : (
            <div className="px-4 max-sm:text-xs max-sm:px-2 lg:pl-16 py-2 pt-0 flex items-center gap-4 max-sm:gap-2">
              {module?.scripts_number > 0 && (
                <>
                  <span className="flex gap-2 items-center pr-4 text-sm">
                    <Icon
                      icon="fluent:document-text-32-filled"
                      className="text-xl text-indigo-600"
                    />
                    {module?.scripts_number} Scripts
                  </span>
                  <div className="h-4 w-0.5 bg-gray-300 my-4"></div>
                </>
              )}

              {module?.videos_number > 0 && (
                <>
                  <span className="flex gap-2 items-center pr-4 text-sm">
                    <Icon
                      icon="material-symbols:hangout-video-rounded"
                      className="text-xl text-red-500"
                    />
                    {module?.videos_number} Videos
                  </span>
                  <div className="h-4 w-0.5 bg-gray-300 my-4"></div>
                </>
              )}

              {module?.quizs_number > 0 && (
                <>
                  <span className="flex gap-2 items-center pr-4 text-sm">
                    <Icon
                      icon="fluent:document-text-32-filled"
                      className="text-xl text-green-500"
                    />
                    {module?.quizs_number} Quizes
                  </span>
                </>
              )}

              {/* <div className="h-4 w-0.5 bg-gray-300 my-4"></div>

            <span className="flex gap-2 items-center">
              <img src={clockIcon} className="" alt="" />
              {module?.duration ? formatDuration(module?.duration) : "0min"}
            </span> */}
            </div>
          )}
        </div>

        <div
          className={`overflow-hidden transition-all duration-500 ease-in-out ${
            open ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0"
          }`}
          style={{
            transitionProperty: "max-height, opacity",
          }}
        >
          {open &&
            (module?.contents?.length > 0 ? module?.contents?.map(
              (content, idx) =>
                (content?.type === "video" && (
                  <Link
                    key={idx}
                    to={`/content-details/${content?.id}`}
                    className={`text-sm text-slate-700 font-normal rounded-b-md px-4 py-4 flex items-start ${
                      content?.id == selectedItem
                        ? "bg-white"
                        : "bg-slate-50 mb-1"
                    } justify-between gap-4 cursor-pointer border-sky-300`}
                  >
                    {" "}
                    {/* {console.log(content)} */}
                    <div className="flex gap-4">
                      <Icon
                        icon="material-symbols:hangout-video-rounded"
                        className="text-xl text-red-500"
                      />
                      <div>
                        <p className="text-md mb-2">{content?.title}</p>
                        {/* <span className="text-gray-400">
                          1hr 45min | 80 points
                        </span> */}
                      </div>
                    </div>
                    {idx > 0 &&
                      module?.contents[idx - 1].is_completed == false && (
                        <Icon
                          icon="lineicons:locked-2"
                          className="text-xl text-gray-600"
                        />
                      )}
                    {content?.is_completed == true && (
                      <Icon
                        icon="lineicons:checkmark-circle"
                        className="text-xl text-green-600"
                      />
                    )}
                  </Link>
                )) ||
                (content?.type === "quiz" && (
                  <div
                    key={idx}
                    onClick={() => openQuiz(content)}
                    className={`text-sm text-slate-700 font-normal rounded-b-md px-4 py-4 flex items-start ${
                      content?.id == selectedItem
                        ? "bg-white"
                        : "bg-slate-50 mb-1"
                    } justify-between gap-4 cursor-pointer border-sky-300`}
                  >
                    {" "}
                    <div className="flex gap-4">
                      <Icon
                        icon="fluent:document-text-32-filled"
                        className="text-xl text-green-500"
                      />
                      <div>
                        <p className="text-md mb-2">{content?.title}</p>
                        {/* <span className="text-gray-400">
                          1hr 45min | 80 points
                        </span> */}
                      </div>
                    </div>
                    {idx > 0 &&
                      module?.contents[idx - 1].is_completed == false &&
                      !content?.is_completed == true && (
                        <Icon
                          icon="lineicons:locked-2"
                          className="text-xl text-gray-600"
                        />
                      )}
                    {content?.is_completed == true && (
                      <Icon
                        icon="lineicons:checkmark-circle"
                        className="text-xl text-green-600"
                      />
                    )}
                  </div>
                )) ||
                (content?.type === "script" && (
                  <Link
                    key={idx}
                    to={`/content-details/${content?.id}`}
                    className={`text-sm text-slate-700 font-normal rounded-b-md px-4 py-4 flex items-start ${
                      content?.id == selectedItem
                        ? "bg-white"
                        : "bg-slate-50 mb-1"
                    } justify-between gap-4 cursor-pointer border-sky-300`}
                  >
                    {" "}
                    {/* {console.log(content)} */}
                    <div className="flex gap-4">
                      <Icon
                        icon="fluent:document-text-32-filled"
                        className="text-xl text-indigo-600"
                      />
                      <div>
                        <p className="text-md mb-2">{content?.title}</p>
                        {/* <span className="text-gray-400">
                          1hr 45min | 80 points
                        </span> */}
                      </div>
                    </div>
                    {idx > 0 &&
                      module?.contents[idx - 1].is_completed == false && (
                        <Icon
                          icon="lineicons:locked-2"
                          className="text-xl text-gray-600"
                        />
                      )}
                    {content?.is_completed == true && (
                      <Icon
                        icon="lineicons:checkmark-circle"
                        className="text-xl text-green-600"
                      />
                    )}
                  </Link>
                ))
            ): <p className="text-center text-sm py-3 border-t border-dashed">No Content Available</p>)}
        </div>
      </div>

      {showLoginModal && (
        <QuizModalContent
          data={quizData}
          activeModal={showLoginModal}
          onClose={() => setShowLoginModal(false)}
        />
      )}
      {/* ))} */}
    </div>
  );
};
export default ModuleAccordion;
