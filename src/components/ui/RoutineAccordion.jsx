import { useState } from "react";
import Icon from "@/components/ui/Icon";
import calendarIcon from "@/assets/images/svg/calender.svg";

const RoutineAccordion = ({
  title = "Course Routine",
  course,
  classSchedule,
  isOpen = false,
}) => {
  const [open, setOpen] = useState(isOpen);

  const formatTime = (time24) => {
    const [hours, minutes] = time24.split(":");
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? "PM" : "AM";
    const formattedHour = hour % 12 || 12; // convert 0 to 12 for 12 AM/PM

    return `${formattedHour}:${minutes} ${ampm}`;
  };

  const toggleAccordion = () => setOpen(!open);

  const printRoutine = () => {
    const printContent = document.getElementById("printableRoutine");
    const printWindow = window.open("", "_blank");
    printWindow.document.write(
      `<html><head><title>${course.title} </title></head><body>`
    );
    printWindow.document.write(printContent.innerHTML);
    printWindow.document.write("</body></html>");
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <div className="space-y-5">
      <div
        className={`accordion shadow-md dark:shadow-none rounded-md ${
          open
            ? "bg-slate-50 dark:bg-slate-700 dark:bg-slate-700 opacity-80 rounded-t-md"
            : "bg-slate-50 dark:bg-slate-700 rounded-md"
        }`}
      >
        <div
          className="flex justify-between cursor-pointer gap-2 transition duration-150 font-medium w-full text-start bg-sky-50 rounded-lg text-base text-slate-600 px-6 py-5"
          onClick={toggleAccordion}
        >
          <span className="text-primary-500 text-xl font-semibold">
            <img src={calendarIcon} alt="" /> {title}
          </span>
          <span
            className={`text-slate-900 dark:text-white text-[22px] transition-all duration-300 h-5 ${
              open ? "rotate-180 transform" : ""
            }`}
          >
            <Icon icon="heroicons-outline:chevron-down" />
          </span>
        </div>

        <div
          className={`overflow-hidden transition-all duration-500 ease-in-out ${
            open ? "max-h-screen opacity-100" : "max-h-0 opacity-0"
          }`}
        >
        <div className="text-sm text-slate-600 font-normal bg-white dark:bg-slate-900 dark:text-slate-300 rounded-b-md">
          <div className="overflow-x-auto">
            <div className="flex justify-end p-4">
              <button
                onClick={printRoutine}
                className="text-sm text-sky-600 px-4 py-2 rounded flex items-center gap-2 bg-sky-100 hover:bg-sky-200"
              >
                Download Routine
                <Icon icon="lucide:download" className="text-xl" />
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
              {classSchedule?.map((item, idx) => (
                <div key={idx} className="grid grid-cols-2 bg-gray-50 border border-gray-200 rounded-lg p-4 shadow hover:shadow-lg">
                  <p className="text-sm font-semibold text-gray-800">
                    {new Intl.DateTimeFormat("en-US", { weekday: "short", year: "numeric", month: "short", day: "numeric" }).format(
                      new Date(item?.schedule_datetime)
                    )}
                  </p>
                  <p className="text-sm text-gray-600">Time: {formatTime(item?.start_time)}</p>
                  <p className="text-sm text-gray-600">Duration: {item?.duration} mins</p>
                  <p className="text-sm text-gray-600">Mentor: {item?.mentor || "Unknown"}</p>
                  {/* <a href={item?.class_url} target="_blank" rel="noopener noreferrer" className="text-sky-600 hover:underline text-sm mt-2 inline-block">Join Class</a> */}
                </div>
              ))}
            </div>
          </div>
        </div>

        </div>
      </div>

      {/* Printable content */}
<div
  id="printableRoutine"
  className="hidden"
  style={{
    padding: "20px",
    fontFamily: "Arial, sans-serif",
    border: "1px solid #ccc",
    width: "100%",
  }}
>
  <div style={{ marginBottom: "20px", textAlign: "center" }}>
    <h2 style={{ margin: 0, fontSize: "20px", fontWeight: "bold" }}>
      Class List
    </h2>
  </div>
  <div
    style={{
      display: "grid",
      gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
      gap: "20px",
    }}
  >
    {classSchedule?.map((item, idx) => (
      <div
        key={idx}
        style={{
          border: "1px solid #ccc",
          borderRadius: "8px",
          padding: "10px",
          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
        }}
      >
        <p
          style={{
            fontSize: "14px",
            fontWeight: "bold",
            margin: "5px 0",
          }}
        >
          Date:{" "}
          {new Intl.DateTimeFormat("en-US", {
            weekday: "short",
            year: "numeric",
            month: "short",
            day: "numeric",
          }).format(new Date(item?.schedule_datetime))}
        </p>
        <p style={{ fontSize: "14px", margin: "5px 0" }}>
          Time: {formatTime(item?.start_time)}
        </p>
        <p style={{ fontSize: "14px", margin: "5px 0" }}>
          Duration: {item?.duration} mins
        </p>
        <p style={{ fontSize: "14px", margin: "5px 0" }}>
          Mentor: {item?.mentor || "Unknown"}
        </p>
        {/* <a
          href={item?.class_url}
          target="_blank"
          rel="noopener noreferrer"
          style={{
            fontSize: "14px",
            color: "#1E40AF",
            textDecoration: "underline",
          }}
        >
          Join Class
        </a> */}
      </div>
    ))}
  </div>
</div>

    </div>
  );
};

export default RoutineAccordion;

{
  /* Table added here */
}
{
  /* <table className="min-w-full mt-4 border-collapse">
                <thead>
                  <tr className="">
                    <th colSpan="2" className="border px-4 py-2 text-right">
                      <div className="w-full flex justify-end">
                        <div className="w-48">
                          <a
                            href="path/to/your/routine.pdf" // Replace with your PDF file path
                            download="Routine.pdf" // Filename for the downloaded PDF
                            className="font-normal text-sky-600 flex items-center justify-end gap-2 cursor-pointer"
                          >
                            Download Routine
                            <Icon icon="lucide:download" className="text-xl" />
                          </a>
                        </div>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {Array.from({ length: 5 }).map((_, index) => (
                    <tr key={index} className="hover:bg-gray-100">
                      <td className="border px-4 py-2">
                        Row {index + 1} Data 1
                      </td>
                      <td className="border px-4 py-2">
                        Row {index + 1} Data 2
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table> */
}
