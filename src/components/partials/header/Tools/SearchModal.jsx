import React, { Fragment, useState, useEffect, useRef } from "react";
import { Dialog, Transition, Combobox } from "@headlessui/react";
import Icon from "@/components/ui/Icon";
import api from "@/server/api";
import { useNavigate } from "react-router-dom";
import { ASSET_URL } from "@/config";
import { useSelector } from "react-redux";

const SearchModal = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const debounceTimeout = useRef(null);
  const navigate = useNavigate();
  const { organization } = useSelector((state) => state.commonSlice);

  // Function to close the modal
  function closeModal() {
    setIsOpen(false);
  }

  // Function to open the modal
  function openModal() {
    setIsOpen(true);
  }

  // Fetch search results from API
  const searchCourse = async (search) => {
    if (search.length < 3) return; // Skip API call for queries shorter than 3 characters
    setLoading(true);
    try {
      const res = await api.get(`course-list-web?organization_id=${organization?.id}&search=${search}`);
      const courseList = res?.data?.data?.data || [];
      setSearchResults(courseList);
    } catch (error) {
      console.error("Error fetching search results:", error);
    } finally {
      setLoading(false);
    }
  };

  // Handle input change with debounce
  const handleInputChange = (value) => {
    setQuery(value);
    // Clear the previous debounce timer
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }
    // Set a new debounce timer
    debounceTimeout.current = setTimeout(() => {
      if (value.trim().length >= 3) {
        searchCourse(value.trim());
      } else {
        setSearchResults([]); // Clear results if query is less than 3 characters
      }
    }, 300); // Adjust debounce delay as needed
  };

  // Clean up debounce timer on component unmount
  useEffect(() => {
    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
    };
  }, []);

  return (
    <>
      <div>
        <button
          className="flex items-center xl:text-sm text-lg xl:text-slate-400 text-slate-800 dark:text-slate-300 px-1 space-x-3 rtl:space-x-reverse"
          onClick={openModal}
        >
          <Icon className="text-white" icon="heroicons-outline:search" />
          <span className="xl:inline-block hidden">Search... </span>
        </button>
      </div>

      <Transition show={isOpen} as={Fragment}>
        <Dialog
          as="div"
          className="fixed inset-0 z-[9999] overflow-y-auto p-4 md:pt-[25vh] pt-20"
          onClose={closeModal} // Close modal on outside click
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-slate-900/60 backdrop-filter backdrop-blur-sm backdrop-brightness-10" />
          </Transition.Child>

          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 scale-95"
            enterTo="opacity-100 scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 scale-100"
            leaveTo="opacity-0 scale-95"
          >
            <Dialog.Panel>
              <Combobox>
                <div className="relative">
                  <div className="relative mx-auto max-w-xl rounded-md bg-white dark:bg-slate-800 shadow-2xl ring-1 ring-gray-500-500 dark:ring-light divide-y divide-gray-500-300 dark:divide-light">
                    <div className="flex bg-white dark:bg-slate-800 px-3 rounded-md py-3 items-center">
                      <div className="flex-0 text-slate-700 dark:text-slate-300 ltr:pr-2 rtl:pl-2 text-lg mr-4">
                        <Icon icon="heroicons-outline:search" />
                      </div>
                      <Combobox.Input
                        className="bg-transparent outline-none focus:outline-none border-none w-full flex-1 dark:placeholder:text-slate-300 dark:text-slate-200"
                        placeholder="Search..."
                        value={query} // Bind query state to input value
                        onChange={(e) => handleInputChange(e.target.value)} // Update query state on change
                        onKeyDown={(e) => {
                          if(e.key == "Enter"){
                            navigate(`/search?query=${e.target.value}`),
                            closeModal()
                          }
                        }}
                      />
                    </div>
                    <Transition
                      leave="transition ease-in duration-100"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >

                      <Combobox.Options className="max-h-40 overflow-y-auto text-sm py-2">
                        {loading && (
                          <div className="text-center py-2">
                            <p className="text-slate-500 text-base dark:text-white">Loading...</p>
                          </div>
                        )}
                        {!loading && searchResults.length === 0 && query.length >= 3 && (
                          <div className="text-center py-2">
                            <p className="text-slate-500 text-base dark:text-white">No result found</p>
                          </div>
                        )}
                        {!loading &&
                          searchResults.map((item) => (
                            <Combobox.Option key={item.id}>
                              <div
                                className={`px-4 text-[15px] font-normal capitalize py-2 cursor-pointer text-slate-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700`}
                                onClick={() => {
                                  navigate(`/course-details/${item.id}`);
                                  setIsOpen(false);
                                }}
                              >
                                <div className="flex gap-4">
                                  <img
                                  src={ASSET_URL + item?.thumbnail}
                                   className="w-auto h-12" alt="" />
                                  <span>{item.title}</span>
                                </div>
                              </div>
                            </Combobox.Option>
                          ))}
                      </Combobox.Options>
                    </Transition>
                  </div>
                </div>
              </Combobox>
            </Dialog.Panel>
          </Transition.Child>
        </Dialog>
      </Transition>
    </>
  );
};

export default SearchModal;
