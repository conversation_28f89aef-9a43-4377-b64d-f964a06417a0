import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import api from '@/server/api';
function PaymentSuccess() {
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function verifyPayment() {
      try {
        // Extract payment intent ID from URL query params
        const queryParams = new URLSearchParams(location.search);
        const paymentIntentId = queryParams.get('payment_intent');
        const paymentStatus = queryParams.get('redirect_status');
        
        if (!paymentIntentId) {
          throw new Error('No payment intent ID found in the URL. Please contact support if you believe this is an error.');
        }
        
        if (paymentStatus === 'failed') {
          throw new Error('The payment process was not completed successfully. Please try again.');
        }

        // Verify payment with your backend
        const response = await api.post('verify-payment', {
          paymentIntentId
        });

        if (response.data.success || response.data.status === true) {
          setSuccess(true);
          // Store payment info in localStorage for reference
          try {
            const paymentData = {
              id: paymentIntentId,
              timestamp: new Date().toISOString(),
              status: 'success'
            };
            localStorage.setItem('last_payment_info', JSON.stringify(paymentData));
          } catch (e) {
            // Ignore storage errors
            console.log('Could not store payment info');
          }
        } else {
          setError(response.data.message || 'Payment verification failed');
        }
      } catch (err) {
        setError(err.message || 'Failed to verify payment');
      } finally {
        setLoading(false);
      }
    }

    verifyPayment();
  }, [location]);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-blue-500"></div>
        <p className="mt-4 text-lg">Verifying your payment...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4">
        <div className="bg-red-100 p-6 rounded-lg max-w-md text-center">
          <Icon icon="heroicons:exclamation-circle" className="text-red-500 text-5xl mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-red-700 mb-2">Payment Failed</h2>
          <p className="text-red-600 mb-6">{error}</p>
          <button
            onClick={() => navigate('/my-payments')}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg"
          >
            Go to My Payments
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      <div className="bg-green-100 p-6 rounded-lg max-w-md text-center">
        <Icon icon="heroicons:check-circle" className="text-green-500 text-5xl mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-green-700 mb-2">Payment Successful!</h2>
        <p className="text-green-600 mb-6">Thank you for your payment. Your course has been enrolled.</p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => navigate('/my-courses')}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg"
          >
            View My Courses
          </button>
          <button
            onClick={() => navigate('/')}
            className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-6 rounded-lg"
          >
            Back to Home
          </button>
        </div>
      </div>
    </div>
  );
}

export default PaymentSuccess;