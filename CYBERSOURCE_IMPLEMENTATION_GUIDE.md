# CyberSource Flex API - Complete Implementation Guide

## Overview
This guide provides a complete implementation of CyberSource Flex API for secure payment processing in your Laravel + React application. The implementation includes both frontend (React) and backend (Laravel) components.

## Quick Start

### 1. Test the Frontend (Available Now)
Navigate to: `http://localhost:3000/payment-demo`

This demo page allows you to:
- Configure payment amounts and currency
- Set customer information
- Test the payment form UI
- See test card numbers for CyberSource

### 2. Backend Setup Required
Follow the Laravel implementation steps below to enable full functionality.

## Frontend Implementation ✅ COMPLETED

The React frontend has been implemented with the following features:

### Components Created/Updated:
1. **CyberSourcePaymentPage.jsx** - Main payment form with Flex microforms
2. **PaymentSuccess.jsx** - Payment success page
3. **Route.jsx** - Updated to include new routes

### Key Features:
- Secure tokenization using CyberSource Flex microforms
- Real-time form validation
- Loading states and error handling
- Responsive design with Tailwind CSS
- Automatic redirection to success page

## Backend Implementation Required

You need to implement the following Laravel endpoints:

### 1. Generate Capture Context Endpoint

**Route**: `POST /api/website/cybersource/capture-context`

**Controller Method**:
```php
<?php

namespace App\Http\Controllers\Api\Website;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CyberSourceController extends Controller
{
    private $merchantId = 'mehedi54321_1750580586';
    private $apiKey = 'c95a54f4-12bd-4703-a7e0-a1b64c0665fb';
    private $baseUrl = 'https://apitest.cybersource.com';
    
    public function generateCaptureContext(Request $request)
    {
        try {
            $targetOrigin = $request->input('target_origin', 'http://localhost:3000');
            
            $payload = [
                'targetOrigins' => [$targetOrigin],
                'allowedCardNetworks' => ['VISA', 'MASTERCARD', 'AMEX', 'DISCOVER'],
                'allowedPaymentTypes' => ['PANENTRY'],
                'country' => 'US',
                'locale' => 'en_US'
            ];

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'v-c-merchant-id' => $this->merchantId,
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->post($this->baseUrl . '/flex/v2/sessions', $payload);

            if ($response->successful()) {
                $data = $response->json();
                
                return response()->json([
                    'success' => true,
                    'message' => 'Capture context generated successfully',
                    'data' => [
                        'captureContext' => $data['keyId'] ?? $data['sessionId'] ?? $data,
                        'flexSession' => $data['keyId'] ?? $data['sessionId'] ?? $data,
                        'sessionId' => $data['keyId'] ?? $data['sessionId'] ?? $data,
                        'fullResponse' => $data
                    ]
                ]);
            } else {
                throw new \Exception('CyberSource API error: ' . $response->body());
            }
            
        } catch (\Exception $e) {
            Log::error('CyberSource Flex Error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate capture context',
                'error' => $e->getMessage(),
                'status_code' => 400
            ], 400);
        }
    }
}
```

### 2. Process Payment Endpoint

**Route**: `POST /api/website/cybersource/process-payment`

**Controller Method**:
```php
public function processPayment(Request $request)
{
    try {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|string|size:3',
            'cardToken' => 'required|string',
            'expirationToken' => 'required|string',
            'securityToken' => 'required|string',
            'customerInfo' => 'required|array',
            'customerInfo.firstName' => 'required|string',
            'customerInfo.lastName' => 'required|string',
            'customerInfo.email' => 'required|email',
        ]);

        $payload = [
            'clientReferenceInformation' => [
                'code' => 'PAYMENT_' . time() . '_' . rand(1000, 9999)
            ],
            'paymentInformation' => [
                'fluidData' => [
                    'value' => $request->cardToken
                ],
                'card' => [
                    'expirationMonth' => $this->extractMonthFromToken($request->expirationToken),
                    'expirationYear' => $this->extractYearFromToken($request->expirationToken),
                    'securityCode' => $request->securityToken
                ]
            ],
            'orderInformation' => [
                'amountDetails' => [
                    'totalAmount' => $request->amount,
                    'currency' => strtoupper($request->currency)
                ],
                'billTo' => [
                    'firstName' => $request->customerInfo['firstName'],
                    'lastName' => $request->customerInfo['lastName'],
                    'address1' => $request->customerInfo['address1'],
                    'locality' => $request->customerInfo['locality'],
                    'administrativeArea' => $request->customerInfo['administrativeArea'],
                    'postalCode' => $request->customerInfo['postalCode'],
                    'country' => $request->customerInfo['country'],
                    'email' => $request->customerInfo['email'],
                    'phoneNumber' => $request->customerInfo['phoneNumber']
                ]
            ]
        ];

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'v-c-merchant-id' => $this->merchantId,
            'Authorization' => 'Bearer ' . $this->apiKey,
        ])->post($this->baseUrl . '/pts/v2/payments', $payload);

        if ($response->successful()) {
            $data = $response->json();
            
            // Store payment record in database
            $payment = $this->storePaymentRecord($request, $data);
            
            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully',
                'transactionId' => $data['id'] ?? $payment->id,
                'data' => $data
            ]);
        } else {
            throw new \Exception('Payment processing failed: ' . $response->body());
        }
        
    } catch (\Exception $e) {
        Log::error('CyberSource Payment Error: ' . $e->getMessage());
        
        return response()->json([
            'success' => false,
            'message' => 'Payment processing failed',
            'error' => $e->getMessage()
        ], 400);
    }
}

private function extractMonthFromToken($token)
{
    // Implement token parsing logic based on CyberSource documentation
    // This is a placeholder - you'll need to implement actual token parsing
    return '12';
}

private function extractYearFromToken($token)
{
    // Implement token parsing logic based on CyberSource documentation
    // This is a placeholder - you'll need to implement actual token parsing
    return '2025';
}

private function storePaymentRecord($request, $cybersourceResponse)
{
    // Store payment record in your database
    // Return the created payment record
    return (object) ['id' => uniqid()];
}
```

### 3. Routes Configuration

Add to your `routes/api.php`:

```php
Route::prefix('website')->group(function () {
    Route::prefix('cybersource')->group(function () {
        Route::post('capture-context', [CyberSourceController::class, 'generateCaptureContext']);
        Route::post('process-payment', [CyberSourceController::class, 'processPayment']);
    });
});
```

## Environment Configuration

Add to your `.env` file:

```env
CYBERSOURCE_MERCHANT_ID=mehedi54321_1750580586
CYBERSOURCE_API_KEY=c95a54f4-12bd-4703-a7e0-a1b64c0665fb
CYBERSOURCE_BASE_URL=https://apitest.cybersource.com
CYBERSOURCE_ENVIRONMENT=test
```

## Database Migration

Create a payments table:

```php
Schema::create('payments', function (Blueprint $table) {
    $table->id();
    $table->string('transaction_id')->unique();
    $table->string('cybersource_id')->nullable();
    $table->decimal('amount', 10, 2);
    $table->string('currency', 3);
    $table->string('status');
    $table->json('customer_info');
    $table->json('cybersource_response')->nullable();
    $table->timestamps();
});
```

## Testing

### Frontend Testing:
1. Navigate to `/cybersourse-payment`
2. Fill in payment details
3. Submit payment
4. Verify redirection to success page

### Backend Testing:
```bash
# Test capture context generation
curl -X POST http://your-domain.com/api/website/cybersource/capture-context \
  -H "Content-Type: application/json" \
  -d '{"target_origin": "http://localhost:3000"}'

# Test payment processing (after getting tokens from frontend)
curl -X POST http://your-domain.com/api/website/cybersource/process-payment \
  -H "Content-Type: application/json" \
  -d '{
    "amount": "100.00",
    "currency": "USD",
    "cardToken": "token_from_frontend",
    "expirationToken": "exp_token_from_frontend",
    "securityToken": "cvv_token_from_frontend",
    "customerInfo": {
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "address1": "123 Main St",
      "locality": "San Francisco",
      "administrativeArea": "CA",
      "postalCode": "94105",
      "country": "US",
      "phoneNumber": "4155551234"
    }
  }'
```

## Security Considerations

1. **HTTPS Only**: Always use HTTPS in production
2. **Environment Variables**: Move all credentials to environment variables
3. **Input Validation**: Validate all input data
4. **Error Handling**: Don't expose sensitive error details to frontend
5. **Logging**: Log all transactions for audit purposes

## Next Steps

1. Implement the Laravel backend endpoints
2. Test the complete flow
3. Move to production environment
4. Implement webhook handling for payment status updates
5. Add comprehensive error handling and logging

## Support

For CyberSource-specific issues, refer to:
- [CyberSource Developer Documentation](https://developer.cybersource.com/)
- [CyberSource Flex API Guide](https://developer.cybersource.com/docs/cybs/en-us/digital-accept-flex/developer/all/rest/digital-accept-flex.html)
